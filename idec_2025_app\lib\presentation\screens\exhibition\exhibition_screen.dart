import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class ExhibitionScreen extends StatefulWidget {
  const ExhibitionScreen({super.key});

  @override
  State<ExhibitionScreen> createState() => _ExhibitionScreenState();
}

class _ExhibitionScreenState extends State<ExhibitionScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  String _selectedCategory = 'الكل';
  String _selectedHall = 'الكل';
  
  final List<String> _categories = [
    'الكل', 'أجهزة طبية', 'مواد سنية', 'تقنيات حديثة', 'برمجيات', 'تعليم'
  ];
  final List<String> _halls = ['الكل', 'القاعة A', 'القاعة B', 'القاعة C'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'المعرض المصاحب',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (0.1 * _animationController.value),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/exhibition_bg.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 60),
                                
                                // Exhibition Icon
                                Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.store,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                                
                                const SizedBox(height: AppSizes.paddingM),
                                
                                // Stats
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    _buildStatItem('50+', 'عارض'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('200+', 'منتج'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('3', 'قاعات'),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                tabs: const [
                  Tab(text: 'العارضون'),
                  Tab(text: 'المنتجات'),
                  Tab(text: 'خريطة المعرض'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Filters
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Row(
                children: [
                  Expanded(
                    child: _buildFilterDropdown(
                      'الفئة',
                      _selectedCategory,
                      _categories,
                      (value) => setState(() => _selectedCategory = value!),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildFilterDropdown(
                      'القاعة',
                      _selectedHall,
                      _halls,
                      (value) => setState(() => _selectedHall = value!),
                    ),
                  ),
                  IconButton(
                    onPressed: _showExhibitionMap,
                    icon: const Icon(
                      Icons.map,
                      color: Color(AppColors.primaryRed),
                    ),
                    tooltip: 'خريطة المعرض',
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildExhibitorsTab(),
                    _buildProductsTab(),
                    _buildMapTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showQRScanner,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.qr_code_scanner),
        label: const Text('مسح QR'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Text(label),
          isExpanded: true,
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildExhibitorsTab() {
    final exhibitors = _getExhibitors();
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 0.8,
      ),
      itemCount: exhibitors.length,
      itemBuilder: (context, index) {
        final exhibitor = exhibitors[index];
        return _buildExhibitorCard(exhibitor);
      },
    );
  }

  Widget _buildExhibitorCard(Map<String, dynamic> exhibitor) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          gradient: LinearGradient(
            colors: [
              const Color(AppColors.primaryWhite),
              const Color(AppColors.offWhite),
            ],
          ),
        ),
        child: Column(
          children: [
            // Company Logo
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                      const Color(AppColors.primaryBlack).withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppSizes.radiusL),
                    topRight: Radius.circular(AppSizes.radiusL),
                  ),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryWhite),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.business,
                          size: 30,
                          color: Color(AppColors.primaryRed),
                        ),
                      ),
                    ),
                    
                    // Booth Number
                    Positioned(
                      top: AppSizes.paddingS,
                      right: AppSizes.paddingS,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingS,
                          vertical: AppSizes.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryRed),
                          borderRadius: BorderRadius.circular(AppSizes.radiusS),
                        ),
                        child: Text(
                          exhibitor['boothNumber'],
                          style: const TextStyle(
                            color: Color(AppColors.primaryWhite),
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Company Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Company Name
                    Text(
                      exhibitor['name'],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Color(AppColors.primaryBlack),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: AppSizes.paddingXS),
                    
                    // Category
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: AppSizes.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                      child: Text(
                        exhibitor['category'],
                        style: const TextStyle(
                          color: Color(AppColors.primaryBlack),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingS),
                    
                    // Description
                    Text(
                      exhibitor['description'],
                      style: TextStyle(
                        fontSize: 12,
                        color: const Color(AppColors.grey).withValues(alpha: 0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Action Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _showExhibitorDetails(exhibitor),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppColors.primaryRed),
                          foregroundColor: const Color(AppColors.primaryWhite),
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingS),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          elevation: 2,
                        ),
                        child: const Text(
                          'عرض التفاصيل',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsTab() {
    return const Center(
      child: Text(
        'المنتجات\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildMapTab() {
    return const Center(
      child: Text(
        'خريطة المعرض\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  void _showExhibitionMap() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خريطة المعرض'),
        content: Container(
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            color: const Color(AppColors.lightGrey),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.map,
                  size: 60,
                  color: Color(AppColors.primaryRed),
                ),
                SizedBox(height: AppSizes.paddingM),
                Text(
                  'خريطة تفاعلية\nقريباً...',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showQRScanner() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح ماسح QR للمعرض...'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _showExhibitorDetails(Map<String, dynamic> exhibitor) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Company Header
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(AppColors.primaryRed),
                                const Color(AppColors.darkRed),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: const Icon(
                            Icons.business,
                            size: 40,
                            color: Color(AppColors.primaryWhite),
                          ),
                        ),
                        
                        const SizedBox(width: AppSizes.paddingM),
                        
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                exhibitor['name'],
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                exhibitor['category'],
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: const Color(AppColors.grey),
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: AppSizes.paddingXS),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingS,
                                  vertical: AppSizes.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                ),
                                child: Text(
                                  'جناح ${exhibitor['boothNumber']}',
                                  style: const TextStyle(
                                    color: Color(AppColors.primaryRed),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Description
                    Text(
                      'نبذة عن الشركة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    Text(
                      exhibitor['fullDescription'] ?? exhibitor['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getExhibitors() {
    return [
      {
        'name': 'شركة الأجهزة الطبية المتقدمة',
        'category': 'أجهزة طبية',
        'description': 'أحدث الأجهزة الطبية لعيادات الأسنان',
        'boothNumber': 'A-01',
        'hall': 'القاعة A',
        'fullDescription': 'شركة رائدة في مجال الأجهزة الطبية المتخصصة في طب الأسنان، تقدم أحدث التقنيات والحلول المبتكرة.',
      },
      {
        'name': 'مؤسسة المواد السنية الحديثة',
        'category': 'مواد سنية',
        'description': 'مواد سنية عالية الجودة ومعتمدة دولياً',
        'boothNumber': 'A-02',
        'hall': 'القاعة A',
        'fullDescription': 'متخصصون في توفير أفضل المواد السنية المستوردة من أوروبا وأمريكا.',
      },
      {
        'name': 'تقنيات الليزر للأسنان',
        'category': 'تقنيات حديثة',
        'description': 'أجهزة الليزر المتطورة لعلاج الأسنان',
        'boothNumber': 'B-01',
        'hall': 'القاعة B',
        'fullDescription': 'نقدم أحدث تقنيات الليزر في علاج الأسنان واللثة مع التدريب المتخصص.',
      },
      {
        'name': 'برمجيات إدارة العيادات',
        'category': 'برمجيات',
        'description': 'حلول رقمية شاملة لإدارة عيادات الأسنان',
        'boothNumber': 'B-02',
        'hall': 'القاعة B',
        'fullDescription': 'نظام متكامل لإدارة المواعيد والملفات الطبية والفواتير.',
      },
      {
        'name': 'أكاديمية التعليم المستمر',
        'category': 'تعليم',
        'description': 'دورات تدريبية وشهادات معتمدة',
        'boothNumber': 'C-01',
        'hall': 'القاعة C',
        'fullDescription': 'نقدم برامج تدريبية متخصصة وشهادات معتمدة في جميع تخصصات طب الأسنان.',
      },
      {
        'name': 'معدات التعقيم والسلامة',
        'category': 'أجهزة طبية',
        'description': 'أجهزة التعقيم وأدوات السلامة المهنية',
        'boothNumber': 'C-02',
        'hall': 'القاعة C',
        'fullDescription': 'متخصصون في توفير أجهزة التعقيم وأدوات السلامة المهنية للعيادات.',
      },
    ];
  }
}
