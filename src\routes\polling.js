const express = require('express');
const { body, param, query } = require('express-validator');
const pollingController = require('../controllers/pollingController');
const qaController = require('../controllers/qaController');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// All routes require authentication
router.use(auth.authenticate);

// POLLING ROUTES

// Create poll
router.post('/polls',
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('titleAr').optional().isString(),
    body('titleEn').optional().isString(),
    body('description').optional().isString(),
    body('type').isIn([
      'MULTIPLE_CHOICE',
      'SINGLE_CHOICE', 
      'YES_NO',
      'RATING',
      'TEXT',
      'RANKING'
    ]).withMessage('Invalid poll type'),
    body('sessionId').optional().isString(),
    body('isAnonymous').optional().isBoolean(),
    body('allowMultiple').optional().isBoolean(),
    body('options').isArray({ min: 1 }).withMessage('At least one option is required'),
    body('options.*.text').notEmpty().withMessage('Option text is required'),
    body('options.*.textAr').optional().isString(),
    body('options.*.textEn').optional().isString(),
    body('options.*.isCorrect').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  pollingController.createPoll
);

// Get poll by ID
router.get('/polls/:id',
  [
    param('id').notEmpty().withMessage('Poll ID is required')
  ],
  validation.handleValidationErrors,
  pollingController.getPollById
);

// Submit poll response
router.post('/polls/:id/respond',
  [
    param('id').notEmpty().withMessage('Poll ID is required'),
    body('optionId').optional().isString(),
    body('textResponse').optional().isString(),
    body('ratingValue').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5')
  ],
  validation.handleValidationErrors,
  pollingController.submitPollResponse
);

// Get live polls for session
router.get('/sessions/:sessionId/polls/live',
  [
    param('sessionId').notEmpty().withMessage('Session ID is required')
  ],
  validation.handleValidationErrors,
  pollingController.getLivePolls
);

// Toggle poll live status
router.put('/polls/:id/toggle-live',
  [
    param('id').notEmpty().withMessage('Poll ID is required'),
    body('isLive').isBoolean().withMessage('isLive must be boolean')
  ],
  validation.handleValidationErrors,
  pollingController.togglePollLive
);

// Get poll results
router.get('/polls/:id/results',
  [
    param('id').notEmpty().withMessage('Poll ID is required')
  ],
  validation.handleValidationErrors,
  pollingController.getPollResults
);

// Get user's polls
router.get('/my-polls',
  [
    query('sessionId').optional().isString(),
    query('isActive').optional().isBoolean(),
    query('isLive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  pollingController.getUserPolls
);

// Update poll
router.put('/polls/:id',
  [
    param('id').notEmpty().withMessage('Poll ID is required'),
    body('title').optional().isString(),
    body('description').optional().isString(),
    body('isActive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  pollingController.updatePoll
);

// Delete poll
router.delete('/polls/:id',
  [
    param('id').notEmpty().withMessage('Poll ID is required')
  ],
  validation.handleValidationErrors,
  pollingController.deletePoll
);

// Get polling statistics
router.get('/polls/stats', pollingController.getPollingStats);

// Q&A ROUTES

// Create Q&A session
router.post('/qa-sessions',
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('titleAr').optional().isString(),
    body('titleEn').optional().isString(),
    body('description').optional().isString(),
    body('sessionId').optional().isString(),
    body('allowAnonymous').optional().isBoolean(),
    body('moderationRequired').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  qaController.createQASession
);

// Get Q&A session by ID
router.get('/qa-sessions/:id',
  [
    param('id').notEmpty().withMessage('Q&A session ID is required')
  ],
  validation.handleValidationErrors,
  qaController.getQASessionById
);

// Submit question
router.post('/qa-sessions/:id/questions',
  [
    param('id').notEmpty().withMessage('Q&A session ID is required'),
    body('content').notEmpty().withMessage('Question content is required'),
    body('isAnonymous').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  qaController.submitQuestion
);

// Vote on question
router.post('/questions/:id/vote',
  [
    param('id').notEmpty().withMessage('Question ID is required'),
    body('voteType').isIn(['UPVOTE', 'DOWNVOTE']).withMessage('Invalid vote type')
  ],
  validation.handleValidationErrors,
  qaController.voteOnQuestion
);

// Answer question
router.post('/questions/:id/answer',
  [
    param('id').notEmpty().withMessage('Question ID is required'),
    body('answer').notEmpty().withMessage('Answer is required')
  ],
  validation.handleValidationErrors,
  qaController.answerQuestion
);

// Moderate question
router.put('/questions/:id/moderate',
  [
    param('id').notEmpty().withMessage('Question ID is required'),
    body('status').isIn(['APPROVED', 'REJECTED']).withMessage('Invalid status')
  ],
  validation.handleValidationErrors,
  qaController.moderateQuestion
);

// Highlight question
router.put('/questions/:id/highlight',
  [
    param('id').notEmpty().withMessage('Question ID is required'),
    body('isHighlighted').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  qaController.highlightQuestion
);

// Get pending questions
router.get('/qa-sessions/:id/questions/pending',
  [
    param('id').notEmpty().withMessage('Q&A session ID is required')
  ],
  validation.handleValidationErrors,
  qaController.getPendingQuestions
);

// Toggle Q&A session live status
router.put('/qa-sessions/:id/toggle-live',
  [
    param('id').notEmpty().withMessage('Q&A session ID is required'),
    body('isLive').isBoolean().withMessage('isLive must be boolean')
  ],
  validation.handleValidationErrors,
  qaController.toggleQASessionLive
);

// Get live Q&A sessions for session
router.get('/sessions/:sessionId/qa-sessions/live',
  [
    param('sessionId').notEmpty().withMessage('Session ID is required')
  ],
  validation.handleValidationErrors,
  qaController.getLiveQASessions
);

// Get Q&A statistics
router.get('/qa-sessions/stats', qaController.getQAStats);

module.exports = router;
