import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const { isAuthenticated, isLoading, user, hasRole } = useAuth();
  const { t } = useLanguage();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          {t('loading')}
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check for required role if specified
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={2}
        p={3}
      >
        <Typography variant="h4" color="error" textAlign="center">
          {t('accessDenied', 'غير مسموح بالوصول')}
        </Typography>
        <Typography variant="body1" color="text.secondary" textAlign="center">
          {t('insufficientPermissions', 'ليس لديك الصلاحيات الكافية للوصول إلى هذه الصفحة')}
        </Typography>
      </Box>
    );
  }

  // Render children if authenticated and authorized
  return children;
};

export default ProtectedRoute;
