import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  
  // Controllers
  final _arabicNameController = TextEditingController();
  final _englishNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _universityController = TextEditingController();
  final _specializationController = TextEditingController();
  
  // State
  int _currentStep = 0;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  String _selectedQualification = '';
  bool _acceptTerms = false;
  
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _qualifications = [
    'DOCTOR',
    'STUDENT_YEAR_6',
    'STUDENT_YEAR_5',
    'STUDENT_YEAR_4',
    'STUDENT_YEAR_3',
    'STUDENT_YEAR_2',
    'STUDENT_YEAR_1',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _arabicNameController.dispose();
    _englishNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _universityController.dispose();
    _specializationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _handleRegister();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate() || !_acceptTerms) {
      if (!_acceptTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب الموافقة على الشروط والأحكام'),
            backgroundColor: Color(AppColors.error),
          ),
        );
      }
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 3));
    
    setState(() {
      _isLoading = false;
    });
    
    // Show success and navigate
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء الحساب بنجاح!'),
          backgroundColor: Color(AppColors.success),
        ),
      );
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(AppColors.primaryRed),
              Color(AppColors.darkRed),
              Color(AppColors.primaryBlack),
            ],
            stops: [0.0, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(),
              
              // Progress Indicator
              _buildProgressIndicator(),
              
              // Form Content
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    margin: const EdgeInsets.all(AppSizes.paddingM),
                    decoration: BoxDecoration(
                      color: const Color(AppColors.primaryWhite),
                      borderRadius: BorderRadius.circular(AppSizes.radiusL),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(AppColors.primaryBlack).withValues(alpha: 0.2),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Form(
                      key: _formKey,
                      child: PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          _buildPersonalInfoStep(),
                          _buildAccountInfoStep(),
                          _buildAcademicInfoStep(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              
              // Navigation Buttons
              _buildNavigationButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: Color(AppColors.primaryWhite),
                ),
              ),
              const Spacer(),
              Text(
                AppStrings.register,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: const Color(AppColors.primaryWhite),
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            'إنشاء حساب جديد للمؤتمر',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
      child: Row(
        children: List.generate(3, (index) {
          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(
                right: index < 2 ? AppSizes.paddingS : 0,
              ),
              decoration: BoxDecoration(
                color: index <= _currentStep
                    ? const Color(AppColors.primaryWhite)
                    : const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPersonalInfoStep() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'المعلومات الشخصية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Arabic Name
          TextFormField(
            controller: _arabicNameController,
            decoration: InputDecoration(
              labelText: AppStrings.arabicName,
              prefixIcon: const Icon(Icons.person_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // English Name
          TextFormField(
            controller: _englishNameController,
            decoration: InputDecoration(
              labelText: AppStrings.englishName,
              prefixIcon: const Icon(Icons.person_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Phone
          TextFormField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: AppStrings.phone,
              prefixIcon: const Icon(Icons.phone_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              hintText: '+967xxxxxxxxx',
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAccountInfoStep() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'معلومات الحساب',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Email
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText: AppStrings.email,
              prefixIcon: const Icon(Icons.email_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              if (!value.contains('@')) {
                return AppStrings.emailInvalid;
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Password
          TextFormField(
            controller: _passwordController,
            obscureText: !_isPasswordVisible,
            decoration: InputDecoration(
              labelText: AppStrings.password,
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              if (value.length < AppConstants.minPasswordLength) {
                return AppStrings.passwordTooShort;
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Confirm Password
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: !_isConfirmPasswordVisible,
            decoration: InputDecoration(
              labelText: AppStrings.confirmPassword,
              prefixIcon: const Icon(Icons.lock_outlined),
              suffixIcon: IconButton(
                icon: Icon(
                  _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              if (value != _passwordController.text) {
                return AppStrings.passwordsDoNotMatch;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAcademicInfoStep() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'المعلومات الأكاديمية',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Qualification
          DropdownButtonFormField<String>(
            value: _selectedQualification.isEmpty ? null : _selectedQualification,
            decoration: InputDecoration(
              labelText: AppStrings.qualification,
              prefixIcon: const Icon(Icons.school_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
            items: _qualifications.map((qualification) {
              return DropdownMenuItem(
                value: qualification,
                child: Text(_getQualificationText(qualification)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedQualification = value ?? '';
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return AppStrings.fieldRequired;
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // University
          TextFormField(
            controller: _universityController,
            decoration: InputDecoration(
              labelText: AppStrings.university,
              prefixIcon: const Icon(Icons.account_balance_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Specialization
          TextFormField(
            controller: _specializationController,
            decoration: InputDecoration(
              labelText: AppStrings.specialization,
              prefixIcon: const Icon(Icons.medical_services_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Terms and Conditions
          Row(
            children: [
              Checkbox(
                value: _acceptTerms,
                onChanged: (value) {
                  setState(() {
                    _acceptTerms = value ?? false;
                  });
                },
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _acceptTerms = !_acceptTerms;
                    });
                  },
                  child: const Text(
                    'أوافق على الشروط والأحكام وسياسة الخصوصية',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(AppColors.primaryWhite),
                  side: const BorderSide(color: Color(AppColors.primaryWhite)),
                  padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                ),
                child: const Text('السابق'),
              ),
            ),
          
          if (_currentStep > 0) const SizedBox(width: AppSizes.paddingM),
          
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(AppColors.primaryWhite),
                foregroundColor: const Color(AppColors.primaryRed),
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                elevation: 8,
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(AppColors.primaryRed),
                        ),
                      ),
                    )
                  : Text(
                      _currentStep < 2 ? 'التالي' : 'إنشاء الحساب',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  String _getQualificationText(String qualification) {
    switch (qualification) {
      case 'DOCTOR':
        return 'طبيب';
      case 'STUDENT_YEAR_6':
        return 'طالب سنة سادسة';
      case 'STUDENT_YEAR_5':
        return 'طالب سنة خامسة';
      case 'STUDENT_YEAR_4':
        return 'طالب سنة رابعة';
      case 'STUDENT_YEAR_3':
        return 'طالب سنة ثالثة';
      case 'STUDENT_YEAR_2':
        return 'طالب سنة ثانية';
      case 'STUDENT_YEAR_1':
        return 'طالب سنة أولى';
      default:
        return qualification;
    }
  }
}
