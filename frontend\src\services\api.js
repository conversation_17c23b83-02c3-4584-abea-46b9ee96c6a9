import axios from 'axios';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  verifyOTP: (data) => api.post('/auth/verify-otp', data),
  sendOTP: (data) => api.post('/auth/send-otp', data),
  refreshToken: (refreshToken) => api.post('/auth/refresh-token', { refreshToken }),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (data) => api.post('/auth/reset-password', data),
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  changePassword: (data) => api.put('/users/change-password', data),
};

// Conference API
export const conferenceAPI = {
  getSessions: (params) => api.get('/conference/sessions', { params }),
  getSession: (id) => api.get(`/conference/sessions/${id}`),
  getSpeakers: () => api.get('/conference/speakers'),
  getSpeaker: (id) => api.get(`/conference/speakers/${id}`),
  registerAttendance: (sessionId) => api.post(`/conference/sessions/${sessionId}/attend`),
  getSessionQR: (sessionId) => api.get(`/conference/sessions/${sessionId}/qr`),
  rateSession: (sessionId, data) => api.post(`/conference/sessions/${sessionId}/rate`, data),
  getPersonalAgenda: () => api.get('/conference/my-agenda'),
  addToAgenda: (sessionId) => api.post('/conference/my-agenda/add', { sessionId }),
  removeFromAgenda: (sessionId) => api.delete(`/conference/my-agenda/${sessionId}`),
  searchSessions: (query) => api.get('/conference/sessions/search', { params: { q: query } }),
};

// Subscription API
export const subscriptionAPI = {
  getPricing: (qualification) => api.get('/subscriptions/pricing', { params: { qualification } }),
  createSubscription: (data) => api.post('/subscriptions', data),
  getMySubscription: () => api.get('/subscriptions/my-subscription'),
  getAllSubscriptions: (params) => api.get('/subscriptions', { params }),
  getSubscriptionStats: () => api.get('/subscriptions/stats'),
  updateSubscriptionStatus: (id, data) => api.put(`/subscriptions/${id}/status`, data),
  approveSubscription: (id, data) => api.put(`/subscriptions/${id}/approve`, data),
  rejectSubscription: (id, data) => api.put(`/subscriptions/${id}/reject`, data),
};

// User API
export const userAPI = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data) => api.put('/users/profile', data),
  changePassword: (data) => api.put('/users/change-password', data),
  getSubscriptionStatus: () => api.get('/users/subscription-status'),
  submitSubscription: (data) => api.post('/users/submit-subscription', data),
  uploadDocument: (data) => api.post('/users/upload-document', data),
  getPaymentHistory: (params) => api.get('/users/payment-history', { params }),
  getCertificates: () => api.get('/users/certificates'),
  getNotifications: (params) => api.get('/users/notifications', { params }),
  markNotificationRead: (id) => api.put(`/users/notifications/${id}/read`),
  deleteAccount: () => api.delete('/users/account'),
};

// Admin API
export const adminAPI = {
  getDashboard: () => api.get('/admin/dashboard'),
  getUsers: (params) => api.get('/admin/users', { params }),
  getUserDetails: (id) => api.get(`/admin/users/${id}`),
  updateUserStatus: (id, data) => api.put(`/admin/users/${id}/status`, data),
  createManualPayment: (data) => api.post('/admin/payments/manual', data),
  getReports: (params) => api.get('/admin/reports', { params }),
  sendNotification: (data) => api.post('/admin/notifications/send', data),
  getSystemHealth: () => api.get('/admin/system/health'),
  getSystemMetrics: () => api.get('/admin/system/metrics'),
  getSystemPerformance: () => api.get('/admin/system/performance'),
};

// Content Management API
export const contentAPI = {
  // Sessions
  createSession: (data) => api.post('/content/sessions', data),
  updateSession: (id, data) => api.put(`/content/sessions/${id}`, data),
  deleteSession: (id) => api.delete(`/content/sessions/${id}`),
  
  // Speakers
  createSpeaker: (data) => api.post('/content/speakers', data),
  updateSpeaker: (id, data) => api.put(`/content/speakers/${id}`, data),
  deleteSpeaker: (id) => api.delete(`/content/speakers/${id}`),
  
  // Announcements
  getAnnouncements: (params) => api.get('/content/announcements', { params }),
  createAnnouncement: (data) => api.post('/content/announcements', data),
  updateAnnouncement: (id, data) => api.put(`/content/announcements/${id}`, data),
  deleteAnnouncement: (id) => api.delete(`/content/announcements/${id}`),
  
  // Statistics
  getContentStats: () => api.get('/content/stats'),
};

// Monitoring API
export const monitoringAPI = {
  getHealth: () => api.get('/monitoring/health'),
  getMetrics: () => api.get('/monitoring/metrics'),
  getAdvancedMetrics: () => api.get('/monitoring/metrics/advanced'),
  getRealTimeStats: () => api.get('/monitoring/metrics/realtime'),
  getPerformance: () => api.get('/monitoring/performance'),
  getDashboard: () => api.get('/monitoring/dashboard'),
  getApiUsage: (params) => api.get('/monitoring/api-usage', { params }),
  getErrorLogs: (params) => api.get('/monitoring/errors', { params }),
  getAlerts: (params) => api.get('/monitoring/alerts', { params }),
  resetMetrics: () => api.post('/monitoring/reset'),
  exportMetrics: (params) => api.get('/monitoring/export', { params }),
};

// Upload API
export const uploadAPI = {
  uploadFile: (file, type) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    return api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  uploadProfileImage: (file) => {
    const formData = new FormData();
    formData.append('image', file);
    
    return api.post('/upload/profile-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  uploadDocument: (file, documentType) => {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('type', documentType);
    
    return api.post('/upload/document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// Export default api instance
export default api;
