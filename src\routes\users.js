const express = require('express');
const { authenticate } = require('../middleware/auth');
const userController = require('../controllers/userController');

const router = express.Router();

// Routes
router.get('/profile', authenticate, userController.getProfile);
router.put('/profile', authenticate, userController.updateProfile);
router.put('/change-password', authenticate, userController.changePassword);
router.get('/subscription-status', authenticate, userController.getSubscriptionStatus);
router.post('/submit-subscription', authenticate, userController.submitSubscription);
router.post('/upload-document', authenticate, userController.uploadDocument);
router.get('/payment-history', authenticate, userController.getPaymentHistory);
router.get('/certificates', authenticate, userController.getCertificates);
router.get('/notifications', authenticate, userController.getNotifications);
router.put('/notifications/:notificationId/read', authenticate, userController.markNotificationRead);
router.delete('/account', authenticate, userController.deleteAccount);

module.exports = router;
