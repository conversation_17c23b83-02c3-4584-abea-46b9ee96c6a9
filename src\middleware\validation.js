const { validationResult } = require('express-validator');
const { ValidationError } = require('./errorHandler');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }));
    
    return next(new ValidationError('Validation failed', formattedErrors));
  }
  
  next();
};

// Request ID middleware
const addRequestId = (req, res, next) => {
  req.requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);
  res.setHeader('X-Request-ID', req.requestId);
  next();
};

// Request timing middleware
const requestTiming = (req, res, next) => {
  req.startTime = Date.now();

  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - req.startTime;
    res.setHeader('X-Response-Time', `${duration}ms`);

    // Log API request
    const logger = require('../utils/logger');
    logger.logAPIRequest(req, res, duration);

    // Record metrics in both monitoring systems
    const { systemMonitor } = require('../utils/monitoring');
    const advancedMonitor = require('../utils/advancedMonitoring');

    const isError = res.statusCode >= 400;
    systemMonitor.recordRequest(duration, isError);
    advancedMonitor.recordRequest(duration, isError, req, res);

    return originalSend.call(this, data);
  };

  next();
};

// Sanitize input middleware
const sanitizeInput = (req, res, next) => {
  // Basic XSS protection
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }
    if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        obj[key] = sanitize(obj[key]);
      }
    }
    return obj;
  };
  
  if (req.body) {
    req.body = sanitize(req.body);
  }
  if (req.query) {
    req.query = sanitize(req.query);
  }
  if (req.params) {
    req.params = sanitize(req.params);
  }
  
  next();
};

// Pagination middleware
const pagination = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;
  
  // Validate pagination parameters
  if (page < 1) {
    return next(new ValidationError('Page number must be greater than 0'));
  }
  
  if (limit < 1 || limit > 100) {
    return next(new ValidationError('Limit must be between 1 and 100'));
  }
  
  req.pagination = {
    page,
    limit,
    offset
  };
  
  next();
};

// Response formatting middleware
const formatResponse = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    // If data is already formatted, send as is
    if (data && typeof data === 'object' && data.hasOwnProperty('success')) {
      return originalJson.call(this, data);
    }
    
    // Format the response
    const formattedResponse = {
      success: res.statusCode < 400,
      data: data,
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    };
    
    // Add pagination info if available
    if (req.paginationResult) {
      formattedResponse.pagination = req.paginationResult;
    }
    
    return originalJson.call(this, formattedResponse);
  };
  
  next();
};

// Helper function to create pagination result
const createPaginationResult = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    page,
    limit,
    total,
    pages: totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  };
};

module.exports = {
  handleValidationErrors,
  addRequestId,
  requestTiming,
  sanitizeInput,
  pagination,
  formatResponse,
  createPaginationResult
};
