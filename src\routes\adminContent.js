const express = require('express');
const { body, param, query } = require('express-validator');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// All routes require admin authentication
router.use(auth.authenticate);
router.use(auth.requireRole(['SUPER_ADMIN', 'ADMIN']));

// LIBRARY CONTENT MANAGEMENT

// Create library category
router.post('/library/categories',
  [
    body('name').notEmpty().withMessage('Category name is required'),
    body('nameAr').notEmpty().withMessage('Arabic name is required'),
    body('nameEn').notEmpty().withMessage('English name is required'),
    body('description').optional().isString(),
    body('parentId').optional().isString(),
    body('icon').optional().isString(),
    body('color').optional().isString(),
    body('sortOrder').optional().isInt()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const category = await prisma.libraryCategory.create({
        data: req.body
      });

      res.status(201).json({
        success: true,
        message: 'Library category created successfully',
        data: category
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to create library category',
          code: 'CREATE_CATEGORY_ERROR'
        }
      });
    }
  }
);

// Create library item
router.post('/library/items',
  [
    body('categoryId').notEmpty().withMessage('Category ID is required'),
    body('title').notEmpty().withMessage('Title is required'),
    body('titleAr').optional().isString(),
    body('titleEn').optional().isString(),
    body('description').optional().isString(),
    body('content').optional().isString(),
    body('type').isIn([
      'DOCUMENT',
      'PRESENTATION', 
      'VIDEO',
      'AUDIO',
      'IMAGE',
      'ARTICLE',
      'RESEARCH_PAPER',
      'CASE_STUDY',
      'GUIDELINE',
      'PROTOCOL'
    ]).withMessage('Invalid item type'),
    body('fileUrl').optional().isURL(),
    body('author').optional().isString(),
    body('tags').optional().isArray(),
    body('isPublic').optional().isBoolean(),
    body('isFeatured').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const item = await prisma.libraryItem.create({
        data: req.body,
        include: {
          category: true
        }
      });

      res.status(201).json({
        success: true,
        message: 'Library item created successfully',
        data: item
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to create library item',
          code: 'CREATE_ITEM_ERROR'
        }
      });
    }
  }
);

// Update library item
router.put('/library/items/:id',
  [
    param('id').notEmpty().withMessage('Item ID is required'),
    body('title').optional().isString(),
    body('description').optional().isString(),
    body('content').optional().isString(),
    body('isPublic').optional().isBoolean(),
    body('isFeatured').optional().isBoolean(),
    body('isActive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const item = await prisma.libraryItem.update({
        where: { id: req.params.id },
        data: req.body,
        include: {
          category: true
        }
      });

      res.json({
        success: true,
        message: 'Library item updated successfully',
        data: item
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to update library item',
          code: 'UPDATE_ITEM_ERROR'
        }
      });
    }
  }
);

// Delete library item
router.delete('/library/items/:id',
  [
    param('id').notEmpty().withMessage('Item ID is required')
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      await prisma.libraryItem.delete({
        where: { id: req.params.id }
      });

      res.json({
        success: true,
        message: 'Library item deleted successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to delete library item',
          code: 'DELETE_ITEM_ERROR'
        }
      });
    }
  }
);

// EXHIBITION MANAGEMENT

// Create exhibitor
router.post('/exhibition/exhibitors',
  [
    body('companyName').notEmpty().withMessage('Company name is required'),
    body('companyNameAr').optional().isString(),
    body('companyNameEn').optional().isString(),
    body('description').optional().isString(),
    body('website').optional().isURL(),
    body('email').optional().isEmail(),
    body('phone').optional().isString(),
    body('category').isIn([
      'DENTAL_EQUIPMENT',
      'DENTAL_MATERIALS',
      'PHARMACEUTICALS', 
      'TECHNOLOGY',
      'EDUCATION',
      'SERVICES',
      'OTHER'
    ]).withMessage('Invalid category'),
    body('isPremium').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const exhibitor = await prisma.exhibitor.create({
        data: req.body
      });

      res.status(201).json({
        success: true,
        message: 'Exhibitor created successfully',
        data: exhibitor
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to create exhibitor',
          code: 'CREATE_EXHIBITOR_ERROR'
        }
      });
    }
  }
);

// Create booth
router.post('/exhibition/booths',
  [
    body('exhibitorId').notEmpty().withMessage('Exhibitor ID is required'),
    body('boothNumber').notEmpty().withMessage('Booth number is required'),
    body('hallName').notEmpty().withMessage('Hall name is required'),
    body('size').optional().isString(),
    body('location').optional().isObject()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const booth = await prisma.booth.create({
        data: req.body,
        include: {
          exhibitor: true
        }
      });

      res.status(201).json({
        success: true,
        message: 'Booth created successfully',
        data: booth
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to create booth',
          code: 'CREATE_BOOTH_ERROR'
        }
      });
    }
  }
);

// POLLING MANAGEMENT

// Get all polls with management info
router.get('/polling/polls',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('sessionId').optional().isString(),
    query('isActive').optional().isBoolean(),
    query('isLive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const { page = 1, limit = 20, sessionId, isActive, isLive } = req.query;

      const where = {};
      if (sessionId) where.sessionId = sessionId;
      if (isActive !== undefined) where.isActive = isActive === 'true';
      if (isLive !== undefined) where.isLive = isLive === 'true';

      const [polls, total] = await Promise.all([
        prisma.poll.findMany({
          where,
          include: {
            session: {
              select: {
                titleAr: true,
                titleEn: true
              }
            },
            creator: {
              select: {
                arabicName: true,
                englishName: true
              }
            },
            _count: {
              select: { responses: true }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.poll.count({ where })
      ]);

      res.json({
        success: true,
        data: polls,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get polls',
          code: 'GET_POLLS_ERROR'
        }
      });
    }
  }
);

// Get all Q&A sessions with management info
router.get('/polling/qa-sessions',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('sessionId').optional().isString(),
    query('isActive').optional().isBoolean(),
    query('isLive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  async (req, res) => {
    try {
      const { page = 1, limit = 20, sessionId, isActive, isLive } = req.query;

      const where = {};
      if (sessionId) where.sessionId = sessionId;
      if (isActive !== undefined) where.isActive = isActive === 'true';
      if (isLive !== undefined) where.isLive = isLive === 'true';

      const [qaSessions, total] = await Promise.all([
        prisma.qASession.findMany({
          where,
          include: {
            session: {
              select: {
                titleAr: true,
                titleEn: true
              }
            },
            creator: {
              select: {
                arabicName: true,
                englishName: true
              }
            },
            _count: {
              select: { questions: true }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.qASession.count({ where })
      ]);

      res.json({
        success: true,
        data: qaSessions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get Q&A sessions',
          code: 'GET_QA_SESSIONS_ERROR'
        }
      });
    }
  }
);

module.exports = router;
