# IDEC 2025 - Dental Conference Management System

## نظرة عامة

نظام إدارة شامل لمؤتمر ومعرض طب الأسنان IDEC 2025، يوفر منصة متكاملة لإدارة التسجيل، الاشتراكات، الجلسات، والدورات التدريبية.

## الميزات الرئيسية

### للمشاركين
- ✅ تسجيل وإدارة الحساب الشخصي
- ✅ نظام اشتراك متدرج مع موافقة الوثائق
- ✅ جدول أعمال تفاعلي مع تنبيهات ذكية
- ✅ نظام QR Code لتسجيل الحضور
- ✅ مكتبة رقمية للمحتوى العلمي
- ✅ شبكة تواصل احترافية
- ✅ نظام تلعيب ومكافآت

### للإدارة
- ✅ لوحة تحكم شاملة
- ✅ إدارة المستخدمين والصلاحيات
- ✅ نظام موافقة الاشتراكات
- ✅ إدارة المدفوعات والمحاسبة
- ✅ تقارير وتحليلات متقدمة
- ✅ نظام إشعارات متعدد القنوات

## التقنيات المستخدمة

### Backend
- **Node.js** + **Express.js** - خادم التطبيق
- **TypeScript** - لضمان جودة الكود
- **PostgreSQL** - قاعدة البيانات الرئيسية
- **Prisma ORM** - إدارة قاعدة البيانات
- **Redis** - التخزين المؤقت والجلسات
- **JWT** - المصادقة والتفويض

### الأمان
- **bcryptjs** - تشفير كلمات المرور
- **Helmet** - حماية HTTP headers
- **Rate Limiting** - منع الهجمات
- **Input Validation** - التحقق من المدخلات

### المراقبة والسجلات
- **Winston** - نظام السجلات
- **نظام مراقبة مخصص** - مراقبة الأداء والموارد
- **PM2** - إدارة العمليات

## متطلبات النظام

### الحد الأدنى
- **Node.js** >= 18.0.0
- **PostgreSQL** >= 13
- **Redis** >= 6.0
- **RAM** >= 4GB
- **Storage** >= 50GB

### المُوصى به
- **Node.js** >= 20.0.0
- **PostgreSQL** >= 15
- **Redis** >= 7.0
- **RAM** >= 8GB
- **Storage** >= 100GB SSD

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/idec2025-backend.git
cd idec2025-backend
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتعديل ملف .env بالقيم المناسبة
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb idec2025

# تشغيل المايجريشن
npm run db:migrate

# إنشاء البيانات التجريبية (اختياري)
npm run db:seed
```

### 5. تشغيل التطبيق

#### بيئة التطوير
```bash
npm run dev
```

#### بيئة الإنتاج
```bash
npm start
# أو باستخدام PM2
npm run pm2:start
```

## هيكل المشروع

```
src/
├── config/          # ملفات التكوين
│   ├── database.js  # إعداد قاعدة البيانات
│   └── redis.js     # إعداد Redis
├── controllers/     # منطق التحكم
├── middleware/      # الوسطاء
│   └── errorHandler.js
├── models/          # نماذج البيانات
├── routes/          # مسارات API
│   └── index.js
├── services/        # خدمات الأعمال
├── utils/           # أدوات مساعدة
│   ├── logger.js    # نظام السجلات
│   └── monitoring.js # نظام المراقبة
├── workers/         # العمليات الخلفية
└── server.js        # نقطة البداية
```

## API Documentation

### نقاط النهاية الرئيسية

#### المصادقة
- `POST /api/v1/auth/register` - تسجيل مستخدم جديد
- `POST /api/v1/auth/login` - تسجيل الدخول
- `POST /api/v1/auth/verify-otp` - التحقق من OTP
- `POST /api/v1/auth/refresh-token` - تجديد الرمز المميز

#### المستخدمين
- `GET /api/v1/users/profile` - الملف الشخصي
- `PUT /api/v1/users/profile` - تحديث الملف الشخصي
- `POST /api/v1/users/upload-document` - رفع الوثائق

#### المؤتمر
- `GET /api/v1/conference/sessions` - قائمة الجلسات
- `POST /api/v1/conference/sessions/:id/attend` - تسجيل الحضور
- `GET /api/v1/conference/my-agenda` - الجدول الشخصي

#### الإدارة
- `GET /api/v1/admin/users` - قائمة المستخدمين
- `PUT /api/v1/admin/subscriptions/:id/approve` - موافقة الاشتراك
- `GET /api/v1/admin/reports/financial` - التقارير المالية

### فحص الصحة
- `GET /health` - فحص صحة التطبيق
- `GET /metrics` - مقاييس الأداء

## النشر

### باستخدام PM2
```bash
# بدء التطبيق
pm2 start ecosystem.config.js --env production

# مراقبة التطبيق
pm2 monit

# إعادة تشغيل
pm2 restart idec2025-api

# إيقاف التطبيق
pm2 stop idec2025-api
```

### باستخدام Docker (قريباً)
```bash
docker build -t idec2025-backend .
docker run -p 3000:3000 idec2025-backend
```

## المراقبة والسجلات

### السجلات
- **combined.log** - جميع السجلات
- **error.log** - سجلات الأخطاء فقط
- **http.log** - سجلات HTTP (بيئة التطوير)
- **metrics.log** - مقاييس الأداء

### المراقبة
- **CPU Usage** - استخدام المعالج
- **Memory Usage** - استخدام الذاكرة
- **Disk Usage** - استخدام القرص الصلب
- **Response Time** - زمن الاستجابة
- **Error Rate** - معدل الأخطاء

## الاختبار

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل الاختبارات مع المراقبة
npm run test:watch

# تقرير التغطية
npm run test:coverage
```

## المساهمة

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم، يرجى التواصل مع:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-XX-XXX-XXXX

## الحالة

- ✅ **Backend API** - مكتمل
- 🚧 **Admin Dashboard** - قيد التطوير
- 🚧 **Mobile App** - قيد التطوير
- ⏳ **Testing** - مخطط

---

**تم تطويره بواسطة فريق IDEC 2025** 🚀
