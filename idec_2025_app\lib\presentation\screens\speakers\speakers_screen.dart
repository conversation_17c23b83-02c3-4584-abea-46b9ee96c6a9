import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class SpeakersScreen extends StatefulWidget {
  const SpeakersScreen({super.key});

  @override
  State<SpeakersScreen> createState() => _SpeakersScreenState();
}

class _SpeakersScreenState extends State<SpeakersScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String _selectedCategory = 'الكل';
  final List<String> _categories = [
    'الكل',
    'متحدثون رئيسيون',
    'أطباء استشاريون',
    'أكاديميون',
    'باحثون',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: const Color(AppColors.primaryRed),
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'المتحدثون',
                style: TextStyle(
                  color: Color(AppColors.primaryWhite),
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(AppColors.primaryRed),
                      Color(AppColors.darkRed),
                      Color(AppColors.primaryBlack),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background Pattern
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: Container(
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/speakers_bg.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    // Content
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(height: 40),
                          Container(
                            padding: const EdgeInsets.all(AppSizes.paddingM),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                            ),
                            child: const Icon(
                              Icons.people,
                              size: 40,
                              color: Color(AppColors.primaryWhite),
                            ),
                          ),
                          const SizedBox(height: AppSizes.paddingS),
                          Text(
                            'خبراء من جميع أنحاء العالم',
                            style: TextStyle(
                              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Category Filter
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _categories.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Container(
                      margin: const EdgeInsets.only(right: AppSizes.paddingS),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                        backgroundColor: const Color(AppColors.lightGrey),
                        selectedColor: const Color(AppColors.primaryRed),
                        labelStyle: TextStyle(
                          color: isSelected
                              ? const Color(AppColors.primaryWhite)
                              : const Color(AppColors.primaryBlack),
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusL),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
          
          // Speakers Grid
          SliverPadding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            sliver: FadeTransition(
              opacity: _fadeAnimation,
              child: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: AppSizes.paddingM,
                  mainAxisSpacing: AppSizes.paddingM,
                  childAspectRatio: 0.75,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    return _buildSpeakerCard(_getSpeakers()[index]);
                  },
                  childCount: _getSpeakers().length,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpeakerCard(Map<String, dynamic> speaker) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Speaker Image
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(AppColors.primaryRed).withValues(alpha: 0.8),
                    const Color(AppColors.darkRed).withValues(alpha: 0.6),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppSizes.radiusL),
                  topRight: Radius.circular(AppSizes.radiusL),
                ),
              ),
              child: Stack(
                children: [
                  // Placeholder for speaker image
                  Center(
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: const Icon(
                        Icons.person,
                        size: 30,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),
                  ),
                  
                  // Category Badge
                  Positioned(
                    top: AppSizes.paddingS,
                    right: AppSizes.paddingS,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: AppSizes.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryBlack).withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                      child: Text(
                        speaker['category'],
                        style: const TextStyle(
                          color: Color(AppColors.primaryWhite),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Speaker Info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    speaker['name'],
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Color(AppColors.primaryBlack),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: AppSizes.paddingXS),
                  
                  // Title
                  Text(
                    speaker['title'],
                    style: TextStyle(
                      fontSize: 12,
                      color: const Color(AppColors.grey).withValues(alpha: 0.8),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const Spacer(),
                  
                  // Action Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        _showSpeakerDetails(speaker);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(AppColors.primaryRed),
                        foregroundColor: const Color(AppColors.primaryWhite),
                        padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingS),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusS),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'عرض التفاصيل',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showSpeakerDetails(Map<String, dynamic> speaker) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Speaker Header
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(AppColors.primaryRed),
                                const Color(AppColors.darkRed),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: const Icon(
                            Icons.person,
                            size: 40,
                            color: Color(AppColors.primaryWhite),
                          ),
                        ),
                        
                        const SizedBox(width: AppSizes.paddingM),
                        
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                speaker['name'],
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: const Color(AppColors.primaryBlack),
                                ),
                              ),
                              Text(
                                speaker['title'],
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: const Color(AppColors.grey),
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: AppSizes.paddingXS),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingS,
                                  vertical: AppSizes.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                ),
                                child: Text(
                                  speaker['category'],
                                  style: const TextStyle(
                                    color: Color(AppColors.primaryRed),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Bio
                    Text(
                      'نبذة عن المتحدث',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    Text(
                      speaker['bio'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(AppColors.primaryBlack),
                        height: 1.6,
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Sessions
                    Text(
                      'الجلسات',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    ...speaker['sessions'].map<Widget>((session) => Container(
                      margin: const EdgeInsets.only(bottom: AppSizes.paddingS),
                      padding: const EdgeInsets.all(AppSizes.paddingM),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.lightGrey),
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.event,
                            color: Color(AppColors.primaryRed),
                            size: AppSizes.iconS,
                          ),
                          const SizedBox(width: AppSizes.paddingS),
                          Expanded(
                            child: Text(
                              session,
                              style: const TextStyle(
                                color: Color(AppColors.primaryBlack),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )).toList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getSpeakers() {
    final allSpeakers = [
      {
        'name': 'د. أحمد محمد علي',
        'title': 'استشاري جراحة الفم والوجه والفكين',
        'category': 'متحدث رئيسي',
        'bio': 'خبير في جراحة الفم والوجه والفكين مع أكثر من 20 عاماً من الخبرة. حاصل على الدكتوراه من جامعة هارفارد ومؤلف لأكثر من 50 بحثاً علمياً في مجال طب الأسنان.',
        'sessions': ['الجراحة التجميلية للوجه والفكين', 'أحدث تقنيات زراعة الأسنان'],
      },
      {
        'name': 'د. فاطمة أحمد حسن',
        'title': 'أستاذة طب أسنان الأطفال',
        'category': 'أكاديمية',
        'bio': 'أستاذة في كلية طب الأسنان بجامعة صنعاء، متخصصة في طب أسنان الأطفال والوقاية. لها مساهمات كبيرة في تطوير برامج الوقاية من تسوس الأسنان.',
        'sessions': ['طب أسنان الأطفال الحديث', 'برامج الوقاية المجتمعية'],
      },
      {
        'name': 'د. محمد عبدالله الزهراني',
        'title': 'استشاري تقويم الأسنان',
        'category': 'استشاري',
        'bio': 'استشاري تقويم الأسنان مع خبرة واسعة في التقويم الشفاف والتقويم الجراحي. حاصل على زمالة من الجمعية الأمريكية لتقويم الأسنان.',
        'sessions': ['التقويم الشفاف', 'التقويم الجراحي المعقد'],
      },
      {
        'name': 'د. سارة محمد قاسم',
        'title': 'باحثة في علوم المواد السنية',
        'category': 'باحثة',
        'bio': 'باحثة متخصصة في تطوير المواد السنية الحديثة. حاصلة على الدكتوراه في علوم المواد وتعمل على تطوير مواد حشو جديدة.',
        'sessions': ['المواد السنية المتقدمة', 'مستقبل المواد في طب الأسنان'],
      },
      {
        'name': 'د. عبدالرحمن علي محمد',
        'title': 'استشاري علاج الجذور',
        'category': 'استشاري',
        'bio': 'استشاري علاج الجذور مع خبرة في استخدام المجهر الجراحي والتقنيات الحديثة في علاج الجذور. مدرب معتمد في عدة برامج دولية.',
        'sessions': ['علاج الجذور بالمجهر', 'الحالات المعقدة في علاج الجذور'],
      },
      {
        'name': 'د. نورا أحمد سالم',
        'title': 'استشارية طب اللثة',
        'category': 'استشارية',
        'bio': 'استشارية طب اللثة والأنسجة المحيطة بالأسنان. متخصصة في جراحة اللثة التجميلية وزراعة الأسنان. حاصلة على الماجستير من جامعة لندن.',
        'sessions': ['جراحة اللثة التجميلية', 'إدارة أمراض اللثة المتقدمة'],
      },
    ];

    if (_selectedCategory == 'الكل') {
      return allSpeakers;
    }
    
    return allSpeakers.where((speaker) => speaker['category'] == _selectedCategory).toList();
  }
}
