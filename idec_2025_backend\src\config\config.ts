import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface Config {
  // Server Configuration
  port: number;
  nodeEnv: string;
  apiVersion: string;

  // Database Configuration
  database: {
    url: string;
  };

  // Redis Configuration
  redis: {
    url: string;
  };

  // JWT Configuration
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };

  // Encryption Configuration
  encryption: {
    bcryptRounds: number;
    key: string;
  };

  // File Upload Configuration
  upload: {
    maxFileSize: number;
    path: string;
    allowedTypes: string[];
  };

  // Email Configuration
  email: {
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      user: string;
      pass: string;
    };
    from: string;
  };

  // SMS Configuration (Twilio)
  sms: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };

  // WhatsApp Configuration
  whatsapp: {
    apiUrl: string;
    token: string;
  };

  // Payment Gateway Configuration
  payment: {
    stripe: {
      secretKey: string;
      publishableKey: string;
      webhookSecret: string;
    };
    paypal: {
      clientId: string;
      clientSecret: string;
      mode: string;
    };
  };

  // Cloud Storage Configuration
  storage: {
    aws: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
      bucket: string;
    };
  };

  // Firebase Configuration
  firebase: {
    projectId: string;
    privateKey: string;
    clientEmail: string;
  };

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // CORS Configuration
  cors: {
    origin: string | string[];
    credentials: boolean;
  };

  // Logging Configuration
  logging: {
    level: string;
    file: string;
  };

  // QR Code Configuration
  qr: {
    size: number;
    margin: number;
    errorCorrection: string;
  };

  // Conference Configuration
  conference: {
    name: string;
    startDate: string;
    endDate: string;
    timezone: string;
    location: string;
  };

  // Admin Configuration
  admin: {
    email: string;
    password: string;
  };

  // Security Configuration
  security: {
    helmet: {
      csp: boolean;
    };
    trustProxy: boolean;
  };

  // Development Configuration
  development: {
    debug: boolean;
  };

  // Swagger Configuration
  swagger: {
    enabled: boolean;
  };
}

const getEnvVar = (name: string, defaultValue?: string): string => {
  const value = process.env[name];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value || defaultValue!;
};

const getEnvNumber = (name: string, defaultValue?: number): number => {
  const value = process.env[name];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value ? parseInt(value, 10) : defaultValue!;
};

const getEnvBoolean = (name: string, defaultValue?: boolean): boolean => {
  const value = process.env[name];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value ? value.toLowerCase() === 'true' : defaultValue!;
};

const getEnvArray = (name: string, defaultValue?: string[]): string[] => {
  const value = process.env[name];
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is required`);
  }
  return value ? value.split(',').map(item => item.trim()) : defaultValue!;
};

export const config: Config = {
  // Server Configuration
  port: getEnvNumber('PORT', 3000),
  nodeEnv: getEnvVar('NODE_ENV', 'development'),
  apiVersion: getEnvVar('API_VERSION', 'v1'),

  // Database Configuration
  database: {
    url: getEnvVar('DATABASE_URL'),
  },

  // Redis Configuration
  redis: {
    url: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
  },

  // JWT Configuration
  jwt: {
    secret: getEnvVar('JWT_SECRET'),
    expiresIn: getEnvVar('JWT_EXPIRES_IN', '24h'),
    refreshSecret: getEnvVar('JWT_REFRESH_SECRET'),
    refreshExpiresIn: getEnvVar('JWT_REFRESH_EXPIRES_IN', '7d'),
  },

  // Encryption Configuration
  encryption: {
    bcryptRounds: getEnvNumber('BCRYPT_ROUNDS', 12),
    key: getEnvVar('ENCRYPTION_KEY'),
  },

  // File Upload Configuration
  upload: {
    maxFileSize: getEnvNumber('MAX_FILE_SIZE', 10485760), // 10MB
    path: getEnvVar('UPLOAD_PATH', './uploads'),
    allowedTypes: getEnvArray('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']),
  },

  // Email Configuration
  email: {
    smtp: {
      host: getEnvVar('SMTP_HOST', 'smtp.gmail.com'),
      port: getEnvNumber('SMTP_PORT', 587),
      secure: getEnvBoolean('SMTP_SECURE', false),
      user: getEnvVar('SMTP_USER'),
      pass: getEnvVar('SMTP_PASS'),
    },
    from: getEnvVar('EMAIL_FROM', '<EMAIL>'),
  },

  // SMS Configuration
  sms: {
    accountSid: getEnvVar('TWILIO_ACCOUNT_SID'),
    authToken: getEnvVar('TWILIO_AUTH_TOKEN'),
    phoneNumber: getEnvVar('TWILIO_PHONE_NUMBER'),
  },

  // WhatsApp Configuration
  whatsapp: {
    apiUrl: getEnvVar('WHATSAPP_API_URL', 'https://api.whatsapp.com'),
    token: getEnvVar('WHATSAPP_TOKEN'),
  },

  // Payment Gateway Configuration
  payment: {
    stripe: {
      secretKey: getEnvVar('STRIPE_SECRET_KEY'),
      publishableKey: getEnvVar('STRIPE_PUBLISHABLE_KEY'),
      webhookSecret: getEnvVar('STRIPE_WEBHOOK_SECRET'),
    },
    paypal: {
      clientId: getEnvVar('PAYPAL_CLIENT_ID'),
      clientSecret: getEnvVar('PAYPAL_CLIENT_SECRET'),
      mode: getEnvVar('PAYPAL_MODE', 'sandbox'),
    },
  },

  // Cloud Storage Configuration
  storage: {
    aws: {
      accessKeyId: getEnvVar('AWS_ACCESS_KEY_ID'),
      secretAccessKey: getEnvVar('AWS_SECRET_ACCESS_KEY'),
      region: getEnvVar('AWS_REGION', 'us-east-1'),
      bucket: getEnvVar('AWS_S3_BUCKET'),
    },
  },

  // Firebase Configuration
  firebase: {
    projectId: getEnvVar('FIREBASE_PROJECT_ID'),
    privateKey: getEnvVar('FIREBASE_PRIVATE_KEY'),
    clientEmail: getEnvVar('FIREBASE_CLIENT_EMAIL'),
  },

  // Rate Limiting Configuration
  rateLimit: {
    windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 900000), // 15 minutes
    maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
  },

  // CORS Configuration
  cors: {
    origin: getEnvArray('CORS_ORIGIN', ['http://localhost:3000']),
    credentials: getEnvBoolean('CORS_CREDENTIALS', true),
  },

  // Logging Configuration
  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    file: getEnvVar('LOG_FILE', './logs/app.log'),
  },

  // QR Code Configuration
  qr: {
    size: getEnvNumber('QR_CODE_SIZE', 200),
    margin: getEnvNumber('QR_CODE_MARGIN', 4),
    errorCorrection: getEnvVar('QR_CODE_ERROR_CORRECTION', 'M'),
  },

  // Conference Configuration
  conference: {
    name: getEnvVar('CONFERENCE_NAME', 'IDEC 2025'),
    startDate: getEnvVar('CONFERENCE_START_DATE', '2025-03-15'),
    endDate: getEnvVar('CONFERENCE_END_DATE', '2025-03-17'),
    timezone: getEnvVar('CONFERENCE_TIMEZONE', 'Asia/Riyadh'),
    location: getEnvVar('CONFERENCE_LOCATION', 'Riyadh, Saudi Arabia'),
  },

  // Admin Configuration
  admin: {
    email: getEnvVar('ADMIN_EMAIL', '<EMAIL>'),
    password: getEnvVar('ADMIN_PASSWORD'),
  },

  // Security Configuration
  security: {
    helmet: {
      csp: getEnvBoolean('HELMET_CSP_ENABLED', true),
    },
    trustProxy: getEnvBoolean('TRUST_PROXY', false),
  },

  // Development Configuration
  development: {
    debug: getEnvBoolean('DEBUG', false),
  },

  // Swagger Configuration
  swagger: {
    enabled: getEnvBoolean('SWAGGER_ENABLED', true),
  },
};
