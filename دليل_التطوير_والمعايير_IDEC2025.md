# دليل التطوير والمعايير - مشروع IDEC 2025

## 1. معايير البرمجة والتطوير

### 1.1 معايير الكود

#### تسمية المتغيرات والدوال
```javascript
// ✅ صحيح
const getUserSubscriptionStatus = async (userId) => {
  const subscriptionData = await db.subscriptions.findByUserId(userId);
  return subscriptionData;
};

// ❌ خطأ
const getUsrSubStat = async (id) => {
  const data = await db.subs.find(id);
  return data;
};
```

#### تنظيم الملفات
```
src/
├── controllers/
│   ├── auth.controller.js
│   ├── user.controller.js
│   └── conference.controller.js
├── services/
│   ├── auth.service.js
│   ├── payment.service.js
│   └── notification.service.js
├── models/
│   ├── User.js
│   ├── Session.js
│   └── Payment.js
├── middleware/
│   ├── auth.middleware.js
│   ├── validation.middleware.js
│   └── rateLimit.middleware.js
├── utils/
│   ├── logger.js
│   ├── encryption.js
│   └── qrGenerator.js
└── config/
    ├── database.js
    ├── redis.js
    └── environment.js
```

#### معايير التعليقات
```javascript
/**
 * يقوم بإنشاء اشتراك جديد للمستخدم في المؤتمر
 * @param {string} userId - معرف المستخدم
 * @param {Object} subscriptionData - بيانات الاشتراك
 * @param {string} subscriptionData.qualification - المؤهل العلمي
 * @param {string} subscriptionData.specialization - التخصص
 * @returns {Promise<Object>} بيانات الاشتراك المُنشأ
 * @throws {ValidationError} في حالة بيانات غير صحيحة
 * @throws {DatabaseError} في حالة خطأ في قاعدة البيانات
 */
const createSubscription = async (userId, subscriptionData) => {
  // التحقق من صحة البيانات
  validateSubscriptionData(subscriptionData);
  
  // إنشاء الاشتراك
  const subscription = await db.subscriptions.create({
    userId,
    ...subscriptionData,
    status: 'pending_review'
  });
  
  return subscription;
};
```

### 1.2 معايير قاعدة البيانات

#### تسمية الجداول والحقول
```sql
-- ✅ صحيح
CREATE TABLE user_subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    subscription_status VARCHAR(50) NOT NULL,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP,
    payment_deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ❌ خطأ
CREATE TABLE usrsub (
    id SERIAL PRIMARY KEY,
    uid INTEGER,
    stat VARCHAR(50),
    apprby INTEGER,
    crtd TIMESTAMP
);
```

#### فهارس الأداء
```sql
-- فهارس للاستعلامات الشائعة
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(subscription_status);
CREATE INDEX idx_session_attendance_session_date ON session_attendance(session_id, created_at);
CREATE INDEX idx_payments_user_status ON payments(user_id, status);

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX idx_users_qualification_specialization ON users(qualification, specialization);
```

### 1.3 معايير الأمان

#### التحقق من المدخلات
```javascript
const { body, validationResult } = require('express-validator');

// قواعد التحقق من بيانات التسجيل
const registrationValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  
  body('phone')
    .isMobilePhone('ar-SA')
    .withMessage('رقم الهاتف غير صحيح'),
  
  body('arabicName')
    .isLength({ min: 2, max: 100 })
    .matches(/^[\u0600-\u06FF\s]+$/)
    .withMessage('الاسم العربي يجب أن يحتوي على أحرف عربية فقط'),
  
  body('englishName')
    .isLength({ min: 2, max: 100 })
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('الاسم الإنجليزي يجب أن يحتوي على أحرف إنجليزية فقط'),
  
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة ورقم ورمز خاص')
];

// معالج التحقق
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'بيانات غير صحيحة',
      errors: errors.array()
    });
  }
  next();
};
```

#### تشفير البيانات الحساسة
```javascript
const bcrypt = require('bcrypt');
const crypto = require('crypto');

// تشفير كلمات المرور
const hashPassword = async (password) => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

// تشفير البيانات الحساسة
const encryptSensitiveData = (data) => {
  const algorithm = 'aes-256-gcm';
  const key = Buffer.from(process.env.ENCRYPTION_KEY, 'hex');
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  cipher.setAAD(Buffer.from('IDEC2025', 'utf8'));
  
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return {
    encrypted,
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex')
  };
};
```

## 2. معايير واجهة المستخدم (UI/UX)

### 2.1 نظام الألوان

#### الألوان الأساسية
```css
:root {
  /* الألوان الأساسية */
  --primary-red: #DC143C;
  --primary-black: #1A1A1A;
  --primary-white: #FFFFFF;
  
  /* الألوان الثانوية */
  --secondary-gray: #F5F5F5;
  --secondary-dark-gray: #666666;
  --secondary-light-gray: #E0E0E0;
  
  /* ألوان الحالة */
  --success-green: #28A745;
  --warning-orange: #FFC107;
  --error-red: #DC3545;
  --info-blue: #17A2B8;
  
  /* الوضع الليلي */
  --dark-bg: #121212;
  --dark-surface: #1E1E1E;
  --dark-text: #FFFFFF;
  --dark-text-secondary: #B3B3B3;
}
```

#### استخدام الألوان
```css
/* الأزرار الأساسية */
.btn-primary {
  background-color: var(--primary-red);
  color: var(--primary-white);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
}

/* الأزرار الثانوية */
.btn-secondary {
  background-color: transparent;
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
  border-radius: 8px;
  padding: 10px 22px;
  font-weight: 600;
}

/* حالات النجاح والخطأ */
.alert-success {
  background-color: var(--success-green);
  color: var(--primary-white);
  padding: 16px;
  border-radius: 8px;
}

.alert-error {
  background-color: var(--error-red);
  color: var(--primary-white);
  padding: 16px;
  border-radius: 8px;
}
```

### 2.2 التصميم المتجاوب

#### نقاط الكسر (Breakpoints)
```css
/* الهواتف الصغيرة */
@media (max-width: 576px) {
  .container {
    padding: 16px;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* الأجهزة اللوحية */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
    margin: 0 auto;
  }
  
  .grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }
}

/* أجهزة سطح المكتب */
@media (min-width: 1024px) {
  .container {
    max-width: 1200px;
  }
  
  .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### 2.3 مكونات الواجهة القياسية

#### البطاقات (Cards)
```css
.card {
  background: var(--primary-white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  border-bottom: 1px solid var(--secondary-light-gray);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary-black);
  margin: 0;
}
```

#### النماذج (Forms)
```css
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--primary-black);
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--secondary-light-gray);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 3px rgba(220, 20, 60, 0.1);
}

.form-input.error {
  border-color: var(--error-red);
}

.form-error {
  color: var(--error-red);
  font-size: 0.875rem;
  margin-top: 4px;
}
```

## 3. معايير الاختبار

### 3.1 اختبارات الوحدة (Unit Tests)

#### مثال على اختبار دالة
```javascript
// tests/services/auth.service.test.js
const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const AuthService = require('../../src/services/auth.service');
const bcrypt = require('bcrypt');

describe('AuthService', () => {
  let authService;
  
  beforeEach(() => {
    authService = new AuthService();
  });
  
  describe('hashPassword', () => {
    it('should hash password correctly', async () => {
      const password = 'TestPassword123!';
      const hashedPassword = await authService.hashPassword(password);
      
      expect(hashedPassword).toBeDefined();
      expect(hashedPassword).not.toBe(password);
      expect(await bcrypt.compare(password, hashedPassword)).toBe(true);
    });
    
    it('should throw error for empty password', async () => {
      await expect(authService.hashPassword('')).rejects.toThrow('Password cannot be empty');
    });
  });
  
  describe('validateUserData', () => {
    it('should validate correct user data', () => {
      const userData = {
        email: '<EMAIL>',
        phone: '+966501234567',
        arabicName: 'أحمد محمد',
        englishName: 'Ahmed Mohammed'
      };
      
      const result = authService.validateUserData(userData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    
    it('should reject invalid email', () => {
      const userData = {
        email: 'invalid-email',
        phone: '+966501234567',
        arabicName: 'أحمد محمد',
        englishName: 'Ahmed Mohammed'
      };
      
      const result = authService.validateUserData(userData);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });
  });
});
```

### 3.2 اختبارات التكامل (Integration Tests)

#### مثال على اختبار API
```javascript
// tests/integration/auth.integration.test.js
const request = require('supertest');
const app = require('../../src/app');
const db = require('../../src/config/database');

describe('Auth API Integration Tests', () => {
  beforeEach(async () => {
    await db.migrate.latest();
    await db.seed.run();
  });
  
  afterEach(async () => {
    await db.migrate.rollback();
  });
  
  describe('POST /api/v1/auth/register', () => {
    it('should register new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        phone: '+966501234567',
        arabicName: 'مستخدم جديد',
        englishName: 'New User',
        password: 'SecurePass123!'
      };
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(201);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.password).toBeUndefined();
    });
    
    it('should reject duplicate email', async () => {
      const userData = {
        email: '<EMAIL>', // موجود في البذور
        phone: '+966501234567',
        arabicName: 'مستخدم جديد',
        englishName: 'New User',
        password: 'SecurePass123!'
      };
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(userData)
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Email already exists');
    });
  });
});
```

### 3.3 اختبارات الأداء

#### مثال على اختبار الحمولة
```javascript
// tests/performance/load.test.js
const autocannon = require('autocannon');

describe('Performance Tests', () => {
  it('should handle 100 concurrent users for login', async () => {
    const result = await autocannon({
      url: 'http://localhost:3000/api/v1/auth/login',
      connections: 100,
      duration: 30,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'TestPassword123!'
      })
    });
    
    expect(result.errors).toBe(0);
    expect(result.timeouts).toBe(0);
    expect(result.latency.average).toBeLessThan(500); // أقل من 500ms
    expect(result.requests.average).toBeGreaterThan(50); // أكثر من 50 طلب/ثانية
  });
});
```

## 4. معايير التوثيق

### 4.1 توثيق API

#### مثال على توثيق endpoint
```yaml
# docs/api/auth.yaml
/api/v1/auth/register:
  post:
    summary: تسجيل مستخدم جديد
    description: إنشاء حساب جديد للمستخدم مع التحقق من البيانات
    tags:
      - Authentication
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - email
              - phone
              - arabicName
              - englishName
              - password
            properties:
              email:
                type: string
                format: email
                example: "<EMAIL>"
              phone:
                type: string
                pattern: "^\\+966[0-9]{9}$"
                example: "+966501234567"
              arabicName:
                type: string
                minLength: 2
                maxLength: 100
                example: "أحمد محمد"
              englishName:
                type: string
                minLength: 2
                maxLength: 100
                example: "Ahmed Mohammed"
              password:
                type: string
                minLength: 8
                example: "SecurePass123!"
    responses:
      201:
        description: تم إنشاء الحساب بنجاح
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: "تم إنشاء الحساب بنجاح"
                data:
                  type: object
                  properties:
                    user:
                      $ref: '#/components/schemas/User'
                    token:
                      type: string
      400:
        description: بيانات غير صحيحة
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Error'
```

### 4.2 توثيق قاعدة البيانات

#### مثال على توثيق جدول
```markdown
## جدول المستخدمين (users)

### الوصف
يحتوي على بيانات جميع المستخدمين المسجلين في النظام

### الحقول

| الحقل | النوع | الوصف | القيود |
|-------|-------|--------|---------|
| id | SERIAL | المعرف الفريد | PRIMARY KEY |
| email | VARCHAR(255) | البريد الإلكتروني | UNIQUE, NOT NULL |
| phone | VARCHAR(20) | رقم الهاتف | UNIQUE, NOT NULL |
| password_hash | VARCHAR(255) | كلمة المرور المشفرة | NOT NULL |
| arabic_name | VARCHAR(100) | الاسم العربي | NOT NULL |
| english_name | VARCHAR(100) | الاسم الإنجليزي | NOT NULL |
| birth_date | DATE | تاريخ الميلاد | NULL |
| qualification | VARCHAR(50) | المؤهل العلمي | NOT NULL |
| specialization | VARCHAR(100) | التخصص | NULL |
| university | VARCHAR(200) | الجامعة/مكان العمل | NULL |
| profile_image | VARCHAR(500) | رابط الصورة الشخصية | NULL |
| status | VARCHAR(20) | حالة الحساب | DEFAULT 'active' |
| created_at | TIMESTAMP | تاريخ الإنشاء | DEFAULT CURRENT_TIMESTAMP |
| updated_at | TIMESTAMP | تاريخ آخر تحديث | DEFAULT CURRENT_TIMESTAMP |

### الفهارس
- `idx_users_email` على حقل email
- `idx_users_phone` على حقل phone
- `idx_users_qualification_specialization` على حقلي qualification و specialization

### العلاقات
- `user_subscriptions.user_id` → `users.id`
- `session_attendance.user_id` → `users.id`
- `payments.user_id` → `users.id`
```

## 5. معايير الأمان والخصوصية

### 5.1 حماية البيانات الشخصية

#### تصنيف البيانات
```javascript
// تصنيف البيانات حسب مستوى الحساسية
const DATA_CLASSIFICATION = {
  PUBLIC: {
    level: 1,
    fields: ['arabicName', 'englishName', 'specialization'],
    encryption: false,
    logging: true
  },
  INTERNAL: {
    level: 2,
    fields: ['email', 'university', 'qualification'],
    encryption: false,
    logging: true,
    accessControl: true
  },
  CONFIDENTIAL: {
    level: 3,
    fields: ['phone', 'birthDate'],
    encryption: true,
    logging: true,
    accessControl: true,
    auditTrail: true
  },
  RESTRICTED: {
    level: 4,
    fields: ['passwordHash', 'paymentInfo'],
    encryption: true,
    logging: false, // لا نسجل هذه البيانات
    accessControl: true,
    auditTrail: true,
    specialPermissions: true
  }
};
```

### 5.2 سجل التدقيق (Audit Trail)

#### تسجيل العمليات الحساسة
```javascript
const auditLogger = {
  logUserAction: (userId, action, details, ipAddress) => {
    const auditEntry = {
      userId,
      action,
      details,
      ipAddress,
      userAgent: details.userAgent,
      timestamp: new Date().toISOString(),
      sessionId: details.sessionId
    };
    
    // حفظ في قاعدة بيانات منفصلة للتدقيق
    db.audit_logs.insert(auditEntry);
  },
  
  logDataAccess: (userId, dataType, recordId, operation) => {
    const accessEntry = {
      userId,
      dataType,
      recordId,
      operation, // READ, UPDATE, DELETE
      timestamp: new Date().toISOString()
    };
    
    db.data_access_logs.insert(accessEntry);
  }
};
```

---

هذا الدليل يوفر إطار عمل شامل لضمان جودة التطوير والالتزام بأفضل الممارسات في جميع جوانب المشروع.
