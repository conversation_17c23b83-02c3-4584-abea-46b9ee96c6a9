import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Settings state
  bool _notificationsEnabled = true;
  bool _sessionReminders = true;
  bool _importantUpdates = true;
  bool _achievementNotifications = false;
  bool _darkModeEnabled = false;
  bool _biometricEnabled = false;
  String _selectedLanguage = 'العربية';
  String _selectedTheme = 'تلقائي';
  double _fontSize = 16.0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: const Color(AppColors.primaryRed),
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                'الإعدادات',
                style: TextStyle(
                  color: Color(AppColors.primaryWhite),
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(AppColors.primaryRed),
                      Color(AppColors.darkRed),
                      Color(AppColors.primaryBlack),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background Pattern
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: Container(
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/settings_bg.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    // Content
                    Center(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60),
                            Container(
                              padding: const EdgeInsets.all(AppSizes.paddingL),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                              ),
                              child: const Icon(
                                Icons.settings,
                                size: 50,
                                color: Color(AppColors.primaryWhite),
                              ),
                            ),
                            const SizedBox(height: AppSizes.paddingM),
                            Text(
                              'تخصيص تجربتك',
                              style: TextStyle(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Settings Content
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  children: [
                    // Notifications Section
                    _buildSettingsSection(
                      title: 'الإشعارات',
                      icon: Icons.notifications,
                      children: [
                        _buildSwitchTile(
                          title: 'تفعيل الإشعارات',
                          subtitle: 'تلقي جميع الإشعارات',
                          value: _notificationsEnabled,
                          onChanged: (value) {
                            setState(() {
                              _notificationsEnabled = value;
                            });
                          },
                        ),
                        _buildSwitchTile(
                          title: 'تذكيرات الجلسات',
                          subtitle: 'تذكيرات قبل بداية الجلسات',
                          value: _sessionReminders,
                          onChanged: (value) {
                            setState(() {
                              _sessionReminders = value;
                            });
                          },
                        ),
                        _buildSwitchTile(
                          title: 'التحديثات المهمة',
                          subtitle: 'إشعارات التحديثات الهامة',
                          value: _importantUpdates,
                          onChanged: (value) {
                            setState(() {
                              _importantUpdates = value;
                            });
                          },
                        ),
                        _buildSwitchTile(
                          title: 'إشعارات الإنجازات',
                          subtitle: 'إشعارات عند تحقيق إنجازات جديدة',
                          value: _achievementNotifications,
                          onChanged: (value) {
                            setState(() {
                              _achievementNotifications = value;
                            });
                          },
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Appearance Section
                    _buildSettingsSection(
                      title: 'المظهر',
                      icon: Icons.palette,
                      children: [
                        _buildDropdownTile(
                          title: 'اللغة',
                          subtitle: 'اختر لغة التطبيق',
                          value: _selectedLanguage,
                          items: ['العربية', 'English'],
                          onChanged: (value) {
                            setState(() {
                              _selectedLanguage = value!;
                            });
                          },
                        ),
                        _buildDropdownTile(
                          title: 'المظهر',
                          subtitle: 'اختر مظهر التطبيق',
                          value: _selectedTheme,
                          items: ['فاتح', 'داكن', 'تلقائي'],
                          onChanged: (value) {
                            setState(() {
                              _selectedTheme = value!;
                            });
                          },
                        ),
                        _buildSliderTile(
                          title: 'حجم الخط',
                          subtitle: 'تخصيص حجم النص',
                          value: _fontSize,
                          min: 12.0,
                          max: 24.0,
                          divisions: 6,
                          onChanged: (value) {
                            setState(() {
                              _fontSize = value;
                            });
                          },
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Security Section
                    _buildSettingsSection(
                      title: 'الأمان والخصوصية',
                      icon: Icons.security,
                      children: [
                        _buildSwitchTile(
                          title: 'المصادقة البيومترية',
                          subtitle: 'استخدام بصمة الإصبع أو الوجه',
                          value: _biometricEnabled,
                          onChanged: (value) {
                            setState(() {
                              _biometricEnabled = value;
                            });
                          },
                        ),
                        _buildActionTile(
                          title: 'تغيير كلمة المرور',
                          subtitle: 'تحديث كلمة مرور الحساب',
                          icon: Icons.lock,
                          onTap: _changePassword,
                        ),
                        _buildActionTile(
                          title: 'إدارة البيانات',
                          subtitle: 'تصدير أو حذف بياناتك',
                          icon: Icons.data_usage,
                          onTap: _manageData,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Support Section
                    _buildSettingsSection(
                      title: 'الدعم والمساعدة',
                      icon: Icons.help,
                      children: [
                        _buildActionTile(
                          title: 'مركز المساعدة',
                          subtitle: 'الأسئلة الشائعة والدعم',
                          icon: Icons.help_center,
                          onTap: _openHelpCenter,
                        ),
                        _buildActionTile(
                          title: 'تواصل معنا',
                          subtitle: 'إرسال رسالة للدعم الفني',
                          icon: Icons.contact_support,
                          onTap: _contactSupport,
                        ),
                        _buildActionTile(
                          title: 'تقييم التطبيق',
                          subtitle: 'شاركنا رأيك في التطبيق',
                          icon: Icons.star_rate,
                          onTap: _rateApp,
                        ),
                        _buildActionTile(
                          title: 'حول التطبيق',
                          subtitle: 'معلومات الإصدار والمطورين',
                          icon: Icons.info,
                          onTap: _showAbout,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Logout Section
                    _buildDangerSection(),
                    
                    const SizedBox(height: AppSizes.paddingXL),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingL),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                  const Color(AppColors.primaryBlack).withValues(alpha: 0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppSizes.radiusL),
                topRight: Radius.circular(AppSizes.radiusL),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(AppColors.primaryRed),
                        const Color(AppColors.darkRed),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(AppColors.primaryWhite),
                    size: AppSizes.iconM,
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppColors.primaryBlack),
                  ),
                ),
              ],
            ),
          ),
          
          // Section Content
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: Color(AppColors.primaryBlack),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Color(AppColors.grey),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(AppColors.primaryRed),
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: Color(AppColors.primaryBlack),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Color(AppColors.grey),
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        items: items.map((item) {
          return DropdownMenuItem(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
        underline: const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      children: [
        ListTile(
          title: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Color(AppColors.primaryBlack),
            ),
          ),
          subtitle: Text(
            subtitle,
            style: const TextStyle(
              color: Color(AppColors.grey),
            ),
          ),
          trailing: Text(
            '${value.round()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(AppColors.primaryRed),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
            activeColor: const Color(AppColors.primaryRed),
          ),
        ),
      ],
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppSizes.paddingS),
        decoration: BoxDecoration(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSizes.radiusM),
        ),
        child: Icon(
          icon,
          color: const Color(AppColors.primaryRed),
          size: AppSizes.iconM,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: Color(AppColors.primaryBlack),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Color(AppColors.grey),
        ),
      ),
      trailing: const Icon(
        Icons.arrow_forward_ios,
        color: Color(AppColors.grey),
        size: AppSizes.iconS,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDangerSection() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(
          color: const Color(AppColors.error).withValues(alpha: 0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.error).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              decoration: BoxDecoration(
                color: const Color(AppColors.error).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: const Icon(
                Icons.logout,
                color: Color(AppColors.error),
                size: AppSizes.iconM,
              ),
            ),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(AppColors.error),
              ),
            ),
            subtitle: const Text(
              'الخروج من الحساب الحالي',
              style: TextStyle(
                color: Color(AppColors.grey),
              ),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Color(AppColors.error),
              size: AppSizes.iconS,
            ),
            onTap: _logout,
          ),
          const Divider(height: 1),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              decoration: BoxDecoration(
                color: const Color(AppColors.error).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: const Icon(
                Icons.delete_forever,
                color: Color(AppColors.error),
                size: AppSizes.iconM,
              ),
            ),
            title: const Text(
              'حذف الحساب',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(AppColors.error),
              ),
            ),
            subtitle: const Text(
              'حذف الحساب نهائياً (لا يمكن التراجع)',
              style: TextStyle(
                color: Color(AppColors.grey),
              ),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: Color(AppColors.error),
              size: AppSizes.iconS,
            ),
            onTap: _deleteAccount,
          ),
        ],
      ),
    );
  }

  void _changePassword() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _manageData() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'إدارة البيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingL),
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تصدير البيانات'),
              subtitle: const Text('تحميل نسخة من بياناتك'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('حذف البيانات'),
              subtitle: const Text('حذف جميع البيانات المحفوظة'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _openHelpCenter() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح مركز المساعدة...'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح نافذة الدعم الفني...'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _rateApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح متجر التطبيقات للتقييم...'),
        backgroundColor: Color(AppColors.success),
      ),
    );
  }

  void _showAbout() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(AppColors.primaryRed),
              const Color(AppColors.darkRed),
            ],
          ),
          borderRadius: BorderRadius.circular(AppSizes.radiusM),
        ),
        child: const Icon(
          Icons.medical_services,
          color: Color(AppColors.primaryWhite),
          size: 30,
        ),
      ),
      children: [
        const Text(AppConstants.conferenceDescription),
        const SizedBox(height: AppSizes.paddingM),
        const Text('تطوير: فريق IDEC 2025'),
      ],
    );
  }

  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Perform logout
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسجيل الخروج بنجاح'),
                  backgroundColor: Color(AppColors.success),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(AppColors.error),
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _deleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: const Text(
          'تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بياناتك نهائياً.\n\nهل أنت متأكد؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Show confirmation dialog
              _showDeleteConfirmation();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(AppColors.error),
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اكتب "حذف الحساب" للتأكيد:'),
            const SizedBox(height: AppSizes.paddingM),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: 'حذف الحساب',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text == 'حذف الحساب') {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الحساب'),
                    backgroundColor: Color(AppColors.error),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(AppColors.error),
            ),
            child: const Text('تأكيد الحذف'),
          ),
        ],
      ),
    );
  }
}
