const express = require('express');
const { body, param, query } = require('express-validator');
const socialController = require('../controllers/socialController');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// All routes require authentication
router.use(auth.authenticate);

// Send connection request
router.post('/connections/request',
  [
    body('receiverId').notEmpty().withMessage('Receiver ID is required'),
    body('message').optional().isLength({ max: 500 }).withMessage('Message must be less than 500 characters')
  ],
  validation.handleValidationErrors,
  socialController.sendConnectionRequest
);

// Respond to connection request
router.put('/connections/:id/respond',
  [
    param('id').notEmpty().withMessage('Connection ID is required'),
    body('response').isIn(['ACCEPTED', 'REJECTED']).withMessage('Response must be ACCEPTED or REJECTED')
  ],
  validation.handleValidationErrors,
  socialController.respondToConnectionRequest
);

// Get user's connections
router.get('/connections',
  [
    query('status').optional().isIn(['PENDING', 'ACCEPTED', 'REJECTED']).withMessage('Invalid status')
  ],
  validation.handleValidationErrors,
  socialController.getUserConnections
);

// Get pending connection requests
router.get('/connections/pending', socialController.getPendingRequests);

// Remove connection
router.delete('/connections/:id',
  [
    param('id').notEmpty().withMessage('Connection ID is required')
  ],
  validation.handleValidationErrors,
  socialController.removeConnection
);

// Search users
router.get('/users/search',
  [
    query('q').isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
    query('qualification').optional().isString(),
    query('specialization').optional().isString()
  ],
  validation.handleValidationErrors,
  socialController.searchUsers
);

// Get connection suggestions
router.get('/suggestions',
  [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
  ],
  validation.handleValidationErrors,
  socialController.getConnectionSuggestions
);

// Get user profile for networking
router.get('/users/:id/profile',
  [
    param('id').notEmpty().withMessage('User ID is required')
  ],
  validation.handleValidationErrors,
  socialController.getUserProfile
);

// Get user's social stats
router.get('/stats', socialController.getUserSocialStats);

// Get networking activity feed
router.get('/feed',
  [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
  ],
  validation.handleValidationErrors,
  socialController.getNetworkingFeed
);

module.exports = router;
