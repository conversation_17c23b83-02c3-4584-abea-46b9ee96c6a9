# المخططات التقنية لمشروع IDEC 2025

## 1. مخطط معمارية النظام العامة

```
┌─────────────────────────────────────────────────────────────┐
│                    IDEC 2025 System Architecture            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Flutter App   │    │  Admin Dashboard │                │
│  │   (Mobile)      │    │   (React Web)   │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────────┬───────────┘                        │
│                       │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                API Gateway                              │ │
│  │              (Node.js + Express)                        │ │
│  └─────────────────────────────────────────────────────────┘ │
│                       │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Business Logic Layer                     │ │
│  │         ┌─────────┐ ┌─────────┐ ┌─────────┐            │ │
│  │         │  Auth   │ │ Payment │ │ Content │            │ │
│  │         │ Service │ │ Service │ │ Service │            │ │
│  │         └─────────┘ └─────────┘ └─────────┘            │ │
│  └─────────────────────────────────────────────────────────┘ │
│                       │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Data Layer                               │ │
│  │    ┌─────────────┐    ┌─────────────┐                  │ │
│  │    │ PostgreSQL  │    │    Redis    │                  │ │
│  │    │ (Main DB)   │    │  (Cache)    │                  │ │
│  │    └─────────────┘    └─────────────┘                  │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                External Services                        │ │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐      │ │
│  │  │Payment  │ │   SMS   │ │WhatsApp │ │  File   │      │ │
│  │  │Gateway  │ │Service  │ │   API   │ │Storage  │      │ │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. مخطط قاعدة البيانات (ERD)

### الجداول الرئيسية والعلاقات

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│     Users       │     │   User_Roles    │     │     Roles       │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id (PK)         │────┐│ user_id (FK)    │┌────│ id (PK)         │
│ email           │    ││ role_id (FK)    ││    │ name            │
│ phone           │    │└─────────────────┘│    │ permissions     │
│ password_hash   │    │                  │    │ created_at      │
│ arabic_name     │    │                  └────┤                 │
│ english_name    │    │                       └─────────────────┘
│ birth_date      │    │
│ qualification   │    │
│ specialization  │    │
│ university      │    │
│ profile_image   │    │
│ status          │    │
│ created_at      │    │
└─────────────────┘    │
                       │
┌─────────────────┐    │    ┌─────────────────┐
│ Subscriptions   │    │    │    Payments     │
├─────────────────┤    │    ├─────────────────┤
│ id (PK)         │    │    │ id (PK)         │
│ user_id (FK)    │────┘    │ user_id (FK)    │
│ status          │         │ amount          │
│ approved_by     │         │ currency        │
│ approved_at     │         │ payment_method  │
│ payment_deadline│         │ gateway_ref     │
│ notes           │         │ purpose         │
│ created_at      │         │ status          │
└─────────────────┘         │ created_at      │
                            └─────────────────┘

┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    Sessions     │     │Session_Attendance│     │    Speakers     │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ id (PK)         │────┐│ id (PK)         │     │ id (PK)         │
│ title_ar        │    ││ user_id (FK)    │     │ name_ar         │
│ title_en        │    ││ session_id (FK) │────┐│ name_en         │
│ description     │    │└─────────────────┘    ││ bio             │
│ start_time      │    │                       ││ image           │
│ end_time        │    │                       ││ linkedin        │
│ room_id         │    │                       ││ created_at      │
│ speaker_id (FK) │────┼───────────────────────┘└─────────────────┘
│ session_type    │    │
│ cme_hours       │    │
│ qr_code         │    │
│ created_at      │    │
└─────────────────┘    │
                       │
┌─────────────────┐    │    ┌─────────────────┐
│     Courses     │    │    │Course_Enrollments│
├─────────────────┤    │    ├─────────────────┤
│ id (PK)         │    │    │ id (PK)         │
│ title_ar        │    │    │ user_id (FK)    │
│ title_en        │    │    │ course_id (FK)  │
│ description     │    │    │ enrollment_date │
│ price           │    │    │ payment_status  │
│ max_participants│    │    │ completion_date │
│ start_date      │    │    │ certificate_url │
│ instructor_id   │    │    │ created_at      │
│ status          │    │    └─────────────────┘
│ created_at      │    │
└─────────────────┘    │
                       │
┌─────────────────┐    │
│   Notifications │    │
├─────────────────┤    │
│ id (PK)         │    │
│ user_id (FK)    │────┘
│ title           │
│ message         │
│ type            │
│ read_at         │
│ created_at      │
└─────────────────┘
```

## 3. مخطط تدفق المستخدم (User Flow)

### تدفق التسجيل والاشتراك

```
Start
  │
  ▼
┌─────────────────┐
│ User Registration│
│ (Phone + Email) │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│   OTP Verify    │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Complete Profile│
│ + Upload Docs   │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│Submit Conference│
│ Subscription    │
└─────────────────┘
  │
  ▼
┌─────────────────┐    No    ┌─────────────────┐
│ Admin Review    │─────────▶│ Rejection       │
│ Documents       │          │ Notification    │
└─────────────────┘          └─────────────────┘
  │ Yes
  ▼
┌─────────────────┐
│ Payment Enable  │
│ Notification    │
└─────────────────┘
  │
  ▼
┌─────────────────┐    Timeout  ┌─────────────────┐
│ User Payment    │────────────▶│ Move to Pending │
│ Process         │             │ List            │
└─────────────────┘             └─────────────────┘
  │ Success
  ▼
┌─────────────────┐
│ Active Member   │
│ QR Generation   │
└─────────────────┘
  │
  ▼
End
```

### تدفق حضور الجلسات

```
Start (Conference Day)
  │
  ▼
┌─────────────────┐
│ Scan Entry QR   │
│ at Main Gate    │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Browse Sessions │
│ in My Agenda    │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Go to Session   │
│ Room            │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Scan Session QR │
│ for Check-in    │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Attend Session  │
│ + Take Notes    │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Session Ends    │
│ Auto Check-out  │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ Rate Session    │
│ & Speaker       │
└─────────────────┘
  │
  ▼
┌─────────────────┐
│ CME Hours       │
│ Updated         │
└─────────────────┘
  │
  ▼
End
```

## 4. مخطط API Endpoints

### Authentication Endpoints
```
POST   /api/v1/auth/register
POST   /api/v1/auth/verify-otp
POST   /api/v1/auth/login
POST   /api/v1/auth/refresh-token
POST   /api/v1/auth/logout
POST   /api/v1/auth/forgot-password
POST   /api/v1/auth/reset-password
```

### User Management Endpoints
```
GET    /api/v1/users/profile
PUT    /api/v1/users/profile
POST   /api/v1/users/upload-document
GET    /api/v1/users/subscription-status
POST   /api/v1/users/submit-subscription
GET    /api/v1/users/payment-history
GET    /api/v1/users/certificates
```

### Conference Endpoints
```
GET    /api/v1/conference/sessions
GET    /api/v1/conference/sessions/:id
GET    /api/v1/conference/speakers
GET    /api/v1/conference/speakers/:id
POST   /api/v1/conference/sessions/:id/attend
GET    /api/v1/conference/my-agenda
POST   /api/v1/conference/my-agenda/add
DELETE /api/v1/conference/my-agenda/:sessionId
POST   /api/v1/conference/sessions/:id/rate
GET    /api/v1/conference/sessions/:id/qr
```

### Courses Endpoints
```
GET    /api/v1/courses
GET    /api/v1/courses/:id
POST   /api/v1/courses/:id/enroll
GET    /api/v1/courses/my-courses
POST   /api/v1/courses/:id/cancel
GET    /api/v1/courses/:id/certificate
```

### Payment Endpoints
```
POST   /api/v1/payments/initiate
POST   /api/v1/payments/verify
GET    /api/v1/payments/history
POST   /api/v1/payments/refund-request
```

### Admin Endpoints
```
GET    /api/v1/admin/users
PUT    /api/v1/admin/users/:id/status
GET    /api/v1/admin/subscriptions
PUT    /api/v1/admin/subscriptions/:id/approve
PUT    /api/v1/admin/subscriptions/:id/reject
GET    /api/v1/admin/payments
POST   /api/v1/admin/payments/manual
GET    /api/v1/admin/reports/subscriptions
GET    /api/v1/admin/reports/financial
GET    /api/v1/admin/reports/attendance
POST   /api/v1/admin/notifications/send
```

## 5. مخطط الأمان والصلاحيات

### نظام الأدوار والصلاحيات

```
┌─────────────────────────────────────────────────────────────┐
│                    Security Architecture                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   JWT Tokens    │    │  Role-Based     │                │
│  │                 │    │  Access Control │                │
│  │ • Access Token  │    │                 │                │
│  │ • Refresh Token │    │ • Super Admin   │                │
│  │ • Short Expiry  │    │ • Admin         │                │
│  └─────────────────┘    │ • Committee     │                │
│                         │ • Speaker       │                │
│                         │ • Participant   │                │
│                         │ • Visitor       │                │
│                         └─────────────────┘                │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                API Security Layers                      │ │
│  │                                                         │ │
│  │  1. Rate Limiting (100 req/min per IP)                 │ │
│  │  2. Input Validation & Sanitization                    │ │
│  │  3. JWT Token Verification                             │ │
│  │  4. Role-Based Permission Check                        │ │
│  │  5. Resource Ownership Validation                      │ │
│  │  6. Audit Logging                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Data Protection                          │ │
│  │                                                         │ │
│  │  • HTTPS/TLS 1.3 Encryption                           │ │
│  │  • Password Hashing (bcrypt)                          │ │
│  │  • Sensitive Data Encryption at Rest                  │ │
│  │  • PII Data Anonymization                             │ │
│  │  • GDPR Compliance                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### مصفوفة الصلاحيات

| الدور | عرض المستخدمين | موافقة الطلبات | إدارة المدفوعات | إدارة المحتوى | إرسال الإشعارات |
|-------|----------------|----------------|-----------------|---------------|-----------------|
| Super Admin | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ❌ | ✅ | ✅ |
| Admission Committee | ✅ | ✅ | ❌ | ❌ | ❌ |
| Accountant | ✅ | ❌ | ✅ | ❌ | ❌ |
| Scientific Committee | ❌ | ❌ | ❌ | ✅ | ❌ |
| Media Committee | ❌ | ❌ | ❌ | ✅ | ✅ |
| Speaker | ❌ | ❌ | ❌ | ✅* | ❌ |

*محدود بالمحتوى الخاص بهم فقط

## 6. مخطط النشر والبنية التحتية

### بيئات النشر

```
┌─────────────────────────────────────────────────────────────┐
│                    Deployment Architecture                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Development   │    │     Staging     │                │
│  │   Environment   │    │   Environment   │                │
│  │                 │    │                 │                │
│  │ • Local DB      │    │ • Test DB       │                │
│  │ • Mock Services │    │ • Real Services │                │
│  │ • Hot Reload    │    │ • Full Testing  │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Production Environment                   │ │
│  │                                                         │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │Load Balancer│  │   App       │  │   Database  │    │ │
│  │  │   (Nginx)   │  │  Servers    │  │   Cluster   │    │ │
│  │  │             │  │  (Node.js)  │  │(PostgreSQL) │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  │                                                         │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │ │
│  │  │    CDN      │  │    Cache    │  │   Backup    │    │ │
│  │  │(CloudFlare) │  │   (Redis)   │  │   System    │    │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘    │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Monitoring & Logging                     │ │
│  │                                                         │ │
│  │  • Application Performance Monitoring (APM)            │ │
│  │  • Error Tracking (Sentry)                            │ │
│  │  • Log Aggregation (ELK Stack)                        │ │
│  │  • Uptime Monitoring                                  │ │
│  │  • Security Monitoring                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 7. مخطط التكامل مع الخدمات الخارجية

### خدمات الدفع

```
┌─────────────────────────────────────────────────────────────┐
│                Payment Integration Flow                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  User Initiates Payment                                     │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐                                        │
│  │   App Backend   │                                        │
│  │                 │                                        │
│  │ 1. Create Order │                                        │
│  │ 2. Generate URL │                                        │
│  └─────────────────┘                                        │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   PayPal API    │    │   Stripe API    │                │
│  │                 │    │                 │                │
│  │ • Secure Form   │    │ • Secure Form   │                │
│  │ • Process       │    │ • Process       │                │
│  │ • Callback      │    │ • Webhook       │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────────┬───────────┘                        │
│                       │                                    │
│                       ▼                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Payment Verification                       │ │
│  │                                                         │ │
│  │ 1. Verify Transaction with Gateway                     │ │
│  │ 2. Update User Status                                  │ │
│  │ 3. Generate Receipt                                    │ │
│  │ 4. Send Confirmation                                   │ │
│  │ 5. Generate QR Code                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### خدمات الرسائل

```
┌─────────────────────────────────────────────────────────────┐
│              Notification Service Integration               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Trigger Event (Registration, Approval, etc.)              │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐                                        │
│  │ Notification    │                                        │
│  │ Service         │                                        │
│  │                 │                                        │
│  │ 1. Get Template │                                        │
│  │ 2. Personalize  │                                        │
│  │ 3. Route Message│                                        │
│  └─────────────────┘                                        │
│           │                                                 │
│           ▼                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   SMS Service   │    │  WhatsApp API   │                │
│  │   (Twilio)      │    │   (Business)    │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────────┬───────────┘                        │
│                       │                                    │
│                       ▼                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Push Notifications                           │ │
│  │          (Firebase Cloud Messaging)                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

هذه المخططات التقنية توفر رؤية شاملة لبنية النظام وتساعد في فهم التفاعلات بين المكونات المختلفة.
