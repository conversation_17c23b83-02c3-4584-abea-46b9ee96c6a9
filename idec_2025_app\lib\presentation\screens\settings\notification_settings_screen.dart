import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/notification_provider.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settingsProvider = provider.Provider.of<NotificationSettingsProvider>(
        context, 
        listen: false
      );
      settingsProvider.loadSettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إعدادات الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _resetToDefaults,
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تعيين',
          ),
        ],
      ),
      body: provider.Consumer<NotificationSettingsProvider>(
        builder: (context, settings, child) {
          return ListView(
            padding: const EdgeInsets.all(AppSizes.paddingL),
            children: [
              // Header
              _buildSectionHeader(
                'أنواع الإشعارات',
                'اختر الإشعارات التي تريد استقبالها',
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Notification Types
              _buildNotificationCard([
                _buildSwitchTile(
                  title: 'تذكير الجلسات',
                  subtitle: 'تذكير قبل بدء الجلسات المسجلة',
                  icon: Icons.event,
                  value: settings.sessionReminders,
                  onChanged: settings.setSessionReminders,
                ),
                _buildSwitchTile(
                  title: 'تذكير الدورات',
                  subtitle: 'تذكير قبل بدء الدورات التدريبية',
                  icon: Icons.school,
                  value: settings.courseReminders,
                  onChanged: settings.setCourseReminders,
                ),
                _buildSwitchTile(
                  title: 'أخبار المؤتمر',
                  subtitle: 'إشعارات الأخبار والإعلانات الجديدة',
                  icon: Icons.newspaper,
                  value: settings.newsNotifications,
                  onChanged: settings.setNewsNotifications,
                ),
                _buildSwitchTile(
                  title: 'الشبكة الاجتماعية',
                  subtitle: 'طلبات التواصل والرسائل الجديدة',
                  icon: Icons.people,
                  value: settings.socialNotifications,
                  onChanged: settings.setSocialNotifications,
                ),
                _buildSwitchTile(
                  title: 'إنجازات التلعيب',
                  subtitle: 'الشارات الجديدة والنقاط المكتسبة',
                  icon: Icons.emoji_events,
                  value: settings.gamificationNotifications,
                  onChanged: settings.setGamificationNotifications,
                ),
              ]),
              
              const SizedBox(height: AppSizes.paddingXL),
              
              // Timing Settings
              _buildSectionHeader(
                'إعدادات التوقيت',
                'تخصيص أوقات التذكير',
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              _buildNotificationCard([
                _buildReminderTimeTile(settings),
              ]),
              
              const SizedBox(height: AppSizes.paddingXL),
              
              // Sound & Vibration
              _buildSectionHeader(
                'الصوت والاهتزاز',
                'إعدادات الصوت والاهتزاز للإشعارات',
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              _buildNotificationCard([
                _buildSwitchTile(
                  title: 'تفعيل الصوت',
                  subtitle: 'تشغيل صوت عند وصول الإشعارات',
                  icon: Icons.volume_up,
                  value: settings.soundEnabled,
                  onChanged: settings.setSoundEnabled,
                ),
                _buildSwitchTile(
                  title: 'تفعيل الاهتزاز',
                  subtitle: 'اهتزاز الجهاز عند وصول الإشعارات',
                  icon: Icons.vibration,
                  value: settings.vibrationEnabled,
                  onChanged: settings.setVibrationEnabled,
                ),
              ]),
              
              const SizedBox(height: AppSizes.paddingXL),
              
              // Test Notification Button
              _buildTestNotificationButton(),
              
              const SizedBox(height: AppSizes.paddingXL),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title, String subtitle) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color(AppColors.primaryBlack),
          ),
        ),
        const SizedBox(height: AppSizes.paddingXS),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppSizes.paddingS),
        decoration: BoxDecoration(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSizes.radiusM),
        ),
        child: Icon(
          icon,
          color: const Color(AppColors.primaryRed),
          size: 24,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(AppColors.primaryRed),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingL,
        vertical: AppSizes.paddingS,
      ),
    );
  }

  Widget _buildReminderTimeTile(NotificationSettingsProvider settings) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppSizes.paddingS),
        decoration: BoxDecoration(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSizes.radiusM),
        ),
        child: const Icon(
          Icons.schedule,
          color: Color(AppColors.primaryRed),
          size: 24,
        ),
      ),
      title: const Text(
        'وقت التذكير',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
      subtitle: Text(
        'التذكير قبل ${settings.reminderMinutes} دقيقة من بدء الجلسة',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 14,
        ),
      ),
      trailing: DropdownButton<int>(
        value: settings.reminderMinutes,
        onChanged: (value) {
          if (value != null) {
            settings.setReminderMinutes(value);
          }
        },
        items: [5, 10, 15, 30, 60].map((minutes) {
          return DropdownMenuItem<int>(
            value: minutes,
            child: Text('$minutes دقيقة'),
          );
        }).toList(),
        underline: const SizedBox(),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingL,
        vertical: AppSizes.paddingS,
      ),
    );
  }

  Widget _buildTestNotificationButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      child: ElevatedButton.icon(
        onPressed: _sendTestNotification,
        icon: const Icon(Icons.notifications_active),
        label: const Text(
          'إرسال إشعار تجريبي',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(AppColors.primaryRed),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingL),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
          ),
        ),
      ),
    );
  }

  void _sendTestNotification() {
    final notificationProvider = provider.Provider.of<NotificationProvider>(
      context, 
      listen: false
    );
    
    notificationProvider.sendAnnouncement(
      title: 'إشعار تجريبي',
      message: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح',
      data: {'type': 'test'},
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال الإشعار التجريبي'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد إعادة تعيين جميع إعدادات الإشعارات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              final settings = provider.Provider.of<NotificationSettingsProvider>(
                context, 
                listen: false
              );
              settings.resetToDefaults();
              Navigator.of(context).pop();
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين الإعدادات بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(color: Color(AppColors.primaryRed)),
            ),
          ),
        ],
      ),
    );
  }
}
