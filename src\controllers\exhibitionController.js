const exhibitionService = require('../services/exhibitionService');
const logger = require('../utils/logger');

class ExhibitionController {
  // Get all exhibitors
  async getExhibitors(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        category, 
        search, 
        isPremium,
        isActive = true 
      } = req.query;

      const filters = { category, search, isActive: isActive === 'true' };
      if (isPremium !== undefined) {
        filters.isPremium = isPremium === 'true';
      }

      const pagination = { page: parseInt(page), limit: parseInt(limit) };

      const result = await exhibitionService.getExhibitors(filters, pagination);

      res.json({
        success: true,
        data: result.exhibitors,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error getting exhibitors:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get exhibitors',
          code: 'GET_EXHIBITORS_ERROR'
        }
      });
    }
  }

  // Get exhibitor by ID
  async getExhibitorById(req, res) {
    try {
      const { id } = req.params;

      const exhibitor = await exhibitionService.getExhibitorById(id);

      res.json({
        success: true,
        data: exhibitor
      });
    } catch (error) {
      logger.error('Error getting exhibitor by ID:', error);
      
      if (error.message === 'Exhibitor not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Exhibitor not found',
            code: 'EXHIBITOR_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get exhibitor',
          code: 'GET_EXHIBITOR_ERROR'
        }
      });
    }
  }

  // Get all booths
  async getBooths(req, res) {
    try {
      const { hallName, search } = req.query;

      const filters = { hallName, search };

      const booths = await exhibitionService.getBooths(filters);

      res.json({
        success: true,
        data: booths
      });
    } catch (error) {
      logger.error('Error getting booths:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get booths',
          code: 'GET_BOOTHS_ERROR'
        }
      });
    }
  }

  // Visit booth
  async visitBooth(req, res) {
    try {
      const { id: boothId } = req.params;
      const { notes } = req.body;
      const userId = req.user.id;

      const visit = await exhibitionService.visitBooth(userId, boothId, notes);

      res.json({
        success: true,
        message: 'Booth visit recorded successfully',
        data: visit
      });
    } catch (error) {
      logger.error('Error visiting booth:', error);
      
      if (error.message === 'Booth not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Booth not found',
            code: 'BOOTH_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to record booth visit',
          code: 'BOOTH_VISIT_ERROR'
        }
      });
    }
  }

  // Get user's booth visits
  async getUserBoothVisits(req, res) {
    try {
      const userId = req.user.id;

      const visits = await exhibitionService.getUserBoothVisits(userId);

      res.json({
        success: true,
        data: visits
      });
    } catch (error) {
      logger.error('Error getting user booth visits:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get booth visits',
          code: 'GET_BOOTH_VISITS_ERROR'
        }
      });
    }
  }

  // Get products
  async getProducts(req, res) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        exhibitorId, 
        category, 
        search, 
        isFeatured 
      } = req.query;

      const filters = { exhibitorId, category, search };
      if (isFeatured !== undefined) {
        filters.isFeatured = isFeatured === 'true';
      }

      const pagination = { page: parseInt(page), limit: parseInt(limit) };

      const result = await exhibitionService.getProducts(filters, pagination);

      res.json({
        success: true,
        data: result.products,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error getting products:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get products',
          code: 'GET_PRODUCTS_ERROR'
        }
      });
    }
  }

  // Get special offers
  async getSpecialOffers(req, res) {
    try {
      const { exhibitorId } = req.query;

      const filters = { exhibitorId };

      const offers = await exhibitionService.getSpecialOffers(filters);

      res.json({
        success: true,
        data: offers
      });
    } catch (error) {
      logger.error('Error getting special offers:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get special offers',
          code: 'GET_OFFERS_ERROR'
        }
      });
    }
  }

  // Get exhibition statistics
  async getExhibitionStats(req, res) {
    try {
      const stats = await exhibitionService.getExhibitionStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting exhibition stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get exhibition statistics',
          code: 'GET_EXHIBITION_STATS_ERROR'
        }
      });
    }
  }

  // Search exhibition
  async searchExhibition(req, res) {
    try {
      const { q: query, category } = req.query;

      if (!query || query.length < 2) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Search query must be at least 2 characters',
            code: 'INVALID_SEARCH_QUERY'
          }
        });
      }

      const filters = { category };

      const results = await exhibitionService.searchExhibition(query, filters);

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      logger.error('Error searching exhibition:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to search exhibition',
          code: 'SEARCH_EXHIBITION_ERROR'
        }
      });
    }
  }

  // Get exhibition map/layout
  async getExhibitionMap(req, res) {
    try {
      // This would typically return a map layout with booth positions
      // For now, we'll return booths grouped by hall
      const booths = await exhibitionService.getBooths({});

      // Group booths by hall
      const hallMap = booths.reduce((acc, booth) => {
        if (!acc[booth.hallName]) {
          acc[booth.hallName] = [];
        }
        acc[booth.hallName].push(booth);
        return acc;
      }, {});

      res.json({
        success: true,
        data: {
          halls: hallMap,
          totalBooths: booths.length
        }
      });
    } catch (error) {
      logger.error('Error getting exhibition map:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get exhibition map',
          code: 'GET_EXHIBITION_MAP_ERROR'
        }
      });
    }
  }

  // Get featured content
  async getFeaturedContent(req, res) {
    try {
      const [featuredExhibitors, featuredProducts, specialOffers] = await Promise.all([
        exhibitionService.getExhibitors({ isPremium: true }, { limit: 6 }),
        exhibitionService.getProducts({ isFeatured: true }, { limit: 8 }),
        exhibitionService.getSpecialOffers({})
      ]);

      res.json({
        success: true,
        data: {
          featuredExhibitors: featuredExhibitors.exhibitors,
          featuredProducts: featuredProducts.products,
          specialOffers: specialOffers.slice(0, 5) // Limit to 5 offers
        }
      });
    } catch (error) {
      logger.error('Error getting featured content:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get featured content',
          code: 'GET_FEATURED_CONTENT_ERROR'
        }
      });
    }
  }
}

module.exports = new ExhibitionController();
