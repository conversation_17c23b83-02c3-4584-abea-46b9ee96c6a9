import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({super.key});

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scanAnimation;
  
  bool _isFlashOn = false;
  bool _isScanning = true;
  String? _scannedData;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _scanAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(AppColors.primaryBlack),
      appBar: AppBar(
        title: const Text(
          'مسح رمز QR',
          style: TextStyle(
            color: Color(AppColors.primaryWhite),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(
          color: Color(AppColors.primaryWhite),
        ),
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _isFlashOn = !_isFlashOn;
              });
            },
            icon: Icon(
              _isFlashOn ? Icons.flash_on : Icons.flash_off,
              color: const Color(AppColors.primaryWhite),
            ),
            tooltip: _isFlashOn ? 'إطفاء الفلاش' : 'تشغيل الفلاش',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Camera Preview (Mock)
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(AppColors.primaryBlack),
                  Color(AppColors.darkBlack),
                ],
              ),
            ),
            child: const Center(
              child: Text(
                'كاميرا QR Scanner\n(محاكاة)',
                style: TextStyle(
                  color: Color(AppColors.primaryWhite),
                  fontSize: 18,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          
          // Scanning Overlay
          if (_isScanning) _buildScanningOverlay(),
          
          // Result Overlay
          if (_scannedData != null) _buildResultOverlay(),
          
          // Instructions
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
              padding: const EdgeInsets.all(AppSizes.paddingL),
              decoration: BoxDecoration(
                color: const Color(AppColors.primaryBlack).withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(AppSizes.radiusL),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.qr_code_scanner,
                    color: Color(AppColors.primaryWhite),
                    size: AppSizes.iconL,
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  const Text(
                    'وجه الكاميرا نحو رمز QR',
                    style: TextStyle(
                      color: Color(AppColors.primaryWhite),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  Text(
                    'تأكد من وضوح الرمز داخل الإطار',
                    style: TextStyle(
                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          
          // Manual Input Button
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Center(
              child: ElevatedButton.icon(
                onPressed: _showManualInputDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(AppColors.primaryRed),
                  foregroundColor: const Color(AppColors.primaryWhite),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingL,
                    vertical: AppSizes.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                  ),
                ),
                icon: const Icon(Icons.keyboard),
                label: const Text('إدخال يدوي'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScanningOverlay() {
    return Stack(
      children: [
        // Dark overlay with transparent center
        Container(
          width: double.infinity,
          height: double.infinity,
          color: const Color(AppColors.primaryBlack).withValues(alpha: 0.5),
        ),
        
        // Scanning frame
        Center(
          child: Container(
            width: 250,
            height: 250,
            decoration: BoxDecoration(
              border: Border.all(
                color: const Color(AppColors.primaryRed),
                width: 3,
              ),
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
            ),
            child: Stack(
              children: [
                // Corner decorations
                ...List.generate(4, (index) {
                  return Positioned(
                    top: index < 2 ? 0 : null,
                    bottom: index >= 2 ? 0 : null,
                    left: index % 2 == 0 ? 0 : null,
                    right: index % 2 == 1 ? 0 : null,
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        border: Border(
                          top: index < 2 ? const BorderSide(
                            color: Color(AppColors.primaryWhite),
                            width: 4,
                          ) : BorderSide.none,
                          bottom: index >= 2 ? const BorderSide(
                            color: Color(AppColors.primaryWhite),
                            width: 4,
                          ) : BorderSide.none,
                          left: index % 2 == 0 ? const BorderSide(
                            color: Color(AppColors.primaryWhite),
                            width: 4,
                          ) : BorderSide.none,
                          right: index % 2 == 1 ? const BorderSide(
                            color: Color(AppColors.primaryWhite),
                            width: 4,
                          ) : BorderSide.none,
                        ),
                      ),
                    ),
                  );
                }),
                
                // Scanning line animation
                AnimatedBuilder(
                  animation: _scanAnimation,
                  builder: (context, child) {
                    return Positioned(
                      top: 250 * _scanAnimation.value - 2,
                      left: 0,
                      right: 0,
                      child: Container(
                        height: 4,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.transparent,
                              const Color(AppColors.primaryRed),
                              Colors.transparent,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(AppColors.primaryBlack).withValues(alpha: 0.9),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(AppSizes.paddingL),
          padding: const EdgeInsets.all(AppSizes.paddingL),
          decoration: BoxDecoration(
            color: const Color(AppColors.primaryWhite),
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            boxShadow: [
              BoxShadow(
                color: const Color(AppColors.primaryBlack).withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success Icon
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(AppColors.success),
                      const Color(AppColors.success).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                ),
                child: const Icon(
                  Icons.check,
                  color: Color(AppColors.primaryWhite),
                  size: AppSizes.iconXL,
                ),
              ),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Title
              Text(
                'تم المسح بنجاح!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(AppColors.primaryBlack),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Scanned Data
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: const Color(AppColors.lightGrey),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Text(
                  _scannedData ?? '',
                  style: const TextStyle(
                    color: Color(AppColors.primaryBlack),
                    fontSize: 14,
                    fontFamily: 'monospace',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setState(() {
                          _scannedData = null;
                          _isScanning = true;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(AppColors.primaryRed),
                        side: const BorderSide(color: Color(AppColors.primaryRed)),
                        padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                      ),
                      child: const Text('مسح مرة أخرى'),
                    ),
                  ),
                  
                  const SizedBox(width: AppSizes.paddingM),
                  
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _processScannedData(_scannedData!);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(AppColors.primaryRed),
                        foregroundColor: const Color(AppColors.primaryWhite),
                        padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                      ),
                      child: const Text('متابعة'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showManualInputDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدخال رمز QR يدوياً'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل محتوى رمز QR:'),
            const SizedBox(height: AppSizes.paddingM),
            TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'مثال: IDEC2025_SESSION_001',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                Navigator.of(context).pop();
                setState(() {
                  _scannedData = controller.text;
                  _isScanning = false;
                });
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _processScannedData(String data) {
    // Process the scanned QR code data
    if (data.startsWith('IDEC2025_SESSION_')) {
      _handleSessionQR(data);
    } else if (data.startsWith('IDEC2025_USER_')) {
      _handleUserQR(data);
    } else if (data.startsWith('IDEC2025_CERT_')) {
      _handleCertificateQR(data);
    } else {
      _showErrorDialog('رمز QR غير صالح');
    }
  }

  void _handleSessionQR(String data) {
    // Extract session ID from QR code
    final sessionId = data.replaceFirst('IDEC2025_SESSION_', '');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل حضور الجلسة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.event,
              color: Color(AppColors.success),
              size: AppSizes.iconXL,
            ),
            const SizedBox(height: AppSizes.paddingM),
            Text('تم تسجيل حضورك في الجلسة: $sessionId'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _handleUserQR(String data) {
    // Handle user verification QR
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم التحقق من الهوية'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.verified_user,
              color: Color(AppColors.success),
              size: AppSizes.iconXL,
            ),
            SizedBox(height: AppSizes.paddingM),
            Text('تم التحقق من هويتك بنجاح'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _handleCertificateQR(String data) {
    // Handle certificate verification QR
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحقق من الشهادة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.workspace_premium,
              color: Color(AppColors.warning),
              size: AppSizes.iconXL,
            ),
            SizedBox(height: AppSizes.paddingM),
            Text('شهادة صالحة ومعتمدة'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error,
              color: Color(AppColors.error),
              size: AppSizes.iconXL,
            ),
            const SizedBox(height: AppSizes.paddingM),
            Text(message),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
