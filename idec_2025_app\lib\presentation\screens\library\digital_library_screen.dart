import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class DigitalLibraryScreen extends StatefulWidget {
  const DigitalLibraryScreen({super.key});

  @override
  State<DigitalLibraryScreen> createState() => _DigitalLibraryScreenState();
}

class _DigitalLibraryScreenState extends State<DigitalLibraryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _rotationAnimation;
  
  String _selectedCategory = 'الكل';
  String _selectedType = 'الكل';
  String _searchQuery = '';
  
  final List<String> _categories = [
    'الكل', 'جراحة', 'تقويم', 'علاج جذور', 'أطفال', 'تجميل', 'إدارة'
  ];
  final List<String> _types = ['الكل', 'كتب', 'أبحاث', 'فيديوهات', 'عروض تقديمية'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 320,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'المكتبة الرقمية',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _rotationAnimation,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: 0.05 * _rotationAnimation.value,
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/library_pattern.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60),
                            
                            // Library Icon with Animation
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: RotationTransition(
                                turns: _rotationAnimation,
                                child: Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.library_books,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Stats Row
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('500+', 'مرجع علمي'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('100+', 'فيديو تعليمي'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('50+', 'كتاب رقمي'),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Search Bar
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Container(
                                margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                ),
                                child: TextField(
                                  onChanged: (value) {
                                    setState(() {
                                      _searchQuery = value;
                                    });
                                  },
                                  style: const TextStyle(
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'ابحث في المكتبة...',
                                    hintStyle: TextStyle(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                                    ),
                                    prefixIcon: const Icon(
                                      Icons.search,
                                      color: Color(AppColors.primaryWhite),
                                    ),
                                    border: InputBorder.none,
                                    contentPadding: const EdgeInsets.all(AppSizes.paddingM),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                isScrollable: true,
                tabs: const [
                  Tab(text: 'الكل'),
                  Tab(text: 'كتب'),
                  Tab(text: 'أبحاث'),
                  Tab(text: 'فيديوهات'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Filters
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Row(
                children: [
                  Expanded(
                    child: _buildFilterDropdown(
                      'التخصص',
                      _selectedCategory,
                      _categories,
                      (value) => setState(() => _selectedCategory = value!),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildFilterDropdown(
                      'النوع',
                      _selectedType,
                      _types,
                      (value) => setState(() => _selectedType = value!),
                    ),
                  ),
                  IconButton(
                    onPressed: _showAdvancedSearch,
                    icon: const Icon(
                      Icons.filter_list,
                      color: Color(AppColors.primaryRed),
                    ),
                    tooltip: 'بحث متقدم',
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllResources(),
                    _buildBooks(),
                    _buildResearch(),
                    _buildVideos(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showMyLibrary,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.bookmark),
        label: const Text('مكتبتي'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Text(label),
          isExpanded: true,
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildAllResources() {
    final resources = _getAllResources();
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 0.75,
      ),
      itemCount: resources.length,
      itemBuilder: (context, index) {
        final resource = resources[index];
        return _buildResourceCard(resource);
      },
    );
  }

  Widget _buildBooks() {
    final books = _getBooks();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: books.length,
      itemBuilder: (context, index) {
        final book = books[index];
        return _buildBookCard(book);
      },
    );
  }

  Widget _buildResearch() {
    final research = _getResearch();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: research.length,
      itemBuilder: (context, index) {
        final paper = research[index];
        return _buildResearchCard(paper);
      },
    );
  }

  Widget _buildVideos() {
    final videos = _getVideos();

    return GridView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 1,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 16 / 9,
      ),
      itemCount: videos.length,
      itemBuilder: (context, index) {
        final video = videos[index];
        return _buildVideoCard(video);
      },
    );
  }

  Widget _buildResourceCard(Map<String, dynamic> resource) {
    final type = resource['type'] ?? 'كتاب';
    final isSaved = resource['isSaved'] ?? false;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _openResource(resource),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
          ),
          child: Column(
            children: [
              // Resource Cover
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        _getTypeColor(type),
                        _getTypeColor(type).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppSizes.radiusL),
                      topRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          _getTypeIcon(type),
                          size: 50,
                          color: const Color(AppColors.primaryWhite),
                        ),
                      ),

                      // Type Badge
                      Positioned(
                        top: AppSizes.paddingS,
                        left: AppSizes.paddingS,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            type,
                            style: TextStyle(
                              color: _getTypeColor(type),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                      // Save Button
                      Positioned(
                        top: AppSizes.paddingS,
                        right: AppSizes.paddingS,
                        child: IconButton(
                          onPressed: () => _toggleSaveResource(resource),
                          icon: Icon(
                            isSaved ? Icons.bookmark : Icons.bookmark_border,
                            color: const Color(AppColors.primaryWhite),
                            size: AppSizes.iconS,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: const Color(AppColors.primaryBlack).withValues(alpha: 0.5),
                            minimumSize: const Size(30, 30),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Resource Info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        resource['title'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Color(AppColors.primaryBlack),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppSizes.paddingXS),

                      // Author
                      Text(
                        resource['author'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(AppColors.grey),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const Spacer(),

                      // Footer
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            size: 12,
                            color: const Color(AppColors.warning),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            '${resource['rating']}',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Color(AppColors.grey),
                            ),
                          ),
                          const Spacer(),
                          if (resource['isPremium'] == true)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingXS,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.warning),
                                borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              ),
                              child: const Text(
                                'مميز',
                                style: TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookCard(Map<String, dynamic> book) {
    final isSaved = book['isSaved'] ?? false;

    return Card(
      elevation: 6,
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _openResource(book),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Row(
            children: [
              // Book Cover
              Container(
                width: 80,
                height: 100,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(AppColors.info),
                      const Color(AppColors.info).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: const Icon(
                  Icons.menu_book,
                  size: 40,
                  color: Color(AppColors.primaryWhite),
                ),
              ),

              const SizedBox(width: AppSizes.paddingM),

              // Book Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book['title'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: AppSizes.paddingXS),

                    Text(
                      book['author'],
                      style: const TextStyle(
                        color: Color(AppColors.grey),
                        fontSize: 14,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    Text(
                      book['description'],
                      style: const TextStyle(
                        color: Color(AppColors.grey),
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16,
                          color: const Color(AppColors.warning),
                        ),
                        const SizedBox(width: AppSizes.paddingXS),
                        Text(
                          '${book['rating']}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingM),
                        Icon(
                          Icons.download,
                          size: 16,
                          color: const Color(AppColors.info),
                        ),
                        const SizedBox(width: AppSizes.paddingXS),
                        Text(
                          '${book['downloads']}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(AppColors.grey),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Actions
              Column(
                children: [
                  IconButton(
                    onPressed: () => _toggleSaveResource(book),
                    icon: Icon(
                      isSaved ? Icons.bookmark : Icons.bookmark_border,
                      color: isSaved
                          ? const Color(AppColors.primaryRed)
                          : const Color(AppColors.grey),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _downloadResource(book),
                    icon: const Icon(
                      Icons.download,
                      color: Color(AppColors.info),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResearchCard(Map<String, dynamic> paper) {
    final isSaved = paper['isSaved'] ?? false;

    return Card(
      elevation: 6,
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _openResource(paper),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingS),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(AppColors.success),
                          const Color(AppColors.success).withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(AppSizes.radiusM),
                    ),
                    child: const Icon(
                      Icons.science,
                      size: 24,
                      color: Color(AppColors.primaryWhite),
                    ),
                  ),

                  const SizedBox(width: AppSizes.paddingM),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          paper['title'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          paper['journal'],
                          style: const TextStyle(
                            color: Color(AppColors.info),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                  IconButton(
                    onPressed: () => _toggleSaveResource(paper),
                    icon: Icon(
                      isSaved ? Icons.bookmark : Icons.bookmark_border,
                      color: isSaved
                          ? const Color(AppColors.primaryRed)
                          : const Color(AppColors.grey),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppSizes.paddingM),

              // Authors
              Text(
                'المؤلفون: ${paper['authors']}',
                style: const TextStyle(
                  color: Color(AppColors.grey),
                  fontSize: 14,
                ),
              ),

              const SizedBox(height: AppSizes.paddingS),

              // Abstract
              Text(
                paper['abstract'],
                style: const TextStyle(
                  color: Color(AppColors.grey),
                  fontSize: 12,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: AppSizes.paddingM),

              // Footer
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.paddingS,
                      vertical: AppSizes.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                    child: Text(
                      paper['category'],
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(width: AppSizes.paddingS),

                  Text(
                    paper['year'],
                    style: const TextStyle(
                      color: Color(AppColors.grey),
                      fontSize: 12,
                    ),
                  ),

                  const Spacer(),

                  Icon(
                    Icons.visibility,
                    size: 16,
                    color: const Color(AppColors.grey),
                  ),
                  const SizedBox(width: AppSizes.paddingXS),
                  Text(
                    '${paper['citations']} استشهاد',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(AppColors.grey),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoCard(Map<String, dynamic> video) {
    final isSaved = video['isSaved'] ?? false;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _playVideo(video),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryBlack).withValues(alpha: 0.8),
                const Color(AppColors.primaryBlack).withValues(alpha: 0.6),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Video Thumbnail
              Center(
                child: Container(
                  padding: const EdgeInsets.all(AppSizes.paddingL),
                  decoration: BoxDecoration(
                    color: const Color(AppColors.primaryRed),
                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    size: 50,
                    color: Color(AppColors.primaryWhite),
                  ),
                ),
              ),

              // Video Info
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        const Color(AppColors.primaryBlack).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(AppSizes.radiusL),
                      bottomRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        video['title'],
                        style: const TextStyle(
                          color: Color(AppColors.primaryWhite),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppSizes.paddingXS),

                      Text(
                        video['instructor'],
                        style: TextStyle(
                          color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                          fontSize: 14,
                        ),
                      ),

                      const SizedBox(height: AppSizes.paddingS),

                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            video['duration'],
                            style: TextStyle(
                              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            onPressed: () => _toggleSaveResource(video),
                            icon: Icon(
                              isSaved ? Icons.bookmark : Icons.bookmark_border,
                              color: const Color(AppColors.primaryWhite),
                              size: AppSizes.iconS,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'كتاب':
        return const Color(AppColors.info);
      case 'بحث':
        return const Color(AppColors.success);
      case 'فيديو':
        return const Color(AppColors.primaryRed);
      case 'عرض تقديمي':
        return const Color(AppColors.warning);
      default:
        return const Color(AppColors.primaryBlack);
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'كتاب':
        return Icons.menu_book;
      case 'بحث':
        return Icons.science;
      case 'فيديو':
        return Icons.play_circle;
      case 'عرض تقديمي':
        return Icons.slideshow;
      default:
        return Icons.description;
    }
  }

  void _showAdvancedSearch() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Text(
                'بحث متقدم',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Advanced search content would go here
            const Expanded(
              child: Center(
                child: Text('خيارات البحث المتقدم قريباً...'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMyLibrary() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Text(
                'مكتبتي الشخصية',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // My library content would go here
            const Expanded(
              child: Center(
                child: Text('لا توجد مواد محفوظة بعد'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openResource(Map<String, dynamic> resource) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      resource['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      'المؤلف: ${resource['author']}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: const Color(AppColors.grey),
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      resource['description'] ?? 'وصف المورد...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSaveResource(Map<String, dynamic> resource) {
    setState(() {
      resource['isSaved'] = !(resource['isSaved'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          resource['isSaved']
              ? 'تم حفظ المورد في مكتبتي'
              : 'تم إزالة المورد من مكتبتي',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _downloadResource(Map<String, dynamic> resource) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('جاري تحميل: ${resource['title']}'),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _playVideo(Map<String, dynamic> video) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(video['title']),
        content: Container(
          width: 300,
          height: 200,
          decoration: BoxDecoration(
            color: const Color(AppColors.primaryBlack),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Center(
            child: Icon(
              Icons.play_circle,
              size: 80,
              color: Color(AppColors.primaryWhite),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getAllResources() {
    return [
      {
        'title': 'أساسيات زراعة الأسنان',
        'author': 'د. أحمد محمد علي',
        'type': 'كتاب',
        'rating': 4.8,
        'isPremium': true,
        'isSaved': false,
        'description': 'دليل شامل لأساسيات زراعة الأسنان',
      },
      {
        'title': 'تقنيات التقويم الحديثة',
        'author': 'د. محمد الزهراني',
        'type': 'بحث',
        'rating': 4.6,
        'isPremium': false,
        'isSaved': true,
        'description': 'بحث حول أحدث تقنيات التقويم',
      },
      {
        'title': 'علاج الجذور بالمجهر',
        'author': 'د. عبدالرحمن علي',
        'type': 'فيديو',
        'rating': 4.9,
        'isPremium': true,
        'isSaved': false,
        'description': 'فيديو تعليمي لعلاج الجذور',
      },
      {
        'title': 'إدارة العيادات الحديثة',
        'author': 'د. سارة أحمد',
        'type': 'عرض تقديمي',
        'rating': 4.5,
        'isPremium': false,
        'isSaved': false,
        'description': 'عرض تقديمي عن إدارة العيادات',
      },
    ];
  }

  List<Map<String, dynamic>> _getBooks() {
    return [
      {
        'title': 'موسوعة طب الأسنان الشاملة',
        'author': 'د. أحمد محمد علي',
        'description': 'موسوعة شاملة تغطي جميع جوانب طب الأسنان من الأساسيات إلى التقنيات المتقدمة',
        'rating': 4.8,
        'downloads': 1250,
        'isSaved': false,
      },
      {
        'title': 'التقويم الشفاف والتقنيات الحديثة',
        'author': 'د. محمد عبدالله الزهراني',
        'description': 'دليل متخصص في التقويم الشفاف وأحدث التقنيات المستخدمة في هذا المجال',
        'rating': 4.6,
        'downloads': 890,
        'isSaved': true,
      },
    ];
  }

  List<Map<String, dynamic>> _getResearch() {
    return [
      {
        'title': 'تأثير التقنيات الحديثة على نجاح زراعة الأسنان',
        'authors': 'د. أحمد علي، د. محمد حسن، د. فاطمة سالم',
        'journal': 'مجلة طب الأسنان الدولية',
        'year': '2024',
        'category': 'جراحة',
        'abstract': 'دراسة شاملة حول تأثير التقنيات الحديثة في زراعة الأسنان ومعدلات النجاح...',
        'citations': 45,
        'isSaved': false,
      },
      {
        'title': 'فعالية العلاج بالليزر في طب اللثة',
        'authors': 'د. نورا أحمد، د. سارة محمد',
        'journal': 'مجلة طب اللثة',
        'year': '2023',
        'category': 'علاج اللثة',
        'abstract': 'بحث حول فعالية استخدام الليزر في علاج أمراض اللثة المختلفة...',
        'citations': 32,
        'isSaved': true,
      },
    ];
  }

  List<Map<String, dynamic>> _getVideos() {
    return [
      {
        'title': 'تقنيات زراعة الأسنان المتقدمة',
        'instructor': 'د. أحمد محمد علي',
        'duration': '45 دقيقة',
        'isSaved': false,
      },
      {
        'title': 'علاج الجذور بالمجهر - الجزء الأول',
        'instructor': 'د. عبدالرحمن علي محمد',
        'duration': '30 دقيقة',
        'isSaved': true,
      },
      {
        'title': 'التقويم الشفاف للحالات المعقدة',
        'instructor': 'د. محمد عبدالله الزهراني',
        'duration': '60 دقيقة',
        'isSaved': false,
      },
    ];
  }
}
