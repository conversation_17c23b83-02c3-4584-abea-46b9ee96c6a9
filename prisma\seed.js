const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create roles
  console.log('Creating roles...');
  const roles = await Promise.all([
    prisma.role.upsert({
      where: { name: 'super_admin' },
      update: {},
      create: {
        name: 'super_admin',
        description: 'Super Administrator with full system access',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          subscriptions: ['create', 'read', 'update', 'delete'],
          payments: ['create', 'read', 'update', 'delete'],
          sessions: ['create', 'read', 'update', 'delete'],
          courses: ['create', 'read', 'update', 'delete'],
          reports: ['read'],
          system: ['manage']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'admin' },
      update: {},
      create: {
        name: 'admin',
        description: 'Administrator with management access',
        permissions: {
          users: ['read', 'update'],
          subscriptions: ['read', 'update'],
          payments: ['read'],
          sessions: ['create', 'read', 'update'],
          courses: ['create', 'read', 'update'],
          reports: ['read']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'admission_committee' },
      update: {},
      create: {
        name: 'admission_committee',
        description: 'Admission Committee for reviewing applications',
        permissions: {
          users: ['read'],
          subscriptions: ['read', 'update'],
          documents: ['read', 'update']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'accountant' },
      update: {},
      create: {
        name: 'accountant',
        description: 'Accountant for financial management',
        permissions: {
          payments: ['create', 'read', 'update'],
          reports: ['read']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'scientific_committee' },
      update: {},
      create: {
        name: 'scientific_committee',
        description: 'Scientific Committee for content management',
        permissions: {
          sessions: ['create', 'read', 'update', 'delete'],
          courses: ['create', 'read', 'update', 'delete'],
          speakers: ['create', 'read', 'update', 'delete']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'media_committee' },
      update: {},
      create: {
        name: 'media_committee',
        description: 'Media Committee for content and communications',
        permissions: {
          notifications: ['create', 'read'],
          content: ['create', 'read', 'update']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'speaker' },
      update: {},
      create: {
        name: 'speaker',
        description: 'Conference Speaker',
        permissions: {
          sessions: ['read', 'update_own'],
          content: ['create_own', 'read_own', 'update_own']
        }
      }
    }),
    
    prisma.role.upsert({
      where: { name: 'participant' },
      update: {},
      create: {
        name: 'participant',
        description: 'Conference Participant',
        permissions: {
          profile: ['read', 'update_own'],
          sessions: ['read'],
          courses: ['read', 'enroll']
        }
      }
    })
  ]);

  console.log(`✅ Created ${roles.length} roles`);

  // Create admin user
  console.log('Creating admin user...');
  const hashedPassword = await bcrypt.hash('Admin123!@#', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      phone: '+966501234567',
      passwordHash: hashedPassword,
      arabicName: 'مدير النظام',
      englishName: 'System Administrator',
      qualification: 'ADMIN',
      specialization: 'System Administration',
      university: 'IDEC 2025',
      status: 'ACTIVE',
      emailVerified: true,
      phoneVerified: true
    }
  });

  // Assign super_admin role to admin user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: roles[0].id // super_admin role
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: roles[0].id
    }
  });

  console.log('✅ Created admin user');

  // Create sample speakers
  console.log('Creating sample speakers...');
  const speakers = await Promise.all([
    prisma.speaker.create({
      data: {
        nameAr: 'د. أحمد محمد السعيد',
        nameEn: 'Dr. Ahmed Mohammed Al-Saeed',
        bio: 'استشاري طب الأسنان مع خبرة 15 عام في زراعة الأسنان',
        email: '<EMAIL>',
        linkedin: 'https://linkedin.com/in/ahmed-saeed'
      }
    }),
    
    prisma.speaker.create({
      data: {
        nameAr: 'د. فاطمة علي الزهراني',
        nameEn: 'Dr. Fatima Ali Al-Zahrani',
        bio: 'أستاذة طب أسنان الأطفال بجامعة الملك سعود',
        email: '<EMAIL>',
        linkedin: 'https://linkedin.com/in/fatima-zahrani'
      }
    }),
    
    prisma.speaker.create({
      data: {
        nameAr: 'د. محمد عبدالله القحطاني',
        nameEn: 'Dr. Mohammed Abdullah Al-Qahtani',
        bio: 'خبير في تقويم الأسنان والجراحة التجميلية',
        email: '<EMAIL>',
        linkedin: 'https://linkedin.com/in/mohammed-qahtani'
      }
    })
  ]);

  console.log(`✅ Created ${speakers.length} speakers`);

  // Create sample rooms
  console.log('Creating sample rooms...');
  const rooms = await Promise.all([
    prisma.room.create({
      data: {
        name: 'القاعة الرئيسية',
        capacity: 500,
        location: 'الطابق الأول',
        equipment: {
          projector: true,
          microphone: true,
          speakers: true,
          wifi: true,
          airConditioning: true
        }
      }
    }),
    
    prisma.room.create({
      data: {
        name: 'قاعة الورش',
        capacity: 100,
        location: 'الطابق الثاني',
        equipment: {
          projector: true,
          microphone: true,
          speakers: true,
          wifi: true,
          workstations: 50
        }
      }
    }),
    
    prisma.room.create({
      data: {
        name: 'قاعة المعرض',
        capacity: 200,
        location: 'الطابق الأرضي',
        equipment: {
          displayScreens: true,
          wifi: true,
          powerOutlets: true
        }
      }
    })
  ]);

  console.log(`✅ Created ${rooms.length} rooms`);

  // Create sample sessions
  console.log('Creating sample sessions...');
  const sessions = await Promise.all([
    prisma.session.create({
      data: {
        titleAr: 'مستقبل زراعة الأسنان',
        titleEn: 'The Future of Dental Implants',
        description: 'محاضرة شاملة حول أحدث التقنيات في زراعة الأسنان',
        startTime: new Date('2025-03-15T09:00:00Z'),
        endTime: new Date('2025-03-15T10:30:00Z'),
        speakerId: speakers[0].id,
        roomId: rooms[0].id,
        sessionType: 'LECTURE',
        cmeHours: 1.5,
        maxAttendees: 500,
        status: 'SCHEDULED'
      }
    }),
    
    prisma.session.create({
      data: {
        titleAr: 'ورشة عمل: تقويم الأسنان الحديث',
        titleEn: 'Workshop: Modern Orthodontics',
        description: 'ورشة عمل تطبيقية حول تقنيات تقويم الأسنان الحديثة',
        startTime: new Date('2025-03-15T11:00:00Z'),
        endTime: new Date('2025-03-15T13:00:00Z'),
        speakerId: speakers[2].id,
        roomId: rooms[1].id,
        sessionType: 'WORKSHOP',
        cmeHours: 2.0,
        maxAttendees: 100,
        status: 'SCHEDULED'
      }
    }),
    
    prisma.session.create({
      data: {
        titleAr: 'طب أسنان الأطفال: التحديات والحلول',
        titleEn: 'Pediatric Dentistry: Challenges and Solutions',
        description: 'مناقشة التحديات الحديثة في طب أسنان الأطفال',
        startTime: new Date('2025-03-15T14:00:00Z'),
        endTime: new Date('2025-03-15T15:30:00Z'),
        speakerId: speakers[1].id,
        roomId: rooms[0].id,
        sessionType: 'PRESENTATION',
        cmeHours: 1.5,
        maxAttendees: 500,
        status: 'SCHEDULED'
      }
    })
  ]);

  console.log(`✅ Created ${sessions.length} sessions`);

  // Create sample courses
  console.log('Creating sample courses...');
  const courses = await Promise.all([
    prisma.course.create({
      data: {
        titleAr: 'دورة زراعة الأسنان المتقدمة',
        titleEn: 'Advanced Dental Implant Course',
        description: 'دورة شاملة تغطي جميع جوانب زراعة الأسنان من الأساسيات إلى التقنيات المتقدمة',
        price: 2500.00,
        maxParticipants: 30,
        startDate: new Date('2025-03-16T09:00:00Z'),
        endDate: new Date('2025-03-16T17:00:00Z'),
        instructorId: speakers[0].id,
        status: 'PUBLISHED'
      }
    }),
    
    prisma.course.create({
      data: {
        titleAr: 'دورة تقويم الأسنان للمبتدئين',
        titleEn: 'Orthodontics for Beginners',
        description: 'دورة تأسيسية في تقويم الأسنان للأطباء الجدد',
        price: 1800.00,
        maxParticipants: 25,
        startDate: new Date('2025-03-17T09:00:00Z'),
        endDate: new Date('2025-03-17T16:00:00Z'),
        instructorId: speakers[2].id,
        status: 'PUBLISHED'
      }
    })
  ]);

  console.log(`✅ Created ${courses.length} courses`);

  // Create sample test users
  console.log('Creating sample test users...');
  const testPassword = await bcrypt.hash('Test123!@#', 12);
  
  const testUsers = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+966501234568',
        passwordHash: testPassword,
        arabicName: 'د. سارة أحمد',
        englishName: 'Dr. Sarah Ahmed',
        qualification: 'DOCTOR',
        specialization: 'General Dentistry',
        university: 'King Saud University',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true
      }
    }),
    
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        phone: '+966501234569',
        passwordHash: testPassword,
        arabicName: 'محمد علي الطالب',
        englishName: 'Mohammed Ali Student',
        qualification: 'STUDENT_YEAR_5',
        university: 'King Abdulaziz University',
        status: 'ACTIVE',
        emailVerified: true,
        phoneVerified: true
      }
    })
  ]);

  // Assign participant role to test users
  const participantRole = roles.find(role => role.name === 'participant');
  await Promise.all(testUsers.map(user => 
    prisma.userRole.create({
      data: {
        userId: user.id,
        roleId: participantRole.id
      }
    })
  ));

  console.log(`✅ Created ${testUsers.length} test users`);

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Summary:');
  console.log(`- Roles: ${roles.length}`);
  console.log(`- Speakers: ${speakers.length}`);
  console.log(`- Rooms: ${rooms.length}`);
  console.log(`- Sessions: ${sessions.length}`);
  console.log(`- Courses: ${courses.length}`);
  console.log(`- Users: ${testUsers.length + 1} (including admin)`);
  
  console.log('\n🔐 Admin Credentials:');
  console.log('Email: <EMAIL>');
  console.log('Password: Admin123!@#');
  
  console.log('\n🧪 Test User Credentials:');
  console.log('Doctor - Email: <EMAIL>, Password: Test123!@#');
  console.log('Student - Email: <EMAIL>, Password: Test123!@#');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
