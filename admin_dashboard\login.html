<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - لوحة التحكم IDEC 2025</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #8b0000 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        .login-left {
            background: linear-gradient(135deg, #dc3545 0%, #8b0000 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .login-right {
            padding: 60px 40px;
        }
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #dc3545 0%, #8b0000 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .loading {
            display: none;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side -->
            <div class="col-md-6 login-left">
                <div>
                    <div class="logo">
                        <i class="fas fa-tooth"></i>
                    </div>
                    <h2 class="mb-4">IDEC 2025</h2>
                    <h4 class="mb-4">لوحة التحكم الإدارية</h4>
                    <p class="mb-4">نظام إدارة شامل للمؤتمر الدولي لطب الأسنان</p>
                    
                    <div class="features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <div class="fw-bold">إدارة المستخدمين</div>
                                <small>إدارة شاملة للمشاركين والمتحدثين</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div>
                                <div class="fw-bold">مراجعة الطلبات</div>
                                <small>نظام متقدم لمراجعة طلبات الاشتراك</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div>
                                <div class="fw-bold">التقارير والإحصائيات</div>
                                <small>تحليلات شاملة ومفصلة</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div>
                                <div class="fw-bold">المحاسبة المالية</div>
                                <small>إدارة المدفوعات والمعاملات المالية</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Side -->
            <div class="col-md-6 login-right">
                <div class="text-center mb-4">
                    <h3 class="text-dark mb-2">مرحباً بك</h3>
                    <p class="text-muted">قم بتسجيل الدخول للوصول إلى لوحة التحكم</p>
                </div>
                
                <div id="alertContainer"></div>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <div class="input-group">
                            <input type="email" class="form-control with-icon" id="email" required>
                            <span class="input-group-text">
                                <i class="fas fa-envelope text-muted"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" class="form-control with-icon" id="password" required>
                            <span class="input-group-text">
                                <i class="fas fa-lock text-muted"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-login text-white w-100 mb-3">
                        <span class="login-text">تسجيل الدخول</span>
                        <span class="loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            جاري التحقق...
                        </span>
                    </button>
                </form>
                
                <div class="text-center">
                    <small class="text-muted">
                        هل نسيت كلمة المرور؟ 
                        <a href="#" class="text-decoration-none" style="color: #dc3545;">إعادة تعيين</a>
                    </small>
                </div>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        نظام آمن ومحمي
                    </small>
                </div>
                
                <!-- Demo Credentials -->
                <div class="mt-4 p-3 bg-light rounded">
                    <small class="text-muted d-block mb-2">
                        <i class="fas fa-info-circle me-1"></i>
                        بيانات تجريبية:
                    </small>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">البريد:</small><br>
                            <small class="fw-bold"><EMAIL></small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">كلمة المرور:</small><br>
                            <small class="fw-bold">Admin123!@#</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-secondary btn-sm mt-2 w-100" onclick="fillDemoCredentials()">
                        <i class="fas fa-magic me-1"></i>
                        ملء البيانات التجريبية
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:3000/api/v1';
        
        // Check if already logged in
        if (localStorage.getItem('adminToken')) {
            window.location.href = 'dashboard.html';
        }
        
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Show loading state
            showLoading(true);
            clearAlert();
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    // Store token
                    if (rememberMe) {
                        localStorage.setItem('adminToken', data.data.token);
                    } else {
                        sessionStorage.setItem('adminToken', data.data.token);
                    }
                    
                    // Check if user has admin privileges
                    const userRoles = data.data.user.userRoles || [];
                    const hasAdminRole = userRoles.some(ur => 
                        ['super_admin', 'admin', 'admission_committee', 'accountant', 'scientific_committee', 'media_committee'].includes(ur.role.name)
                    );
                    
                    if (hasAdminRole) {
                        showAlert('success', 'تم تسجيل الدخول بنجاح! جاري التوجيه...');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    } else {
                        showAlert('danger', 'ليس لديك صلاحية للوصول إلى لوحة التحكم');
                        localStorage.removeItem('adminToken');
                        sessionStorage.removeItem('adminToken');
                    }
                } else {
                    showAlert('danger', data.error?.message || 'خطأ في تسجيل الدخول');
                }
            } catch (error) {
                console.error('Login error:', error);
                showAlert('danger', 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            } finally {
                showLoading(false);
            }
        });
        
        // Show/hide loading state
        function showLoading(show) {
            const loginText = document.querySelector('.login-text');
            const loadingText = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            if (show) {
                loginText.style.display = 'none';
                loadingText.style.display = 'inline';
                submitBtn.disabled = true;
            } else {
                loginText.style.display = 'inline';
                loadingText.style.display = 'none';
                submitBtn.disabled = false;
            }
        }
        
        // Show alert
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }
        
        // Clear alert
        function clearAlert() {
            document.getElementById('alertContainer').innerHTML = '';
        }
        
        // Fill demo credentials
        function fillDemoCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'Admin123!@#';
        }
        
        // Auto-fill demo credentials on page load for testing
        window.addEventListener('load', function() {
            // Uncomment the line below for automatic demo credential filling
            // fillDemoCredentials();
        });
    </script>
</body>
</html>
