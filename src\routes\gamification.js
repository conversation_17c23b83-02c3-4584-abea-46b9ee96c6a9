const express = require('express');
const { body, param, query } = require('express-validator');
const gamificationController = require('../controllers/gamificationController');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// User routes (require authentication)
router.use(auth.authenticate);

// Get user's gamification summary
router.get('/summary', gamificationController.getUserSummary);

// Get user's points history
router.get('/points/history', 
  [
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  validation.handleValidationErrors,
  gamificationController.getPointsHistory
);

// Get user's badges
router.get('/badges', gamificationController.getUserBadges);

// Get user's achievements
router.get('/achievements', gamificationController.getUserAchievements);

// Get leaderboard
router.get('/leaderboard',
  [
    query('period').optional().isIn(['DAILY', 'WEEKLY', 'MONTHLY', 'ALL_TIME']).withMessage('Invalid period'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  validation.handleValidationErrors,
  gamificationController.getLeaderboard
);

// Admin routes (require admin permissions)
router.use(auth.requireRole(['SUPER_ADMIN', 'ADMIN']));

// Award points to user
router.post('/points/award',
  [
    body('userId').notEmpty().withMessage('User ID is required'),
    body('pointType').isIn([
      'SESSION_ATTENDANCE',
      'COURSE_COMPLETION',
      'PROFILE_COMPLETION',
      'SOCIAL_INTERACTION',
      'QUESTION_ASKED',
      'RATING_GIVEN',
      'DOCUMENT_UPLOAD',
      'EARLY_REGISTRATION',
      'REFERRAL',
      'SPECIAL_ACHIEVEMENT'
    ]).withMessage('Invalid point type'),
    body('points').isInt({ min: 1 }).withMessage('Points must be a positive integer'),
    body('reason').notEmpty().withMessage('Reason is required'),
    body('sessionId').optional().isString(),
    body('courseId').optional().isString()
  ],
  validation.handleValidationErrors,
  gamificationController.awardPoints
);

// Badge management routes
router.post('/badges',
  [
    body('name').notEmpty().withMessage('Badge name is required'),
    body('nameAr').notEmpty().withMessage('Arabic name is required'),
    body('nameEn').notEmpty().withMessage('English name is required'),
    body('description').optional().isString(),
    body('icon').optional().isString(),
    body('color').optional().isString(),
    body('criteria').isObject().withMessage('Criteria must be an object')
  ],
  validation.handleValidationErrors,
  gamificationController.createBadge
);

router.get('/badges/all', gamificationController.getAllBadges);

router.put('/badges/:id',
  [
    param('id').notEmpty().withMessage('Badge ID is required'),
    body('name').optional().isString(),
    body('nameAr').optional().isString(),
    body('nameEn').optional().isString(),
    body('description').optional().isString(),
    body('icon').optional().isString(),
    body('color').optional().isString(),
    body('criteria').optional().isObject(),
    body('isActive').optional().isBoolean()
  ],
  validation.handleValidationErrors,
  gamificationController.updateBadge
);

router.delete('/badges/:id',
  [
    param('id').notEmpty().withMessage('Badge ID is required')
  ],
  validation.handleValidationErrors,
  gamificationController.deleteBadge
);

// Get gamification statistics
router.get('/stats', gamificationController.getGamificationStats);

// Trigger badge check for specific user
router.post('/users/:userId/check-badges',
  [
    param('userId').notEmpty().withMessage('User ID is required')
  ],
  validation.handleValidationErrors,
  gamificationController.triggerBadgeCheck
);

// Update leaderboard rankings
router.post('/leaderboard/update',
  [
    body('period').optional().isIn(['DAILY', 'WEEKLY', 'MONTHLY', 'ALL_TIME']).withMessage('Invalid period')
  ],
  validation.handleValidationErrors,
  gamificationController.updateLeaderboard
);

module.exports = router;
