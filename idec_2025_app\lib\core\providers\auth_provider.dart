import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../services/api_service.dart';
import '../constants/app_constants.dart';

class AuthProvider extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _error;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;

  final ApiService _apiService = ApiService();

  // Initialize auth state
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      await _apiService.initialize();
      
      // Check if user is already logged in
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConstants.tokenKey);
      
      if (token != null) {
        // Verify token and get user data
        final response = await _apiService.getUserProfile();
        if (response['success']) {
          _user = User.fromJson(response['data']);
          _isAuthenticated = true;
        } else {
          // Token is invalid, clear it
          await clearAuth();
        }
      }
    } catch (e) {
      _error = e.toString();
      await clearAuth();
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.login(email, password);
      
      if (response['success']) {
        final authData = response['data'];
        
        // Save token
        await _apiService.setAuthToken(authData['token']);
        
        // Save user data
        _user = User.fromJson(authData['user']);
        _isAuthenticated = true;
        
        // Save user to local storage
        await _saveUserToLocal(_user!);
        
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Login failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register
  Future<bool> register(Map<String, dynamic> userData) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.register(userData);
      
      if (response['success']) {
        // Registration successful, but may need verification
        _error = null;
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Registration failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _apiService.logout();
    } catch (e) {
      // Continue with logout even if API call fails
      debugPrint('Logout API error: $e');
    }

    await clearAuth();
    _setLoading(false);
  }

  // Clear authentication data
  Future<void> clearAuth() async {
    await _apiService.clearAuthToken();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.userKey);
    
    _user = null;
    _isAuthenticated = false;
    _error = null;
    
    notifyListeners();
  }

  // Update user profile
  Future<bool> updateProfile(Map<String, dynamic> userData) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.updateUserProfile(userData);
      
      if (response['success']) {
        _user = User.fromJson(response['data']);
        await _saveUserToLocal(_user!);
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Update failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      final response = await _apiService.refreshToken();
      
      if (response['success']) {
        await _apiService.setAuthToken(response['data']['token']);
        return true;
      } else {
        await clearAuth();
        return false;
      }
    } catch (e) {
      await clearAuth();
      return false;
    }
  }

  // Check if user has specific role
  bool hasRole(List<String> roles) {
    return _user != null && roles.contains(_user!.role);
  }

  // Check if user is admin
  bool get isAdmin => hasRole(['SUPER_ADMIN', 'ADMIN']);

  // Check if user is moderator
  bool get isModerator => hasRole(['SUPER_ADMIN', 'ADMIN', 'MODERATOR']);

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  Future<void> _saveUserToLocal(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userKey, user.toJson().toString());
  }

  // Get user from local storage
  Future<User?> _getUserFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userString = prefs.getString(AppConstants.userKey);
      
      if (userString != null) {
        // Parse user data from string
        // Note: This is a simplified version, you might want to use proper JSON parsing
        return null; // Implement proper parsing if needed
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }
}

// Auth state enum
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

// Auth result class
class AuthResult {
  final bool success;
  final String? message;
  final User? user;

  AuthResult({
    required this.success,
    this.message,
    this.user,
  });
}

// Login form data
class LoginData {
  final String email;
  final String password;

  LoginData({
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

// Registration form data
class RegisterData {
  final String email;
  final String password;
  final String arabicName;
  final String englishName;
  final String phone;
  final String qualification;
  final String specialization;
  final String workPlace;
  final String city;
  final String country;

  RegisterData({
    required this.email,
    required this.password,
    required this.arabicName,
    required this.englishName,
    required this.phone,
    required this.qualification,
    required this.specialization,
    required this.workPlace,
    required this.city,
    required this.country,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'arabicName': arabicName,
      'englishName': englishName,
      'phone': phone,
      'qualification': qualification,
      'specialization': specialization,
      'workPlace': workPlace,
      'city': city,
      'country': country,
    };
  }
}
