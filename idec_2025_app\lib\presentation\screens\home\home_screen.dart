import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';
import '../auth/login_screen.dart';
import '../auth/register_screen.dart';
import '../conference/conference_screen.dart';
import '../speakers/speakers_screen.dart';
import '../agenda/agenda_screen.dart';
import '../profile/profile_screen.dart';
import '../search/search_screen.dart';
import '../notifications/notifications_screen.dart';
import '../qr/qr_scanner_screen.dart';
// New Advanced Screens
import '../sessions/sessions_screen.dart';
import '../exhibition/exhibition_screen.dart';
import '../courses/courses_screen.dart';
import '../news/news_screen.dart';
import '../library/digital_library_screen.dart';
import '../exhibitors/exhibitors_screen.dart';
import '../ar/ar_experience_screen.dart';
import '../ai/ai_assistant_screen.dart';
import '../social/social_network_screen.dart';
import '../analytics/analytics_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeTab(),
    const ConferenceTab(),
    const SpeakersTab(),
    const AgendaTab(),
    const ProfileTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: AppStrings.home,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.event),
            label: AppStrings.conference,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: AppStrings.speakers,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: AppStrings.agenda,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: AppStrings.profile,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const QRScannerScreen(),
            ),
          );
        },
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        tooltip: 'مسح QR Code',
        child: const Icon(Icons.qr_code_scanner),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SearchScreen(),
                ),
              );
            },
            icon: const Icon(Icons.search),
            tooltip: 'البحث',
          ),
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const NotificationsScreen(),
                    ),
                  );
                },
                icon: const Icon(Icons.notifications),
                tooltip: 'الإشعارات',
              ),
              // Notification Badge
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(AppColors.primaryWhite),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 12,
                    minHeight: 12,
                  ),
                  child: const Text(
                    '5',
                    style: TextStyle(
                      color: Color(AppColors.primaryRed),
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      drawer: _buildDrawer(context),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppSizes.paddingL),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(AppColors.primaryRed),
                    const Color(AppColors.darkRed),
                    const Color(AppColors.primaryBlack).withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppSizes.radiusL),
                boxShadow: [
                  BoxShadow(
                    color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppConstants.conferenceName,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  Text(
                    AppConstants.conferenceDescription,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: AppSizes.paddingM),
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        color: Colors.white,
                        size: AppSizes.iconS,
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Text(
                        '15-17 مارس 2025',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: AppSizes.iconS,
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Text(
                        AppConstants.conferenceLocation,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.paddingL),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const RegisterScreen(),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(AppColors.primaryWhite),
                            foregroundColor: const Color(AppColors.primaryRed),
                            elevation: 8,
                            shadowColor: const Color(AppColors.primaryBlack).withValues(alpha: 0.3),
                          ),
                          child: const Text('سجل الآن'),
                        ),
                      ),

                      const SizedBox(width: AppSizes.paddingM),

                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const LoginScreen(),
                              ),
                            );
                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: const Color(AppColors.primaryWhite),
                            side: const BorderSide(
                              color: Color(AppColors.primaryWhite),
                              width: 2,
                            ),
                            elevation: 4,
                          ),
                          child: const Text('تسجيل الدخول'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingL),

            // Quick Actions Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickAction(
                  context,
                  Icons.smart_toy,
                  'المساعد الذكي',
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const AIAssistantScreen()),
                  ),
                ),
                _buildQuickAction(
                  context,
                  Icons.view_in_ar,
                  'الواقع المعزز',
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const ARExperienceScreen()),
                  ),
                ),
                _buildQuickAction(
                  context,
                  Icons.qr_code_scanner,
                  'مسح QR',
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const QRScannerScreen()),
                  ),
                ),
                _buildQuickAction(
                  context,
                  Icons.people_alt,
                  'الشبكة الاجتماعية',
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const SocialNetworkScreen()),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSizes.paddingL),

            // Features Section
            Text(
              'مميزات المؤتمر',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            
            // Quick Access Cards
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppSizes.paddingM,
              mainAxisSpacing: AppSizes.paddingM,
              childAspectRatio: 1.1,
              children: [
                _buildQuickAccessCard(
                  context,
                  Icons.event_available,
                  'الجلسات',
                  'جلسات علمية متخصصة',
                  AppColors.info,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const SessionsScreen())),
                ),
                _buildQuickAccessCard(
                  context,
                  Icons.store,
                  'المعرض',
                  'معرض الشركات والمنتجات',
                  AppColors.success,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const ExhibitionScreen())),
                ),
                _buildQuickAccessCard(
                  context,
                  Icons.school,
                  'الدورات',
                  'دورات تدريبية معتمدة',
                  AppColors.warning,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const CoursesScreen())),
                ),
                _buildQuickAccessCard(
                  context,
                  Icons.newspaper,
                  'الأخبار',
                  'آخر أخبار المؤتمر',
                  AppColors.primaryRed,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const NewsScreen())),
                ),
              ],
            ),

            const SizedBox(height: AppSizes.paddingL),

            // Advanced Features Section
            Text(
              'الميزات المتقدمة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppSizes.paddingM,
              mainAxisSpacing: AppSizes.paddingM,
              childAspectRatio: 1.1,
              children: [
                _buildAdvancedFeatureCard(
                  context,
                  Icons.library_books,
                  'المكتبة الرقمية',
                  'مراجع ومصادر علمية',
                  AppColors.info,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const DigitalLibraryScreen())),
                ),
                _buildAdvancedFeatureCard(
                  context,
                  Icons.view_in_ar,
                  'الواقع المعزز',
                  'تجربة تعليمية تفاعلية',
                  AppColors.primaryRed,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const ARExperienceScreen())),
                ),
                _buildAdvancedFeatureCard(
                  context,
                  Icons.smart_toy,
                  'المساعد الذكي',
                  'مساعد ذكي للمؤتمر',
                  AppColors.success,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const AIAssistantScreen())),
                ),
                _buildAdvancedFeatureCard(
                  context,
                  Icons.people_alt,
                  'الشبكة الاجتماعية',
                  'تواصل مع المشاركين',
                  AppColors.warning,
                  () => Navigator.push(context, MaterialPageRoute(builder: (_) => const SocialNetworkScreen())),
                ),
              ],
            ),

            const SizedBox(height: AppSizes.paddingL),

            // Additional Services
            Text(
              'خدمات إضافية',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),

            Row(
              children: [
                Expanded(
                  child: _buildServiceCard(
                    context,
                    Icons.business,
                    'العارضون',
                    'دليل الشركات العارضة',
                    AppColors.primaryBlack,
                    () => Navigator.push(context, MaterialPageRoute(builder: (_) => const ExhibitorsScreen())),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: _buildServiceCard(
                    context,
                    Icons.analytics,
                    'الإحصائيات',
                    'تحليلات وإحصائيات المؤتمر',
                    AppColors.info,
                    () => Navigator.push(context, MaterialPageRoute(builder: (_) => const AnalyticsScreen())),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AIAssistantScreen()),
        ),
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.smart_toy),
        label: const Text('المساعد الذكي'),
        elevation: 8,
      ),
    );
  }

  Widget _buildQuickAccessCard(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    int color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                Color(color).withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(color),
                        Color(color).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    boxShadow: [
                      BoxShadow(
                        color: Color(color).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: AppSizes.iconL,
                    color: const Color(AppColors.primaryWhite),
                  ),
                ),
                const SizedBox(height: AppSizes.paddingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Color(color),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppSizes.paddingXS),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(AppColors.grey),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedFeatureCard(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    int color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(color).withValues(alpha: 0.1),
                const Color(AppColors.primaryWhite),
                Color(color).withValues(alpha: 0.05),
              ],
            ),
            border: Border.all(
              color: Color(color).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(color),
                        Color(color).withValues(alpha: 0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                    boxShadow: [
                      BoxShadow(
                        color: Color(color).withValues(alpha: 0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: AppSizes.iconL,
                    color: const Color(AppColors.primaryWhite),
                  ),
                ),
                const SizedBox(height: AppSizes.paddingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Color(color),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppSizes.paddingXS),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(AppColors.grey),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildServiceCard(
    BuildContext context,
    IconData icon,
    String title,
    String description,
    int color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                Color(color).withValues(alpha: 0.03),
              ],
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(color),
                      Color(color).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Icon(
                  icon,
                  size: AppSizes.iconM,
                  color: const Color(AppColors.primaryWhite),
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Color(color),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingXS),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(AppColors.grey),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: AppSizes.iconS,
                color: Color(color),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(AppColors.primaryRed),
              Color(AppColors.darkRed),
              Color(AppColors.primaryBlack),
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // Drawer Header
            DrawerHeader(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(AppColors.primaryRed),
                    Color(AppColors.darkRed),
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSizes.paddingM),
                    decoration: BoxDecoration(
                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    ),
                    child: const Icon(
                      Icons.medical_services,
                      size: 40,
                      color: Color(AppColors.primaryWhite),
                    ),
                  ),
                  const SizedBox(height: AppSizes.paddingM),
                  const Text(
                    AppConstants.appName,
                    style: TextStyle(
                      color: Color(AppColors.primaryWhite),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Text(
                    'المؤتمر الدولي لطب الأسنان',
                    style: TextStyle(
                      color: Color(AppColors.primaryWhite),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

            // Main Sections
            _buildDrawerSection('الأقسام الرئيسية', [
              _buildDrawerItem(Icons.event_available, 'الجلسات العلمية', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const SessionsScreen()));
              }),
              _buildDrawerItem(Icons.store, 'المعرض المصاحب', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const ExhibitionScreen()));
              }),
              _buildDrawerItem(Icons.school, 'الدورات التدريبية', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const CoursesScreen()));
              }),
              _buildDrawerItem(Icons.newspaper, 'أخبار المؤتمر', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const NewsScreen()));
              }),
            ]),

            const Divider(color: Color(AppColors.primaryWhite), thickness: 0.5),

            // Advanced Features
            _buildDrawerSection('الميزات المتقدمة', [
              _buildDrawerItem(Icons.library_books, 'المكتبة الرقمية', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const DigitalLibraryScreen()));
              }),
              _buildDrawerItem(Icons.view_in_ar, 'الواقع المعزز', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const ARExperienceScreen()));
              }),
              _buildDrawerItem(Icons.smart_toy, 'المساعد الذكي', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const AIAssistantScreen()));
              }),
              _buildDrawerItem(Icons.people_alt, 'الشبكة الاجتماعية', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const SocialNetworkScreen()));
              }),
            ]),

            const Divider(color: Color(AppColors.primaryWhite), thickness: 0.5),

            // Additional Services
            _buildDrawerSection('خدمات إضافية', [
              _buildDrawerItem(Icons.business, 'دليل العارضين', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const ExhibitorsScreen()));
              }),
              _buildDrawerItem(Icons.analytics, 'الإحصائيات والتحليلات', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const AnalyticsScreen()));
              }),
              _buildDrawerItem(Icons.qr_code_scanner, 'ماسح QR', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const QRScannerScreen()));
              }),
              _buildDrawerItem(Icons.search, 'البحث المتقدم', () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (_) => const SearchScreen()));
              }),
            ]),

            const SizedBox(height: AppSizes.paddingL),

            // Footer
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              child: Column(
                children: [
                  const Divider(color: Color(AppColors.primaryWhite), thickness: 0.5),
                  const SizedBox(height: AppSizes.paddingS),
                  Text(
                    'الإصدار 1.0.0',
                    style: TextStyle(
                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '© 2025 IDEC Conference',
                    style: TextStyle(
                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSizes.paddingL,
            vertical: AppSizes.paddingS,
          ),
          child: Text(
            title,
            style: TextStyle(
              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, VoidCallback onTap) {
    return ListTile(
      leading: Icon(
        icon,
        color: const Color(AppColors.primaryWhite),
        size: AppSizes.iconM,
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: Color(AppColors.primaryWhite),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingL,
        vertical: AppSizes.paddingXS,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
      ),
      hoverColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
    );
  }

  Widget _buildQuickAction(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusL),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingS),
        decoration: BoxDecoration(
          color: const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          border: Border.all(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(AppColors.primaryRed),
                    const Color(AppColors.darkRed),
                  ],
                ),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                boxShadow: [
                  BoxShadow(
                    color: const Color(AppColors.primaryBlack).withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: const Color(AppColors.primaryWhite),
                size: AppSizes.iconM,
              ),
            ),
            const SizedBox(height: AppSizes.paddingXS),
            Text(
              label,
              style: const TextStyle(
                color: Color(AppColors.primaryWhite),
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class ConferenceTab extends StatelessWidget {
  const ConferenceTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const ConferenceScreen();
  }
}

class SpeakersTab extends StatelessWidget {
  const SpeakersTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const SpeakersScreen();
  }
}

class AgendaTab extends StatelessWidget {
  const AgendaTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const AgendaScreen();
  }
}

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfileScreen();
  }
}
