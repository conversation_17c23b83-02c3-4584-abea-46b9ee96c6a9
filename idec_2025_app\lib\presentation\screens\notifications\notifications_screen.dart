import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String _selectedFilter = 'الكل';
  final List<String> _filters = ['الكل', 'غير مقروءة', 'مهمة', 'تذكيرات'];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            color: Color(AppColors.primaryWhite),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(AppColors.primaryRed),
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _markAllAsRead,
            icon: const Icon(
              Icons.done_all,
              color: Color(AppColors.primaryWhite),
            ),
            tooltip: 'تحديد الكل كمقروء',
          ),
          IconButton(
            onPressed: _showNotificationSettings,
            icon: const Icon(
              Icons.settings,
              color: Color(AppColors.primaryWhite),
            ),
            tooltip: 'إعدادات الإشعارات',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with Stats
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSizes.paddingL),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(AppColors.primaryRed),
                  Color(AppColors.darkRed),
                ],
              ),
            ),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // Stats Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatItem('12', 'إجمالي'),
                      Container(
                        width: 1,
                        height: 30,
                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                      ),
                      _buildStatItem('5', 'غير مقروءة'),
                      Container(
                        width: 1,
                        height: 30,
                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                      ),
                      _buildStatItem('3', 'مهمة'),
                    ],
                  ),
                  
                  const SizedBox(height: AppSizes.paddingL),
                  
                  // Filter Chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _filters.map((filter) {
                        final isSelected = filter == _selectedFilter;
                        return Container(
                          margin: const EdgeInsets.only(right: AppSizes.paddingS),
                          child: FilterChip(
                            label: Text(filter),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedFilter = filter;
                              });
                            },
                            backgroundColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                            selectedColor: const Color(AppColors.primaryWhite),
                            labelStyle: TextStyle(
                              color: isSelected
                                  ? const Color(AppColors.primaryRed)
                                  : const Color(AppColors.primaryWhite),
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusL),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Notifications List
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ListView.builder(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                itemCount: _getFilteredNotifications().length,
                itemBuilder: (context, index) {
                  final notification = _getFilteredNotifications()[index];
                  return _buildNotificationCard(notification, index);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification, int index) {
    final isRead = notification['isRead'] ?? false;
    final isImportant = notification['isImportant'] ?? false;
    final type = notification['type'] ?? 'general';
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: isRead ? 2 : 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: InkWell(
          onTap: () => _markAsRead(notification),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          child: Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
              gradient: LinearGradient(
                colors: [
                  isRead 
                      ? const Color(AppColors.primaryWhite)
                      : const Color(AppColors.offWhite),
                  const Color(AppColors.primaryWhite),
                ],
              ),
              border: Border.all(
                color: isImportant
                    ? const Color(AppColors.primaryRed)
                    : isRead
                        ? const Color(AppColors.lightGrey)
                        : const Color(AppColors.primaryRed).withValues(alpha: 0.3),
                width: isImportant ? 2 : 1,
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        _getTypeColor(type),
                        _getTypeColor(type).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  ),
                  child: Icon(
                    _getTypeIcon(type),
                    color: const Color(AppColors.primaryWhite),
                    size: AppSizes.iconM,
                  ),
                ),
                
                const SizedBox(width: AppSizes.paddingM),
                
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              notification['title'],
                              style: TextStyle(
                                fontWeight: isRead ? FontWeight.w500 : FontWeight.bold,
                                fontSize: 16,
                                color: const Color(AppColors.primaryBlack),
                              ),
                            ),
                          ),
                          
                          // Badges
                          if (isImportant)
                            Container(
                              margin: const EdgeInsets.only(left: AppSizes.paddingS),
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingS,
                                vertical: AppSizes.paddingXS,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.primaryRed),
                                borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              ),
                              child: const Text(
                                'مهم',
                                style: TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          
                          if (!isRead)
                            Container(
                              width: 8,
                              height: 8,
                              margin: const EdgeInsets.only(left: AppSizes.paddingS),
                              decoration: const BoxDecoration(
                                color: Color(AppColors.primaryRed),
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      
                      const SizedBox(height: AppSizes.paddingS),
                      
                      // Message
                      Text(
                        notification['message'],
                        style: TextStyle(
                          color: isRead 
                              ? const Color(AppColors.grey)
                              : const Color(AppColors.primaryBlack),
                          fontSize: 14,
                          height: 1.4,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: AppSizes.paddingS),
                      
                      // Footer
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: AppSizes.iconS,
                            color: const Color(AppColors.grey),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            notification['time'],
                            style: const TextStyle(
                              color: Color(AppColors.grey),
                              fontSize: 12,
                            ),
                          ),
                          
                          const Spacer(),
                          
                          // Action Buttons
                          if (notification['actionable'] == true)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                TextButton(
                                  onPressed: () => _handleNotificationAction(notification, 'accept'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(AppColors.success),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: AppSizes.paddingS,
                                    ),
                                  ),
                                  child: const Text('قبول', style: TextStyle(fontSize: 12)),
                                ),
                                TextButton(
                                  onPressed: () => _handleNotificationAction(notification, 'decline'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(AppColors.error),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: AppSizes.paddingS,
                                    ),
                                  ),
                                  child: const Text('رفض', style: TextStyle(fontSize: 12)),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'session':
        return const Color(AppColors.primaryRed);
      case 'reminder':
        return const Color(AppColors.warning);
      case 'update':
        return const Color(AppColors.info);
      case 'achievement':
        return const Color(AppColors.success);
      default:
        return const Color(AppColors.primaryBlack);
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'session':
        return Icons.event;
      case 'reminder':
        return Icons.schedule;
      case 'update':
        return Icons.info;
      case 'achievement':
        return Icons.star;
      default:
        return Icons.notifications;
    }
  }

  void _markAsRead(Map<String, dynamic> notification) {
    setState(() {
      notification['isRead'] = true;
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _getAllNotifications()) {
        notification['isRead'] = true;
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديد جميع الإشعارات كمقروءة'),
        backgroundColor: Color(AppColors.success),
      ),
    );
  }

  void _handleNotificationAction(Map<String, dynamic> notification, String action) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم $action الإشعار: ${notification['title']}'),
        backgroundColor: action == 'accept' 
            ? const Color(AppColors.success)
            : const Color(AppColors.error),
      ),
    );
  }

  void _showNotificationSettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات الإشعارات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            SwitchListTile(
              title: const Text('إشعارات الجلسات'),
              subtitle: const Text('تذكيرات بمواعيد الجلسات'),
              value: true,
              onChanged: (value) {},
            ),
            
            SwitchListTile(
              title: const Text('الإشعارات المهمة'),
              subtitle: const Text('إشعارات التحديثات المهمة'),
              value: true,
              onChanged: (value) {},
            ),
            
            SwitchListTile(
              title: const Text('إشعارات الإنجازات'),
              subtitle: const Text('إشعارات عند تحقيق إنجازات جديدة'),
              value: false,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredNotifications() {
    final allNotifications = _getAllNotifications();
    
    switch (_selectedFilter) {
      case 'غير مقروءة':
        return allNotifications.where((n) => !(n['isRead'] ?? false)).toList();
      case 'مهمة':
        return allNotifications.where((n) => n['isImportant'] ?? false).toList();
      case 'تذكيرات':
        return allNotifications.where((n) => n['type'] == 'reminder').toList();
      default:
        return allNotifications;
    }
  }

  List<Map<String, dynamic>> _getAllNotifications() {
    return [
      {
        'title': 'تذكير: جلسة زراعة الأسنان',
        'message': 'ستبدأ جلسة "أحدث تقنيات زراعة الأسنان" خلال 30 دقيقة في قاعة المحاضرات A',
        'time': 'منذ 5 دقائق',
        'type': 'reminder',
        'isRead': false,
        'isImportant': true,
        'actionable': false,
      },
      {
        'title': 'تحديث في الجدول',
        'message': 'تم تغيير موعد جلسة "التقويم الشفاف" من 2:00 م إلى 3:00 م',
        'time': 'منذ 15 دقيقة',
        'type': 'update',
        'isRead': false,
        'isImportant': true,
        'actionable': false,
      },
      {
        'title': 'إنجاز جديد!',
        'message': 'تهانينا! لقد حصلت على إنجاز "مشارك نشط" لحضورك أكثر من 10 جلسات',
        'time': 'منذ ساعة',
        'type': 'achievement',
        'isRead': false,
        'isImportant': false,
        'actionable': false,
      },
      {
        'title': 'دعوة للمشاركة',
        'message': 'تم دعوتك للمشاركة في جلسة نقاش حول "مستقبل طب الأسنان الرقمي"',
        'time': 'منذ ساعتين',
        'type': 'session',
        'isRead': true,
        'isImportant': false,
        'actionable': true,
      },
      {
        'title': 'تأكيد التسجيل',
        'message': 'تم تأكيد تسجيلك في جلسة "علاج الجذور بالمجهر" بنجاح',
        'time': 'منذ 3 ساعات',
        'type': 'session',
        'isRead': true,
        'isImportant': false,
        'actionable': false,
      },
      {
        'title': 'تذكير: تقييم الجلسة',
        'message': 'لا تنس تقييم جلسة "جراحة اللثة التجميلية" التي حضرتها أمس',
        'time': 'أمس',
        'type': 'reminder',
        'isRead': true,
        'isImportant': false,
        'actionable': false,
      },
    ];
  }
}
