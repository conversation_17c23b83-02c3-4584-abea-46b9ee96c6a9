<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة - IDEC 2025</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="css/admin.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #dc3545 0%, #8b0000 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        .main-content {
            margin-right: 250px;
            padding: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .role-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .btn-action {
            padding: 5px 10px;
            margin: 2px;
            border-radius: 5px;
            font-size: 0.8em;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar position-fixed top-0 end-0 h-100" style="width: 250px; z-index: 1000;">
        <div class="p-3 text-center border-bottom border-light">
            <h4 class="text-white mb-0">
                <i class="fas fa-tooth me-2"></i>
                IDEC 2025
            </h4>
            <small class="text-white-50">لوحة التحكم</small>
        </div>
        
        <div class="p-3">
            <div class="user-info text-center mb-4">
                <div class="user-avatar bg-white text-danger rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user"></i>
                </div>
                <div class="text-white">
                    <div id="userName">المدير العام</div>
                    <small id="userRole" class="text-white-50">مدير النظام</small>
                </div>
            </div>
        </div>

        <nav class="nav flex-column px-3">
            <a class="nav-link active" href="#" data-section="dashboard">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة المعلومات
            </a>
            <a class="nav-link" href="#" data-section="applications">
                <i class="fas fa-file-alt me-2"></i>
                طلبات الاشتراك
                <span class="notification-badge" id="pendingCount">0</span>
            </a>
            <a class="nav-link" href="#" data-section="users">
                <i class="fas fa-users me-2"></i>
                إدارة المستخدمين
            </a>
            <a class="nav-link" href="#" data-section="accounting">
                <i class="fas fa-calculator me-2"></i>
                المحاسبة والمالية
            </a>
            <a class="nav-link" href="#" data-section="content">
                <i class="fas fa-edit me-2"></i>
                إدارة المحتوى
            </a>
            <a class="nav-link" href="#" data-section="reports">
                <i class="fas fa-chart-bar me-2"></i>
                التقارير والإحصائيات
            </a>
            <a class="nav-link" href="#" data-section="settings">
                <i class="fas fa-cog me-2"></i>
                الإعدادات
            </a>
            <hr class="border-light">
            <a class="nav-link text-warning" href="#" onclick="logout()">
                <i class="fas fa-sign-out-alt me-2"></i>
                تسجيل الخروج
            </a>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">مرحباً بك في لوحة التحكم</h2>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <small class="text-muted">آخر تحديث:</small>
                    <span id="lastUpdate" class="fw-bold">الآن</span>
                </div>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="content-section active">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-primary me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="totalUsers">0</h3>
                                <small class="text-muted">إجمالي المستخدمين</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success me-3">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="activeSubscriptions">0</h3>
                                <small class="text-muted">الاشتراكات النشطة</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="pendingApplications">0</h3>
                                <small class="text-muted">طلبات في الانتظار</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-info me-3">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="totalRevenue">0</h3>
                                <small class="text-muted">إجمالي الإيرادات (ريال)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الأنشطة الأخيرة</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivities" class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                جاري التحميل...
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إحصائيات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>معدل القبول</span>
                                    <span id="approvalRate" class="fw-bold text-success">0%</span>
                                </div>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar bg-success" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>معدل الدفع</span>
                                    <span id="paymentRate" class="fw-bold text-info">0%</span>
                                </div>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar bg-info" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>الجلسات المجدولة</span>
                                    <span id="scheduledSessions" class="fw-bold">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Applications Section -->
        <div id="applications" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>طلبات الاشتراك</h3>
                <div class="btn-group">
                    <button class="btn btn-outline-primary" onclick="filterApplications('all')">الكل</button>
                    <button class="btn btn-outline-warning" onclick="filterApplications('pending')">في الانتظار</button>
                    <button class="btn btn-outline-success" onclick="filterApplications('approved')">موافق عليها</button>
                    <button class="btn btn-outline-danger" onclick="filterApplications('rejected')">مرفوضة</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>المؤهل</th>
                                    <th>تاريخ التقديم</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="applicationsTable">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل البيانات...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Section -->
        <div id="users" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>إدارة المستخدمين</h3>
                <button class="btn btn-primary" onclick="showAddUserModal()">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم جديد
                </button>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="userSearch" placeholder="البحث عن مستخدم...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="userStatusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="ACTIVE">نشط</option>
                                <option value="INACTIVE">غير نشط</option>
                                <option value="SUSPENDED">معلق</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="userRoleFilter">
                                <option value="">جميع الأدوار</option>
                                <option value="ATTENDEE">مشارك</option>
                                <option value="SPEAKER">متحدث</option>
                                <option value="ADMIN">مدير</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="searchUsers()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل البيانات...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Accounting Section -->
        <div id="accounting" class="content-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>المحاسبة والمالية</h3>
                <button class="btn btn-success" onclick="showManualReceiptModal()">
                    <i class="fas fa-plus"></i>
                    إنشاء سند يدوي
                </button>
            </div>
            
            <!-- Financial Stats -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-success me-3">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="totalIncome">0</h3>
                                <small class="text-muted">إجمالي الإيرادات</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-warning me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="pendingPayments">0</h3>
                                <small class="text-muted">مدفوعات معلقة</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="d-flex align-items-center">
                            <div class="stats-icon bg-danger me-3">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div>
                                <h3 class="mb-0" id="totalRefunds">0</h3>
                                <small class="text-muted">إجمالي المستردات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">المعاملات المالية</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportFinancialData()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم المعاملة</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTable">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل البيانات...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other sections will be added here -->
        <div id="content" class="content-section">
            <h3>إدارة المحتوى</h3>
            <p>قريباً...</p>
        </div>

        <div id="reports" class="content-section">
            <h3>التقارير والإحصائيات</h3>
            <p>قريباً...</p>
        </div>

        <div id="settings" class="content-section">
            <h3>الإعدادات</h3>
            <p>قريباً...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
