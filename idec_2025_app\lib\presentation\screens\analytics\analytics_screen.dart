import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class AnalyticsScreen extends StatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  State<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends State<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _chartAnimation;
  
  String _selectedPeriod = 'اليوم';
  final List<String> _periods = ['اليوم', 'الأسبوع', 'الشهر', 'الكل'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _chartAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 280,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'الإحصائيات والتحليلات',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _chartAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (0.1 * _chartAnimation.value),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/analytics_bg.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 60),
                              
                              // Analytics Icon
                              Container(
                                padding: const EdgeInsets.all(AppSizes.paddingL),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                  border: Border.all(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    width: 2,
                                  ),
                                ),
                                child: const Icon(
                                  Icons.analytics,
                                  size: 50,
                                  color: Color(AppColors.primaryWhite),
                                ),
                              ),
                              
                              const SizedBox(height: AppSizes.paddingM),
                              
                              // Stats
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('85%', 'معدل الحضور'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('4.8', 'تقييم المؤتمر'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('1,250', 'مشارك نشط'),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                isScrollable: true,
                tabs: const [
                  Tab(text: 'عام'),
                  Tab(text: 'الجلسات'),
                  Tab(text: 'المشاركة'),
                  Tab(text: 'التقييمات'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Period Filter
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Row(
                children: [
                  const Text(
                    'الفترة الزمنية:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: _periods.map((period) {
                          final isSelected = period == _selectedPeriod;
                          return Container(
                            margin: const EdgeInsets.only(right: AppSizes.paddingS),
                            child: FilterChip(
                              label: Text(period),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedPeriod = period;
                                });
                              },
                              backgroundColor: const Color(AppColors.primaryWhite),
                              selectedColor: const Color(AppColors.primaryRed),
                              labelStyle: TextStyle(
                                color: isSelected
                                    ? const Color(AppColors.primaryWhite)
                                    : const Color(AppColors.primaryBlack),
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusL),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _exportData,
                    icon: const Icon(
                      Icons.download,
                      color: Color(AppColors.primaryRed),
                    ),
                    tooltip: 'تصدير البيانات',
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildGeneralTab(),
                    _buildSessionsTab(),
                    _buildEngagementTab(),
                    _buildRatingsTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        children: [
          // Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'إجمالي المشاركين',
                  '1,250',
                  Icons.people,
                  AppColors.info,
                  '+12%',
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildMetricCard(
                  'الجلسات المكتملة',
                  '45',
                  Icons.event_available,
                  AppColors.success,
                  '+8%',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'ساعات التعليم',
                  '120',
                  Icons.school,
                  AppColors.warning,
                  '+15%',
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildMetricCard(
                  'معدل الرضا',
                  '4.8/5',
                  Icons.star,
                  AppColors.primaryRed,
                  '+0.3',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Attendance Chart
          _buildChartCard(
            'معدل الحضور اليومي',
            _buildAttendanceChart(),
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Top Sessions
          _buildTopSessionsCard(),
        ],
      ),
    );
  }

  Widget _buildSessionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        children: [
          // Sessions Overview
          _buildChartCard(
            'إحصائيات الجلسات',
            _buildSessionsChart(),
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Sessions List
          _buildSessionsListCard(),
        ],
      ),
    );
  }

  Widget _buildEngagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        children: [
          // Engagement Metrics
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  'التفاعل',
                  '78%',
                  Icons.thumb_up,
                  AppColors.success,
                  '+5%',
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildMetricCard(
                  'المشاركات',
                  '450',
                  Icons.share,
                  AppColors.info,
                  '+20%',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Engagement Chart
          _buildChartCard(
            'مستوى التفاعل',
            _buildEngagementChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        children: [
          // Ratings Overview
          _buildChartCard(
            'توزيع التقييمات',
            _buildRatingsChart(),
          ),

          const SizedBox(height: AppSizes.paddingL),

          // Detailed Ratings
          _buildDetailedRatingsCard(),
        ],
      ),
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    int color,
    String change,
  ) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          gradient: LinearGradient(
            colors: [
              const Color(AppColors.primaryWhite),
              Color(color).withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(color),
                        Color(color).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(AppColors.primaryWhite),
                    size: 20,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingS,
                    vertical: AppSizes.paddingXS,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(AppColors.success).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                  ),
                  child: Text(
                    change,
                    style: const TextStyle(
                      color: Color(AppColors.success),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppSizes.paddingM),

            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Color(color),
              ),
            ),

            const SizedBox(height: AppSizes.paddingXS),

            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(AppColors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartCard(String title, Widget chart) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          gradient: LinearGradient(
            colors: [
              const Color(AppColors.primaryWhite),
              const Color(AppColors.offWhite),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(AppColors.primaryBlack),
              ),
            ),

            const SizedBox(height: AppSizes.paddingL),

            chart,
          ],
        ),
      ),
    );
  }

  Widget _buildAttendanceChart() {
    return AnimatedBuilder(
      animation: _chartAnimation,
      builder: (context, child) {
        return Container(
          height: 200,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              _buildChartBar('الاثنين', 0.8, AppColors.info),
              _buildChartBar('الثلاثاء', 0.9, AppColors.success),
              _buildChartBar('الأربعاء', 0.7, AppColors.warning),
              _buildChartBar('الخميس', 0.85, AppColors.primaryRed),
              _buildChartBar('الجمعة', 0.6, AppColors.primaryBlack),
            ],
          ),
        );
      },
    );
  }

  Widget _buildChartBar(String label, double value, int color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '${(value * 100).toInt()}%',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.paddingS),
        AnimatedContainer(
          duration: Duration(milliseconds: 1000 + (value * 500).toInt()),
          height: 150 * value * _chartAnimation.value,
          width: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Color(color),
                Color(color).withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(AppSizes.radiusS),
          ),
        ),
        const SizedBox(height: AppSizes.paddingS),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Color(AppColors.grey),
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsChart() {
    return Container(
      height: 200,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildPieChartSegment('مكتملة', 0.6, AppColors.success),
          _buildPieChartSegment('جارية', 0.25, AppColors.warning),
          _buildPieChartSegment('قادمة', 0.15, AppColors.info),
        ],
      ),
    );
  }

  Widget _buildPieChartSegment(String label, double value, int color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AnimatedBuilder(
          animation: _chartAnimation,
          builder: (context, child) {
            return Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    Color(color),
                    Color(color).withValues(alpha: 0.7),
                  ],
                ),
              ),
              child: Center(
                child: Text(
                  '${(value * 100 * _chartAnimation.value).toInt()}%',
                  style: const TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            );
          },
        ),
        const SizedBox(height: AppSizes.paddingS),
        Text(
          label,
          style: TextStyle(
            color: Color(color),
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildEngagementChart() {
    return Container(
      height: 200,
      child: Column(
        children: [
          _buildEngagementBar('الإعجابات', 0.85, AppColors.primaryRed),
          const SizedBox(height: AppSizes.paddingM),
          _buildEngagementBar('التعليقات', 0.65, AppColors.info),
          const SizedBox(height: AppSizes.paddingM),
          _buildEngagementBar('المشاركات', 0.45, AppColors.success),
          const SizedBox(height: AppSizes.paddingM),
          _buildEngagementBar('الحفظ', 0.35, AppColors.warning),
        ],
      ),
    );
  }

  Widget _buildEngagementBar(String label, double value, int color) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: AppSizes.paddingM),
        Expanded(
          child: AnimatedBuilder(
            animation: _chartAnimation,
            builder: (context, child) {
              return Container(
                height: 20,
                decoration: BoxDecoration(
                  color: const Color(AppColors.lightGrey),
                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: value * _chartAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(color),
                          Color(color).withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(AppSizes.radiusS),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(width: AppSizes.paddingM),
        Text(
          '${(value * 100).toInt()}%',
          style: TextStyle(
            color: Color(color),
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildRatingsChart() {
    return Container(
      height: 200,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          _buildRatingBar('5⭐', 0.6, AppColors.success),
          _buildRatingBar('4⭐', 0.8, AppColors.info),
          _buildRatingBar('3⭐', 0.4, AppColors.warning),
          _buildRatingBar('2⭐', 0.2, AppColors.error),
          _buildRatingBar('1⭐', 0.1, AppColors.primaryBlack),
        ],
      ),
    );
  }

  Widget _buildRatingBar(String label, double value, int color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          '${(value * 100).toInt()}%',
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppSizes.paddingS),
        AnimatedContainer(
          duration: Duration(milliseconds: 1000 + (value * 500).toInt()),
          height: 120 * value * _chartAnimation.value,
          width: 30,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Color(color),
                Color(color).withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(AppSizes.radiusS),
          ),
        ),
        const SizedBox(height: AppSizes.paddingS),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Color(AppColors.grey),
          ),
        ),
      ],
    );
  }

  Widget _buildTopSessionsCard() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أفضل الجلسات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppSizes.paddingM),

            _buildSessionItem('أحدث تقنيات زراعة الأسنان', '95%', AppColors.success),
            _buildSessionItem('التقويم الشفاف المتقدم', '92%', AppColors.info),
            _buildSessionItem('علاج الجذور بالمجهر', '89%', AppColors.warning),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionItem(String title, String rating, int color) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Color(color),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSizes.paddingM),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            rating,
            style: TextStyle(
              color: Color(color),
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionsListCard() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الجلسات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppSizes.paddingM),

            const Text('قريباً...'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedRatingsCard() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل التقييمات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppSizes.paddingM),

            _buildRatingDetail('جودة المحتوى', 4.9, AppColors.success),
            _buildRatingDetail('أداء المتحدثين', 4.8, AppColors.info),
            _buildRatingDetail('التنظيم', 4.7, AppColors.warning),
            _buildRatingDetail('التقنيات المستخدمة', 4.6, AppColors.primaryRed),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingDetail(String category, double rating, int color) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Expanded(
            child: Text(
              category,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Row(
            children: List.generate(5, (index) {
              return Icon(
                index < rating.floor() ? Icons.star : Icons.star_border,
                color: Color(color),
                size: 16,
              );
            }),
          ),
          const SizedBox(width: AppSizes.paddingS),
          Text(
            rating.toString(),
            style: TextStyle(
              color: Color(color),
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.download,
              size: 60,
              color: Color(AppColors.info),
            ),
            SizedBox(height: AppSizes.paddingM),
            Text(
              'سيتم تصدير البيانات بصيغة Excel',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تصدير البيانات بنجاح'),
                  backgroundColor: Color(AppColors.success),
                ),
              );
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }
}
