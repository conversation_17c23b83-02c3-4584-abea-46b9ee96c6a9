const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gamificationService = require('./gamificationService');

const prisma = new PrismaClient();

class QAService {
  // Create Q&A session
  async createQASession(sessionData, createdBy) {
    try {
      const qaSession = await prisma.qASession.create({
        data: {
          ...sessionData,
          createdBy
        },
        include: {
          session: {
            select: {
              titleAr: true,
              titleEn: true
            }
          },
          creator: {
            select: {
              arabicName: true,
              englishName: true
            }
          }
        }
      });

      logger.info(`Q&A session created: ${qaSession.id} by user ${createdBy}`);
      return qaSession;
    } catch (error) {
      logger.error('Error creating Q&A session:', error);
      throw error;
    }
  }

  // Get Q&A session by ID
  async getQASessionById(sessionId, userId = null) {
    try {
      const qaSession = await prisma.qASession.findUnique({
        where: { id: sessionId },
        include: {
          session: {
            select: {
              titleAr: true,
              titleEn: true
            }
          },
          creator: {
            select: {
              arabicName: true,
              englishName: true
            }
          },
          questions: {
            where: {
              status: { in: ['APPROVED', 'ANSWERED', 'HIGHLIGHTED'] }
            },
            include: {
              user: {
                select: {
                  arabicName: true,
                  englishName: true
                }
              },
              answerer: {
                select: {
                  arabicName: true,
                  englishName: true
                }
              },
              _count: {
                select: {
                  votes: true
                }
              }
            },
            orderBy: [
              { isHighlighted: 'desc' },
              { upvotes: 'desc' },
              { createdAt: 'asc' }
            ]
          },
          _count: {
            select: { questions: true }
          }
        }
      });

      if (!qaSession) {
        throw new Error('Q&A session not found');
      }

      // Check user's vote status for each question
      if (userId) {
        const questionIds = qaSession.questions.map(q => q.id);
        const userVotes = await prisma.questionVote.findMany({
          where: {
            userId,
            questionId: { in: questionIds }
          }
        });

        const voteMap = {};
        userVotes.forEach(vote => {
          voteMap[vote.questionId] = vote.voteType;
        });

        qaSession.questions = qaSession.questions.map(question => ({
          ...question,
          userVote: voteMap[question.id] || null
        }));
      }

      return qaSession;
    } catch (error) {
      logger.error('Error getting Q&A session by ID:', error);
      throw error;
    }
  }

  // Submit question
  async submitQuestion(qaSessionId, userId, content, isAnonymous = false) {
    try {
      const qaSession = await prisma.qASession.findUnique({
        where: { id: qaSessionId }
      });

      if (!qaSession) {
        throw new Error('Q&A session not found');
      }

      if (!qaSession.isActive || !qaSession.isLive) {
        throw new Error('Q&A session is not active or live');
      }

      // Check if session has ended
      if (qaSession.endTime && new Date() > qaSession.endTime) {
        throw new Error('Q&A session has ended');
      }

      const question = await prisma.question.create({
        data: {
          qaSessionId,
          userId: isAnonymous ? null : userId,
          content,
          isAnonymous,
          status: qaSession.moderationRequired ? 'PENDING' : 'APPROVED'
        },
        include: {
          user: {
            select: {
              arabicName: true,
              englishName: true
            }
          }
        }
      });

      // Award points for asking question
      if (userId) {
        await gamificationService.awardPoints(
          userId,
          'QUESTION_ASKED',
          5,
          'Asked a question in Q&A session'
        );
      }

      logger.info(`Question submitted to Q&A session ${qaSessionId}`);
      return question;
    } catch (error) {
      logger.error('Error submitting question:', error);
      throw error;
    }
  }

  // Vote on question
  async voteOnQuestion(questionId, userId, voteType) {
    try {
      const question = await prisma.question.findUnique({
        where: { id: questionId }
      });

      if (!question) {
        throw new Error('Question not found');
      }

      // Check if user has already voted
      const existingVote = await prisma.questionVote.findUnique({
        where: {
          questionId_userId: {
            questionId,
            userId
          }
        }
      });

      let updatedQuestion;

      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Remove vote if same type
          await prisma.questionVote.delete({
            where: { id: existingVote.id }
          });

          // Update question vote counts
          const updateData = {};
          if (voteType === 'UPVOTE') {
            updateData.upvotes = { decrement: 1 };
          } else {
            updateData.downvotes = { decrement: 1 };
          }

          updatedQuestion = await prisma.question.update({
            where: { id: questionId },
            data: updateData
          });
        } else {
          // Change vote type
          await prisma.questionVote.update({
            where: { id: existingVote.id },
            data: { voteType }
          });

          // Update question vote counts
          const updateData = {};
          if (voteType === 'UPVOTE') {
            updateData.upvotes = { increment: 1 };
            updateData.downvotes = { decrement: 1 };
          } else {
            updateData.upvotes = { decrement: 1 };
            updateData.downvotes = { increment: 1 };
          }

          updatedQuestion = await prisma.question.update({
            where: { id: questionId },
            data: updateData
          });
        }
      } else {
        // Create new vote
        await prisma.questionVote.create({
          data: {
            questionId,
            userId,
            voteType
          }
        });

        // Update question vote counts
        const updateData = {};
        if (voteType === 'UPVOTE') {
          updateData.upvotes = { increment: 1 };
        } else {
          updateData.downvotes = { increment: 1 };
        }

        updatedQuestion = await prisma.question.update({
          where: { id: questionId },
          data: updateData
        });
      }

      logger.info(`User ${userId} voted ${voteType} on question ${questionId}`);
      return updatedQuestion;
    } catch (error) {
      logger.error('Error voting on question:', error);
      throw error;
    }
  }

  // Answer question
  async answerQuestion(questionId, userId, answer) {
    try {
      const question = await prisma.question.findUnique({
        where: { id: questionId },
        include: {
          qaSession: true
        }
      });

      if (!question) {
        throw new Error('Question not found');
      }

      // Check if user is authorized to answer (session creator or admin)
      if (question.qaSession.createdBy !== userId) {
        // Check if user has admin role
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });

        if (!user || !['SUPER_ADMIN', 'ADMIN', 'MODERATOR'].includes(user.role)) {
          throw new Error('Unauthorized to answer this question');
        }
      }

      const answeredQuestion = await prisma.question.update({
        where: { id: questionId },
        data: {
          answer,
          answeredBy: userId,
          answeredAt: new Date(),
          status: 'ANSWERED'
        },
        include: {
          user: {
            select: {
              arabicName: true,
              englishName: true
            }
          },
          answerer: {
            select: {
              arabicName: true,
              englishName: true
            }
          }
        }
      });

      logger.info(`Question ${questionId} answered by user ${userId}`);
      return answeredQuestion;
    } catch (error) {
      logger.error('Error answering question:', error);
      throw error;
    }
  }

  // Moderate question (approve/reject)
  async moderateQuestion(questionId, userId, status) {
    try {
      const question = await prisma.question.findUnique({
        where: { id: questionId },
        include: {
          qaSession: true
        }
      });

      if (!question) {
        throw new Error('Question not found');
      }

      // Check if user is authorized to moderate
      if (question.qaSession.createdBy !== userId) {
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });

        if (!user || !['SUPER_ADMIN', 'ADMIN', 'MODERATOR'].includes(user.role)) {
          throw new Error('Unauthorized to moderate this question');
        }
      }

      const moderatedQuestion = await prisma.question.update({
        where: { id: questionId },
        data: { status }
      });

      logger.info(`Question ${questionId} moderated to ${status} by user ${userId}`);
      return moderatedQuestion;
    } catch (error) {
      logger.error('Error moderating question:', error);
      throw error;
    }
  }

  // Highlight question
  async highlightQuestion(questionId, userId, isHighlighted = true) {
    try {
      const question = await prisma.question.findUnique({
        where: { id: questionId },
        include: {
          qaSession: true
        }
      });

      if (!question) {
        throw new Error('Question not found');
      }

      // Check authorization
      if (question.qaSession.createdBy !== userId) {
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });

        if (!user || !['SUPER_ADMIN', 'ADMIN', 'MODERATOR'].includes(user.role)) {
          throw new Error('Unauthorized to highlight this question');
        }
      }

      const highlightedQuestion = await prisma.question.update({
        where: { id: questionId },
        data: {
          isHighlighted,
          status: isHighlighted ? 'HIGHLIGHTED' : question.status
        }
      });

      logger.info(`Question ${questionId} ${isHighlighted ? 'highlighted' : 'unhighlighted'} by user ${userId}`);
      return highlightedQuestion;
    } catch (error) {
      logger.error('Error highlighting question:', error);
      throw error;
    }
  }

  // Get pending questions for moderation
  async getPendingQuestions(qaSessionId, userId) {
    try {
      const qaSession = await prisma.qASession.findUnique({
        where: { id: qaSessionId }
      });

      if (!qaSession) {
        throw new Error('Q&A session not found');
      }

      // Check authorization
      if (qaSession.createdBy !== userId) {
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });

        if (!user || !['SUPER_ADMIN', 'ADMIN', 'MODERATOR'].includes(user.role)) {
          throw new Error('Unauthorized to view pending questions');
        }
      }

      const questions = await prisma.question.findMany({
        where: {
          qaSessionId,
          status: 'PENDING'
        },
        include: {
          user: {
            select: {
              arabicName: true,
              englishName: true
            }
          }
        },
        orderBy: { createdAt: 'asc' }
      });

      return questions;
    } catch (error) {
      logger.error('Error getting pending questions:', error);
      throw error;
    }
  }

  // Toggle Q&A session live status
  async toggleQASessionLive(sessionId, isLive, userId) {
    try {
      const qaSession = await prisma.qASession.findUnique({
        where: { id: sessionId }
      });

      if (!qaSession) {
        throw new Error('Q&A session not found');
      }

      if (qaSession.createdBy !== userId) {
        throw new Error('Unauthorized to modify this Q&A session');
      }

      const updatedSession = await prisma.qASession.update({
        where: { id: sessionId },
        data: {
          isLive,
          startTime: isLive ? new Date() : qaSession.startTime,
          endTime: !isLive ? new Date() : null
        }
      });

      logger.info(`Q&A session ${sessionId} ${isLive ? 'started' : 'stopped'} by user ${userId}`);
      return updatedSession;
    } catch (error) {
      logger.error('Error toggling Q&A session live status:', error);
      throw error;
    }
  }

  // Get Q&A statistics
  async getQAStats() {
    try {
      const [
        totalSessions,
        activeSessions,
        liveSessions,
        totalQuestions,
        answeredQuestions,
        pendingQuestions
      ] = await Promise.all([
        prisma.qASession.count(),
        prisma.qASession.count({ where: { isActive: true } }),
        prisma.qASession.count({ where: { isLive: true } }),
        prisma.question.count(),
        prisma.question.count({ where: { status: 'ANSWERED' } }),
        prisma.question.count({ where: { status: 'PENDING' } })
      ]);

      return {
        totalSessions,
        activeSessions,
        liveSessions,
        totalQuestions,
        answeredQuestions,
        pendingQuestions
      };
    } catch (error) {
      logger.error('Error getting Q&A stats:', error);
      throw error;
    }
  }
}

module.exports = new QAService();
