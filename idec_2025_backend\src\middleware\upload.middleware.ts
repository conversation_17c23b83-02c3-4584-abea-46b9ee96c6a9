import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { config } from '@/config/config';
import { ApiError } from '@/utils/ApiError';
import { logger } from '@/utils/logger';

// Ensure upload directory exists
const uploadDir = config.upload.path;
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let subDir = 'general';
    
    // Determine subdirectory based on file type or route
    if (file.fieldname === 'profileImage' || file.fieldname === 'image') {
      subDir = 'profiles';
    } else if (file.fieldname === 'documents') {
      subDir = 'documents';
    } else if (file.fieldname === 'images') {
      subDir = 'posts';
    } else if (req.path.includes('/library/')) {
      subDir = 'library';
    } else if (req.path.includes('/news/')) {
      subDir = 'news';
    }
    
    const fullPath = path.join(uploadDir, subDir);
    
    // Create subdirectory if it doesn't exist
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
    
    cb(null, fullPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const name = file.fieldname + '-' + uniqueSuffix + ext;
    cb(null, name);
  },
});

// File filter function
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Get file extension
  const ext = path.extname(file.originalname).toLowerCase().substring(1);
  
  // Check if file type is allowed
  if (config.upload.allowedTypes.includes(ext)) {
    cb(null, true);
  } else {
    cb(new ApiError(400, `File type .${ext} is not allowed`, 'INVALID_FILE_TYPE'));
  }
};

// Create multer instance
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // Maximum 10 files per request
  },
});

// File validation middleware
export const validateFile = (req: any, res: any, next: any) => {
  if (!req.file && !req.files) {
    return next();
  }
  
  const files = req.files || [req.file];
  
  for (const file of files) {
    // Additional validation can be added here
    logger.info(`File uploaded: ${file.filename}`, {
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      userId: req.user?.id,
    });
  }
  
  next();
};

// Image-specific upload middleware
export const uploadImage = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    const ext = path.extname(file.originalname).toLowerCase().substring(1);
    
    if (allowedImageTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new ApiError(400, 'Only image files are allowed', 'INVALID_IMAGE_TYPE'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB for images
  },
});

// Document-specific upload middleware
export const uploadDocument = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedDocTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
    const ext = path.extname(file.originalname).toLowerCase().substring(1);
    
    if (allowedDocTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new ApiError(400, 'Only document files are allowed', 'INVALID_DOCUMENT_TYPE'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB for documents
  },
});

// Video-specific upload middleware
export const uploadVideo = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedVideoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    const ext = path.extname(file.originalname).toLowerCase().substring(1);
    
    if (allowedVideoTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new ApiError(400, 'Only video files are allowed', 'INVALID_VIDEO_TYPE'));
    }
  },
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB for videos
  },
});

// Helper function to delete file
export const deleteFile = (filePath: string): void => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`File deleted: ${filePath}`);
    }
  } catch (error) {
    logger.error(`Failed to delete file: ${filePath}`, error);
  }
};

// Helper function to get file URL
export const getFileUrl = (filePath: string): string => {
  if (!filePath) return '';
  
  // Convert absolute path to relative URL
  const relativePath = path.relative(config.upload.path, filePath);
  return `/uploads/${relativePath.replace(/\\/g, '/')}`;
};

// Helper function to validate file size
export const validateFileSize = (file: Express.Multer.File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

// Helper function to validate file type
export const validateFileType = (file: Express.Multer.File, allowedTypes: string[]): boolean => {
  const ext = path.extname(file.originalname).toLowerCase().substring(1);
  return allowedTypes.includes(ext);
};

// Cleanup old files (can be used in a cron job)
export const cleanupOldFiles = (directory: string, maxAge: number = 30): void => {
  try {
    const files = fs.readdirSync(directory);
    const now = Date.now();
    
    files.forEach(file => {
      const filePath = path.join(directory, file);
      const stats = fs.statSync(filePath);
      const age = (now - stats.mtime.getTime()) / (1000 * 60 * 60 * 24); // Age in days
      
      if (age > maxAge) {
        fs.unlinkSync(filePath);
        logger.info(`Cleaned up old file: ${filePath}`);
      }
    });
  } catch (error) {
    logger.error(`Failed to cleanup old files in ${directory}:`, error);
  }
};
