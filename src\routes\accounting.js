const express = require('express');
const { body, param, query } = require('express-validator');
const router = express.Router();

const accountingController = require('../controllers/accountingController');
const { authenticate, requireRole } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');

// Validation rules
const createManualReceiptValidation = [
  body('userId').notEmpty().withMessage('معرف المستخدم مطلوب'),
  body('amount').isFloat({ min: 0.01 }).withMessage('المبلغ يجب أن يكون رقم موجب'),
  body('currency').optional().isIn(['SAR', 'USD', 'EUR']).withMessage('العملة غير صحيحة'),
  body('paymentMethod').isIn(['CASH', 'BANK_TRANSFER', 'MANUAL']).withMessage('طريقة الدفع غير صحيحة'),
  body('referenceNumber').optional().isString().withMessage('رقم المرجع يجب أن يكون نص'),
  body('purpose').isIn(['CONFERENCE_SUBSCRIPTION', 'COURSE_ENROLLMENT']).withMessage('غرض الدفع غير صحيح'),
  body('description').notEmpty().withMessage('وصف المعاملة مطلوب'),
  body('notes').optional().isString().withMessage('الملاحظات يجب أن تكون نص'),
  handleValidationErrors
];

const processRefundValidation = [
  param('paymentId').notEmpty().withMessage('معرف الدفع مطلوب'),
  body('amount').isFloat({ min: 0.01 }).withMessage('مبلغ الاسترداد يجب أن يكون رقم موجب'),
  body('reason').notEmpty().withMessage('سبب الاسترداد مطلوب'),
  body('notes').optional().isString().withMessage('الملاحظات يجب أن تكون نص'),
  handleValidationErrors
];

const financialReportsValidation = [
  query('startDate').optional().isISO8601().withMessage('تاريخ البداية غير صحيح'),
  query('endDate').optional().isISO8601().withMessage('تاريخ النهاية غير صحيح'),
  query('type').optional().isIn(['REGISTRATION_FEE', 'COURSE_FEE', 'REFUND', 'PENALTY', 'DISCOUNT']).withMessage('نوع المعاملة غير صحيح'),
  query('status').optional().isIn(['PENDING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED']).withMessage('حالة المعاملة غير صحيحة'),
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة غير صحيح'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد النتائج غير صحيح'),
  handleValidationErrors
];

// Routes for Accountant
router.use(authenticate);
router.use(requireRole(['accountant', 'super_admin', 'admin']));

// Financial dashboard
router.get('/dashboard', accountingController.getFinancialDashboard);

// Create manual receipt
router.post('/receipts/manual',
  createManualReceiptValidation,
  accountingController.createManualReceipt
);

// Get financial reports
router.get('/reports',
  financialReportsValidation,
  accountingController.getFinancialReports
);

// Get payment details
router.get('/payments/:paymentId', accountingController.getPaymentDetails);

// Process refund
router.post('/payments/:paymentId/refund',
  processRefundValidation,
  accountingController.processRefund
);

// Export financial data
router.get('/export',
  financialReportsValidation,
  accountingController.exportFinancialData
);

module.exports = router;
