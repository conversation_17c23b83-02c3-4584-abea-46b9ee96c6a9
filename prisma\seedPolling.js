const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedPolling() {
  console.log('📊 Seeding polling and Q&A data...');

  try {
    // Get first user and session for demo data
    const firstUser = await prisma.user.findFirst();
    const firstSession = await prisma.session.findFirst();

    if (!firstUser || !firstSession) {
      console.log('⚠️ No users or sessions found. Skipping polling seed.');
      return;
    }

    // Create sample polls
    const polls = [
      {
        sessionId: firstSession.id,
        title: 'What is your experience level with digital dentistry?',
        titleAr: 'ما هو مستوى خبرتك في طب الأسنان الرقمي؟',
        titleEn: 'What is your experience level with digital dentistry?',
        description: 'Help us understand the audience expertise level',
        descriptionAr: 'ساعدنا في فهم مستوى خبرة الحضور',
        descriptionEn: 'Help us understand the audience expertise level',
        type: 'SINGLE_CHOICE',
        isAnonymous: true,
        allowMultiple: false,
        createdBy: firstUser.id,
        options: [
          {
            text: 'Beginner (0-2 years)',
            textAr: 'مبتدئ (0-2 سنوات)',
            textEn: 'Beginner (0-2 years)',
            order: 0
          },
          {
            text: 'Intermediate (3-5 years)',
            textAr: 'متوسط (3-5 سنوات)',
            textEn: 'Intermediate (3-5 years)',
            order: 1
          },
          {
            text: 'Advanced (6-10 years)',
            textAr: 'متقدم (6-10 سنوات)',
            textEn: 'Advanced (6-10 years)',
            order: 2
          },
          {
            text: 'Expert (10+ years)',
            textAr: 'خبير (أكثر من 10 سنوات)',
            textEn: 'Expert (10+ years)',
            order: 3
          }
        ]
      },
      {
        sessionId: firstSession.id,
        title: 'Rate this presentation',
        titleAr: 'قيم هذا العرض التقديمي',
        titleEn: 'Rate this presentation',
        description: 'Please rate the quality of this presentation',
        descriptionAr: 'يرجى تقييم جودة هذا العرض التقديمي',
        descriptionEn: 'Please rate the quality of this presentation',
        type: 'RATING',
        isAnonymous: true,
        allowMultiple: false,
        createdBy: firstUser.id,
        options: []
      },
      {
        sessionId: firstSession.id,
        title: 'Do you agree with the presented findings?',
        titleAr: 'هل توافق على النتائج المعروضة؟',
        titleEn: 'Do you agree with the presented findings?',
        description: 'Quick yes/no poll about the research findings',
        descriptionAr: 'استطلاع سريع بنعم/لا حول نتائج البحث',
        descriptionEn: 'Quick yes/no poll about the research findings',
        type: 'YES_NO',
        isAnonymous: true,
        allowMultiple: false,
        createdBy: firstUser.id,
        options: [
          {
            text: 'Yes',
            textAr: 'نعم',
            textEn: 'Yes',
            order: 0
          },
          {
            text: 'No',
            textAr: 'لا',
            textEn: 'No',
            order: 1
          }
        ]
      },
      {
        sessionId: firstSession.id,
        title: 'What topics would you like to see in future sessions?',
        titleAr: 'ما هي المواضيع التي تود رؤيتها في الجلسات المستقبلية؟',
        titleEn: 'What topics would you like to see in future sessions?',
        description: 'Share your suggestions for future conference topics',
        descriptionAr: 'شارك اقتراحاتك لمواضيع المؤتمر المستقبلية',
        descriptionEn: 'Share your suggestions for future conference topics',
        type: 'TEXT',
        isAnonymous: false,
        allowMultiple: false,
        createdBy: firstUser.id,
        options: []
      }
    ];

    // Create polls with options
    for (const pollData of polls) {
      try {
        const { options, ...pollInfo } = pollData;
        
        const poll = await prisma.poll.create({
          data: {
            ...pollInfo,
            options: {
              create: options
            }
          }
        });

        console.log(`✅ Created poll: ${poll.titleEn}`);
      } catch (error) {
        console.error(`❌ Error creating poll:`, error.message);
      }
    }

    // Create sample Q&A sessions
    const qaSessions = [
      {
        sessionId: firstSession.id,
        title: 'Q&A Session - Digital Dentistry Innovations',
        titleAr: 'جلسة أسئلة وأجوبة - ابتكارات طب الأسنان الرقمي',
        titleEn: 'Q&A Session - Digital Dentistry Innovations',
        description: 'Ask questions about the latest innovations in digital dentistry',
        isActive: true,
        isLive: false,
        allowAnonymous: true,
        moderationRequired: true,
        createdBy: firstUser.id
      },
      {
        sessionId: firstSession.id,
        title: 'Open Discussion - Future of Dental Practice',
        titleAr: 'نقاش مفتوح - مستقبل ممارسة طب الأسنان',
        titleEn: 'Open Discussion - Future of Dental Practice',
        description: 'Open discussion about the future trends in dental practice',
        isActive: true,
        isLive: false,
        allowAnonymous: false,
        moderationRequired: false,
        createdBy: firstUser.id
      }
    ];

    // Create Q&A sessions
    for (const qaSessionData of qaSessions) {
      try {
        const qaSession = await prisma.qASession.create({
          data: qaSessionData
        });

        console.log(`✅ Created Q&A session: ${qaSession.titleEn}`);

        // Create sample questions for each Q&A session
        const sampleQuestions = [
          {
            qaSessionId: qaSession.id,
            userId: firstUser.id,
            content: 'What are the main advantages of using CAD/CAM technology in dental practice?',
            isAnonymous: false,
            status: 'APPROVED',
            upvotes: 5,
            downvotes: 0
          },
          {
            qaSessionId: qaSession.id,
            userId: firstUser.id,
            content: 'How do you see AI integration in dental diagnostics in the next 5 years?',
            isAnonymous: false,
            status: 'APPROVED',
            upvotes: 8,
            downvotes: 1
          },
          {
            qaSessionId: qaSession.id,
            userId: null, // Anonymous question
            content: 'What are the cost considerations when implementing digital workflows?',
            isAnonymous: true,
            status: 'PENDING',
            upvotes: 3,
            downvotes: 0
          }
        ];

        // Create sample questions
        for (const questionData of sampleQuestions) {
          try {
            const question = await prisma.question.create({
              data: questionData
            });

            console.log(`✅ Created question for Q&A session: ${qaSession.titleEn}`);
          } catch (error) {
            console.error(`❌ Error creating question:`, error.message);
          }
        }

      } catch (error) {
        console.error(`❌ Error creating Q&A session:`, error.message);
      }
    }

    console.log('📊 Polling and Q&A seeding completed!');

  } catch (error) {
    console.error('❌ Error in polling seed:', error);
  }
}

module.exports = { seedPolling };

// Run if called directly
if (require.main === module) {
  seedPolling()
    .catch((e) => {
      console.error('❌ Error seeding polling:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
