const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

let prisma;

const connectDatabase = async () => {
  try {
    if (!prisma) {
      prisma = new PrismaClient({
        log: process.env.NODE_ENV === 'development' 
          ? ['query', 'info', 'warn', 'error']
          : ['error'],
        errorFormat: 'pretty',
      });

      // Test the connection
      await prisma.$connect();
      
      // Add middleware for logging
      prisma.$use(async (params, next) => {
        const before = Date.now();
        const result = await next(params);
        const after = Date.now();
        
        if (process.env.NODE_ENV === 'development') {
          logger.debug(`Query ${params.model}.${params.action} took ${after - before}ms`);
        }
        
        return result;
      });

      logger.info('Database connection established successfully');
    }
    
    return prisma;
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

const disconnectDatabase = async () => {
  try {
    if (prisma) {
      await prisma.$disconnect();
      logger.info('Database connection closed');
    }
  } catch (error) {
    logger.error('Error closing database connection:', error);
  }
};

const getDatabase = () => {
  if (!prisma) {
    throw new Error('Database not connected. Call connectDatabase() first.');
  }
  return prisma;
};

// Health check function
const checkDatabaseHealth = async () => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    logger.error('Database health check failed:', error);
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
};

module.exports = {
  connectDatabase,
  disconnectDatabase,
  getDatabase,
  checkDatabaseHealth,
  prisma: () => getDatabase()
};
