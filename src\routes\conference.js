const express = require('express');
const { authenticate, optionalAuth } = require('../middleware/auth');
const conferenceController = require('../controllers/conferenceController');

const router = express.Router();

// Public routes (no authentication required)
router.get('/sessions', conferenceController.getSessions);
router.get('/sessions/search', conferenceController.searchSessions);
router.get('/sessions/:id', conferenceController.getSessionById);
router.get('/sessions/:id/stats', conferenceController.getSessionStats);
router.get('/speakers', conferenceController.getSpeakers);
router.get('/speakers/:id', conferenceController.getSpeakerById);

// Protected routes (authentication required)
router.post('/sessions/:id/attend', authenticate, conferenceController.registerAttendance);
router.get('/sessions/:id/qr', authenticate, conferenceController.getSessionQR);
router.post('/sessions/:id/rate', authenticate, conferenceController.rateSession);
router.get('/my-agenda', authenticate, conferenceController.getPersonalAgenda);
router.post('/my-agenda/add', authenticate, conferenceController.addToAgenda);
router.delete('/my-agenda/:sessionId', authenticate, conferenceController.removeFromAgenda);

// QR Code attendance routes
router.post('/sessions/:id/checkin', authenticate, conferenceController.checkInToSession);
router.post('/sessions/:id/checkout', authenticate, conferenceController.checkOutFromSession);



module.exports = router;
