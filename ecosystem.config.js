module.exports = {
  apps: [
    {
      name: 'idec2025-api',
      script: './src/server.js',
      instances: process.env.NODE_ENV === 'production' ? 'max' : 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      // Logging
      error_file: './logs/pm2-err.log',
      out_file: './logs/pm2-out.log',
      log_file: './logs/pm2-combined.log',
      time: true,
      
      // Memory and CPU limits
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      
      // Auto restart settings
      autorestart: true,
      watch: process.env.NODE_ENV === 'development' ? ['src'] : false,
      ignore_watch: ['node_modules', 'logs', 'uploads', 'backups'],
      watch_options: {
        followSymlinks: false
      },
      
      // Graceful shutdown
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Environment specific settings
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Advanced settings
      instance_var: 'INSTANCE_ID',
      source_map_support: true,
      
      // Cron restart (daily at 3 AM)
      cron_restart: process.env.NODE_ENV === 'production' ? '0 3 * * *' : null
    },
    
    {
      name: 'idec2025-admin',
      script: './src/admin-server.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      // Logging
      error_file: './logs/admin-pm2-err.log',
      out_file: './logs/admin-pm2-out.log',
      log_file: './logs/admin-pm2-combined.log',
      time: true,
      
      // Memory limits
      max_memory_restart: '512M',
      
      // Auto restart settings
      autorestart: true,
      watch: process.env.NODE_ENV === 'development' ? ['src'] : false,
      ignore_watch: ['node_modules', 'logs', 'uploads', 'backups'],
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 5
    },
    
    {
      name: 'idec2025-worker',
      script: './src/workers/background-worker.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      // Logging
      error_file: './logs/worker-pm2-err.log',
      out_file: './logs/worker-pm2-out.log',
      log_file: './logs/worker-pm2-combined.log',
      time: true,
      
      // Memory limits
      max_memory_restart: '256M',
      
      // Auto restart settings
      autorestart: true,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 3,
      
      // Cron restart (daily at 2 AM)
      cron_restart: process.env.NODE_ENV === 'production' ? '0 2 * * *' : null
    }
  ],

  deploy: {
    production: {
      user: 'idec-admin',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: '**************:your-repo/idec2025-backend.git',
      path: '/var/www/idec2025',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npx prisma generate && npx prisma migrate deploy && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes'
    },
    
    staging: {
      user: 'idec-admin',
      host: ['staging-server-ip'],
      ref: 'origin/develop',
      repo: '**************:your-repo/idec2025-backend.git',
      path: '/var/www/idec2025-staging',
      'post-deploy': 'npm install && npx prisma generate && npx prisma migrate deploy && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};
