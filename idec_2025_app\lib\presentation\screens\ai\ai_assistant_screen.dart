import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class AIAssistantScreen extends StatefulWidget {
  const AIAssistantScreen({super.key});

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _typingController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _typingAnimation;
  
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  bool _isTyping = false;
  
  final List<Map<String, dynamic>> _messages = [
    {
      'text': 'مرحباً! أنا مساعدك الذكي في مؤتمر IDEC 2025. كيف يمكنني مساعدتك اليوم؟',
      'isUser': false,
      'timestamp': DateTime.now().subtract(const Duration(minutes: 1)),
      'type': 'text',
    },
  ];
  
  final List<Map<String, dynamic>> _quickActions = [
    {
      'title': 'جدول المؤتمر',
      'icon': Icons.schedule,
      'color': AppColors.info,
      'action': 'schedule',
    },
    {
      'title': 'المتحدثون',
      'icon': Icons.people,
      'color': AppColors.success,
      'action': 'speakers',
    },
    {
      'title': 'الخريطة',
      'icon': Icons.map,
      'color': AppColors.warning,
      'action': 'map',
    },
    {
      'title': 'التوصيات',
      'icon': Icons.recommend,
      'color': AppColors.primaryRed,
      'action': 'recommendations',
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _typingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _typingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _typingController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _typingController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(AppColors.primaryRed),
              Color(AppColors.darkRed),
              Color(AppColors.primaryBlack),
            ],
            stops: [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingM),
                    // AI Avatar
                    Container(
                      padding: const EdgeInsets.all(AppSizes.paddingM),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                            const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                      ),
                      child: const Icon(
                        Icons.smart_toy,
                        color: Color(AppColors.primaryWhite),
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingM),
                    Expanded(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'المساعد الذكي',
                              style: TextStyle(
                                color: Color(AppColors.primaryWhite),
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _isTyping ? 'يكتب...' : 'متاح الآن',
                              style: TextStyle(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _showAISettings,
                      icon: const Icon(
                        Icons.settings,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Messages Area
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
                  decoration: const BoxDecoration(
                    color: Color(AppColors.primaryWhite),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppSizes.radiusL),
                      topRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Quick Actions
                      Container(
                        padding: const EdgeInsets.all(AppSizes.paddingM),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: _quickActions.map((action) {
                              return Container(
                                margin: const EdgeInsets.only(right: AppSizes.paddingS),
                                child: _buildQuickActionChip(action),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      
                      const Divider(height: 1),
                      
                      // Messages List
                      Expanded(
                        child: ListView.builder(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(AppSizes.paddingM),
                          itemCount: _messages.length + (_isTyping ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index == _messages.length && _isTyping) {
                              return _buildTypingIndicator();
                            }
                            return _buildMessageBubble(_messages[index]);
                          },
                        ),
                      ),
                      
                      // Input Area
                      Container(
                        padding: const EdgeInsets.all(AppSizes.paddingM),
                        decoration: BoxDecoration(
                          color: const Color(AppColors.offWhite),
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(AppSizes.radiusL),
                            bottomRight: Radius.circular(AppSizes.radiusL),
                          ),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: _showAttachmentOptions,
                              icon: const Icon(
                                Icons.attach_file,
                                color: Color(AppColors.primaryRed),
                              ),
                            ),
                            Expanded(
                              child: TextField(
                                controller: _messageController,
                                decoration: InputDecoration(
                                  hintText: 'اكتب رسالتك هنا...',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                    borderSide: BorderSide.none,
                                  ),
                                  filled: true,
                                  fillColor: const Color(AppColors.primaryWhite),
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingM,
                                    vertical: AppSizes.paddingS,
                                  ),
                                ),
                                maxLines: null,
                                textInputAction: TextInputAction.send,
                                onSubmitted: (_) => _sendMessage(),
                              ),
                            ),
                            const SizedBox(width: AppSizes.paddingS),
                            FloatingActionButton(
                              onPressed: _sendMessage,
                              backgroundColor: const Color(AppColors.primaryRed),
                              foregroundColor: const Color(AppColors.primaryWhite),
                              mini: true,
                              child: const Icon(Icons.send),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionChip(Map<String, dynamic> action) {
    return InkWell(
      onTap: () => _handleQuickAction(action['action']),
      borderRadius: BorderRadius.circular(AppSizes.radiusL),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizes.paddingM,
          vertical: AppSizes.paddingS,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(action['color']).withValues(alpha: 0.1),
              Color(action['color']).withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          border: Border.all(
            color: Color(action['color']).withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              action['icon'],
              size: 18,
              color: Color(action['color']),
            ),
            const SizedBox(width: AppSizes.paddingS),
            Text(
              action['title'],
              style: TextStyle(
                color: Color(action['color']),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    final isUser = message['isUser'] as bool;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(AppColors.primaryRed),
                    const Color(AppColors.darkRed),
                  ],
                ),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: const Icon(
                Icons.smart_toy,
                color: Color(AppColors.primaryWhite),
                size: 20,
              ),
            ),
            const SizedBox(width: AppSizes.paddingS),
          ],
          
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isUser
                      ? [
                          const Color(AppColors.primaryRed),
                          const Color(AppColors.darkRed),
                        ]
                      : [
                          const Color(AppColors.lightGrey),
                          const Color(AppColors.offWhite),
                        ],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(AppSizes.radiusM),
                  topRight: const Radius.circular(AppSizes.radiusM),
                  bottomLeft: Radius.circular(isUser ? AppSizes.radiusM : AppSizes.radiusS),
                  bottomRight: Radius.circular(isUser ? AppSizes.radiusS : AppSizes.radiusM),
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                    blurRadius: 5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message['text'],
                    style: TextStyle(
                      color: isUser 
                          ? const Color(AppColors.primaryWhite)
                          : const Color(AppColors.primaryBlack),
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                  
                  if (message['type'] == 'suggestion') ...[
                    const SizedBox(height: AppSizes.paddingS),
                    Wrap(
                      spacing: AppSizes.paddingS,
                      children: (message['suggestions'] as List<String>).map((suggestion) {
                        return InkWell(
                          onTap: () => _handleSuggestion(suggestion),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.info).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              border: Border.all(
                                color: const Color(AppColors.info).withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              suggestion,
                              style: const TextStyle(
                                color: Color(AppColors.info),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                  
                  const SizedBox(height: AppSizes.paddingS),
                  
                  Text(
                    _formatTime(message['timestamp']),
                    style: TextStyle(
                      color: isUser 
                          ? const Color(AppColors.primaryWhite).withValues(alpha: 0.7)
                          : const Color(AppColors.grey),
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: AppSizes.paddingS),
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingS),
              decoration: BoxDecoration(
                color: const Color(AppColors.info),
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
              ),
              child: const Icon(
                Icons.person,
                color: Color(AppColors.primaryWhite),
                size: 20,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingS),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(AppColors.primaryRed),
                  const Color(AppColors.darkRed),
                ],
              ),
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
            ),
            child: const Icon(
              Icons.smart_toy,
              color: Color(AppColors.primaryWhite),
              size: 20,
            ),
          ),
          const SizedBox(width: AppSizes.paddingS),
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            decoration: BoxDecoration(
              color: const Color(AppColors.lightGrey),
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
            ),
            child: AnimatedBuilder(
              animation: _typingAnimation,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(3, (index) {
                    final delay = index * 0.3;
                    final animationValue = (_typingAnimation.value - delay).clamp(0.0, 1.0);
                    return Container(
                      margin: const EdgeInsets.only(right: 4),
                      child: Transform.translate(
                        offset: Offset(0, -10 * animationValue),
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(AppColors.primaryRed),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;
    
    setState(() {
      _messages.add({
        'text': text,
        'isUser': true,
        'timestamp': DateTime.now(),
        'type': 'text',
      });
      _isTyping = true;
    });
    
    _messageController.clear();
    _scrollToBottom();
    
    _typingController.repeat();
    
    // Simulate AI response
    Future.delayed(const Duration(seconds: 2), () {
      _typingController.stop();
      setState(() {
        _isTyping = false;
        _messages.add({
          'text': _generateAIResponse(text),
          'isUser': false,
          'timestamp': DateTime.now(),
          'type': 'text',
        });
      });
      _scrollToBottom();
    });
  }

  String _generateAIResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    if (message.contains('جدول') || message.contains('مواعيد')) {
      return 'يمكنك الاطلاع على جدول المؤتمر من خلال قسم "الجدول الشخصي". هناك جلسات متنوعة تبدأ من الساعة 9:00 صباحاً.';
    } else if (message.contains('متحدث') || message.contains('محاضر')) {
      return 'لدينا أكثر من 50 متحدث خبير من جميع أنحاء العالم. يمكنك تصفح قائمة المتحدثين في قسم "المتحدثون".';
    } else if (message.contains('مكان') || message.contains('خريطة')) {
      return 'المؤتمر يقام في مركز الرياض الدولي للمؤتمرات والمعارض. يمكنك الوصول للخريطة التفاعلية من القائمة الرئيسية.';
    } else if (message.contains('تسجيل') || message.contains('حضور')) {
      return 'يمكنك تسجيل حضورك في الجلسات من خلال مسح رمز QR أو من خلال التطبيق مباشرة.';
    } else {
      return 'شكراً لسؤالك! أنا هنا لمساعدتك في أي استفسار حول المؤتمر. يمكنك سؤالي عن الجدول، المتحدثين، المكان، أو أي معلومة أخرى.';
    }
  }

  void _handleQuickAction(String action) {
    String response = '';
    
    switch (action) {
      case 'schedule':
        response = 'إليك أهم الجلسات اليوم:\n\n'
            '• 09:00 - افتتاح المؤتمر\n'
            '• 10:30 - أحدث تقنيات زراعة الأسنان\n'
            '• 14:00 - التقويم الشفاف\n'
            '• 16:00 - جلسة الأسئلة والأجوبة';
        break;
      case 'speakers':
        response = 'من أبرز المتحدثين اليوم:\n\n'
            '• د. أحمد محمد علي - زراعة الأسنان\n'
            '• د. محمد الزهراني - التقويم\n'
            '• د. فاطمة أحمد - طب أسنان الأطفال';
        break;
      case 'map':
        response = 'المؤتمر يقام في:\n'
            'مركز الرياض الدولي للمؤتمرات والمعارض\n\n'
            'القاعات:\n'
            '• القاعة الرئيسية - الطابق الأول\n'
            '• قاعة A - الطابق الثاني\n'
            '• قاعة B - الطابق الثاني';
        break;
      case 'recommendations':
        response = 'بناءً على اهتماماتك، أنصحك بحضور:\n\n'
            '• جلسة زراعة الأسنان المتقدمة\n'
            '• ورشة التقويم الشفاف\n'
            '• معرض الأجهزة الطبية الحديثة';
        break;
    }
    
    setState(() {
      _messages.add({
        'text': response,
        'isUser': false,
        'timestamp': DateTime.now(),
        'type': 'text',
      });
    });
    
    _scrollToBottom();
  }

  void _handleSuggestion(String suggestion) {
    _messageController.text = suggestion;
    _sendMessage();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.mic),
              title: const Text('تسجيل صوتي'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showAISettings() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعدادات المساعد الذكي',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            SwitchListTile(
              title: const Text('الردود الصوتية'),
              subtitle: const Text('تفعيل الردود الصوتية من المساعد'),
              value: true,
              onChanged: (value) {},
            ),
            
            SwitchListTile(
              title: const Text('الإشعارات الذكية'),
              subtitle: const Text('تلقي اقتراحات ذكية حول المؤتمر'),
              value: false,
              onChanged: (value) {},
            ),
            
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('لغة المساعد'),
              subtitle: const Text('العربية'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }
}
