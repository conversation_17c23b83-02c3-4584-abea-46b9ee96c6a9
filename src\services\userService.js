const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { ValidationError, NotFoundError, ConflictError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { removeSensitiveFields } = require('../utils/helpers');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
} catch (error) {
  logger.error('Failed to initialize Prisma client:', error);
  // Use mock data if database is not available
  prisma = null;
}

class UserService {
  constructor() {
    this.saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
  }

  // Create new user
  async createUser(userData) {
    try {
      // Check if user already exists
      const existingUser = await this.findUserByEmail(userData.email);
      if (existingUser) {
        throw new ConflictError('User with this email already exists');
      }

      const existingPhone = await this.findUserByPhone(userData.phone);
      if (existingPhone) {
        throw new ConflictError('User with this phone number already exists');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(userData.password, this.saltRounds);

      const newUser = {
        email: userData.email,
        phone: userData.phone,
        passwordHash,
        arabicName: userData.arabicName,
        englishName: userData.englishName,
        qualification: userData.qualification,
        specialization: userData.specialization || null,
        university: userData.university || null,
        status: 'PENDING_VERIFICATION',
        emailVerified: false,
        phoneVerified: false
      };

      let user;
      if (prisma) {
        user = await prisma.user.create({
          data: newUser,
          include: {
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });

        // Assign default participant role
        const participantRole = await prisma.role.findUnique({
          where: { name: 'participant' }
        });

        if (participantRole) {
          await prisma.userRole.create({
            data: {
              userId: user.id,
              roleId: participantRole.id
            }
          });
        }
      } else {
        // Mock user creation
        user = {
          id: 'user_' + Date.now(),
          ...newUser,
          createdAt: new Date(),
          updatedAt: new Date(),
          userRoles: [{
            role: { name: 'participant' }
          }]
        };
      }

      logger.logUserAction(user.id, 'USER_CREATED', {
        email: user.email,
        qualification: user.qualification
      });

      return removeSensitiveFields(user);
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  // Find user by email
  async findUserByEmail(email) {
    try {
      if (prisma) {
        return await prisma.user.findUnique({
          where: { email },
          include: {
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });
      } else {
        // Mock user for testing
        if (email === '<EMAIL>') {
          return {
            id: 'admin_123',
            email: '<EMAIL>',
            passwordHash: await bcrypt.hash('Admin123!@#', this.saltRounds),
            arabicName: 'مدير النظام',
            englishName: 'System Administrator',
            status: 'ACTIVE',
            emailVerified: true,
            phoneVerified: true,
            userRoles: [{
              role: { name: 'super_admin' }
            }]
          };
        }
        return null;
      }
    } catch (error) {
      logger.error('Error finding user by email:', error);
      return null;
    }
  }

  // Find user by phone
  async findUserByPhone(phone) {
    try {
      if (prisma) {
        return await prisma.user.findUnique({
          where: { phone },
          include: {
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });
      } else {
        return null;
      }
    } catch (error) {
      logger.error('Error finding user by phone:', error);
      return null;
    }
  }

  // Find user by ID
  async findUserById(id) {
    try {
      if (prisma) {
        return await prisma.user.findUnique({
          where: { id },
          include: {
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });
      } else {
        // Mock user
        return {
          id,
          email: '<EMAIL>',
          arabicName: 'مستخدم تجريبي',
          englishName: 'Test User',
          status: 'ACTIVE',
          userRoles: [{
            role: { name: 'participant' }
          }]
        };
      }
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      return null;
    }
  }

  // Update user
  async updateUser(id, updateData) {
    try {
      const user = await this.findUserById(id);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      let updatedUser;
      if (prisma) {
        updatedUser = await prisma.user.update({
          where: { id },
          data: updateData,
          include: {
            userRoles: {
              include: {
                role: true
              }
            }
          }
        });
      } else {
        // Mock update
        updatedUser = { ...user, ...updateData, updatedAt: new Date() };
      }

      logger.logUserAction(id, 'USER_UPDATED', updateData);

      return removeSensitiveFields(updatedUser);
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  // Verify user email
  async verifyEmail(id) {
    return await this.updateUser(id, { 
      emailVerified: true,
      status: 'ACTIVE'
    });
  }

  // Verify user phone
  async verifyPhone(id) {
    return await this.updateUser(id, { phoneVerified: true });
  }

  // Get user roles
  async getUserRoles(id) {
    try {
      const user = await this.findUserById(id);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      return user.userRoles.map(ur => ur.role.name);
    } catch (error) {
      logger.error('Error getting user roles:', error);
      return ['participant']; // Default role
    }
  }

  // Check if user has role
  async hasRole(id, roleName) {
    const roles = await this.getUserRoles(id);
    return roles.includes(roleName);
  }

  // Get all users (admin function)
  async getAllUsers(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      if (prisma) {
        const where = {};
        
        if (filters.status) {
          where.status = filters.status;
        }
        
        if (filters.qualification) {
          where.qualification = filters.qualification;
        }

        if (filters.search) {
          where.OR = [
            { arabicName: { contains: filters.search } },
            { englishName: { contains: filters.search } },
            { email: { contains: filters.search } }
          ];
        }

        const [users, total] = await Promise.all([
          prisma.user.findMany({
            where,
            skip: offset,
            take: limit,
            include: {
              userRoles: {
                include: {
                  role: true
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          }),
          prisma.user.count({ where })
        ]);

        return {
          users: users.map(user => removeSensitiveFields(user)),
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        };
      } else {
        // Mock data
        const mockUsers = [
          {
            id: 'user1',
            email: '<EMAIL>',
            arabicName: 'د. سارة أحمد',
            englishName: 'Dr. Sarah Ahmed',
            qualification: 'DOCTOR',
            status: 'ACTIVE',
            createdAt: new Date()
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            arabicName: 'محمد علي',
            englishName: 'Mohammed Ali',
            qualification: 'STUDENT_YEAR_5',
            status: 'PENDING_VERIFICATION',
            createdAt: new Date()
          }
        ];

        return {
          users: mockUsers,
          total: mockUsers.length,
          page: 1,
          limit: 10,
          pages: 1
        };
      }
    } catch (error) {
      logger.error('Error getting all users:', error);
      throw error;
    }
  }

  // Update user status (admin function)
  async updateUserStatus(id, status, updatedBy) {
    try {
      const updatedUser = await this.updateUser(id, { status });

      logger.logAudit(updatedBy, 'UPDATE_USER_STATUS', 'user', id, {
        newStatus: status
      });

      return updatedUser;
    } catch (error) {
      logger.error('Error updating user status:', error);
      throw error;
    }
  }
}

module.exports = new UserService();
