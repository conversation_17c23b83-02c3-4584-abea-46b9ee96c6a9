import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  useMediaQuery,
  Collapse,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard,
  People,
  Event,
  Payment,
  Assessment,
  Settings,
  Notifications,
  MonitorHeart,
  ExitToApp,
  Person,
  ExpandLess,
  ExpandMore,
  AdminPanelSettings,
  Language,
  Home,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const drawerWidth = 280;

const AdminLayout = () => {
  const { user, logout } = useAuth();
  const { t, toggleLanguage } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [expandedMenus, setExpandedMenus] = useState({});

  // Navigation items
  const navigationItems = [
    {
      title: t('dashboard'),
      icon: <Dashboard />,
      path: '/admin',
      exact: true,
    },
    {
      title: t('users'),
      icon: <People />,
      path: '/admin/users',
      children: [
        { title: t('allUsers', 'جميع المستخدمين'), path: '/admin/users' },
        { title: t('pendingApproval', 'في انتظار الموافقة'), path: '/admin/users?status=pending' },
        { title: t('activeUsers', 'المستخدمون النشطون'), path: '/admin/users?status=active' },
      ],
    },
    {
      title: t('subscriptions'),
      icon: <Payment />,
      path: '/admin/subscriptions',
      children: [
        { title: t('allSubscriptions', 'جميع الاشتراكات'), path: '/admin/subscriptions' },
        { title: t('pendingReview', 'قيد المراجعة'), path: '/admin/subscriptions?status=pending' },
        { title: t('pendingPayment', 'في انتظار الدفع'), path: '/admin/subscriptions?status=approved' },
      ],
    },
    {
      title: t('content'),
      icon: <Event />,
      path: '/admin/content',
      children: [
        { title: t('sessions'), path: '/admin/content/sessions' },
        { title: t('speakers'), path: '/admin/content/speakers' },
        { title: t('announcements', 'الإعلانات'), path: '/admin/content/announcements' },
      ],
    },
    {
      title: t('reports'),
      icon: <Assessment />,
      path: '/admin/reports',
      children: [
        { title: t('overview', 'نظرة عامة'), path: '/admin/reports' },
        { title: t('financial', 'التقارير المالية'), path: '/admin/reports/financial' },
        { title: t('attendance', 'تقارير الحضور'), path: '/admin/reports/attendance' },
      ],
    },
    {
      title: t('monitoring'),
      icon: <MonitorHeart />,
      path: '/admin/monitoring',
    },
    {
      title: t('settings'),
      icon: <Settings />,
      path: '/admin/settings',
      children: [
        { title: t('general', 'عام'), path: '/admin/settings' },
        { title: t('pricing', 'التسعير'), path: '/admin/settings/pricing' },
        { title: t('email', 'البريد الإلكتروني'), path: '/admin/settings/email' },
      ],
    },
  ];

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    await logout();
    handleMenuClose();
    navigate('/');
  };

  const handleMenuExpand = (title) => {
    setExpandedMenus(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const isActivePath = (path, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  const drawer = (
    <Box>
      {/* Logo/Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" alignItems="center" gap={2}>
          <AdminPanelSettings color="primary" sx={{ fontSize: 32 }} />
          <Box>
            <Typography variant="h6" noWrap>
              {t('adminPanel', 'لوحة الإدارة')}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              IDEC 2025
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Navigation */}
      <List sx={{ px: 1, py: 2 }}>
        {navigationItems.map((item) => (
          <React.Fragment key={item.title}>
            <ListItem disablePadding>
              <ListItemButton
                selected={isActivePath(item.path, item.exact)}
                onClick={() => {
                  if (item.children) {
                    handleMenuExpand(item.title);
                  } else {
                    navigate(item.path);
                    if (isMobile) setMobileOpen(false);
                  }
                }}
                sx={{
                  borderRadius: 1,
                  mx: 1,
                  mb: 0.5,
                  '&.Mui-selected': {
                    bgcolor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.title} />
                {item.children && (
                  expandedMenus[item.title] ? <ExpandLess /> : <ExpandMore />
                )}
              </ListItemButton>
            </ListItem>

            {/* Submenu */}
            {item.children && (
              <Collapse in={expandedMenus[item.title]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.children.map((child) => (
                    <ListItem key={child.path} disablePadding>
                      <ListItemButton
                        selected={isActivePath(child.path)}
                        onClick={() => {
                          navigate(child.path);
                          if (isMobile) setMobileOpen(false);
                        }}
                        sx={{
                          pl: 4,
                          borderRadius: 1,
                          mx: 1,
                          mb: 0.5,
                          '&.Mui-selected': {
                            bgcolor: 'primary.light',
                            color: 'primary.contrastText',
                          },
                        }}
                      >
                        <ListItemText 
                          primary={child.title}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>

      <Divider sx={{ mx: 2 }} />

      {/* Quick Actions */}
      <List sx={{ px: 1, py: 1 }}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              navigate('/');
              if (isMobile) setMobileOpen(false);
            }}
            sx={{ borderRadius: 1, mx: 1 }}
          >
            <ListItemIcon>
              <Home />
            </ListItemIcon>
            <ListItemText primary={t('backToSite', 'العودة للموقع')} />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {navigationItems.find(item => 
              isActivePath(item.path, item.exact)
            )?.title || t('adminPanel', 'لوحة الإدارة')}
          </Typography>

          {/* Language Toggle */}
          <IconButton color="inherit" onClick={toggleLanguage} sx={{ mr: 1 }}>
            <Language />
          </IconButton>

          {/* Notifications */}
          <IconButton color="inherit" sx={{ mr: 1 }}>
            <Badge badgeContent={4} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* User Menu */}
          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
          >
            <Avatar sx={{ width: 32, height: 32 }}>
              {user?.arabicName?.charAt(0) || user?.englishName?.charAt(0) || 'A'}
            </Avatar>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => { navigate('/dashboard/profile'); handleMenuClose(); }}>
              <Person sx={{ mr: 1 }} />
              {t('profile')}
            </MenuItem>
            <MenuItem onClick={() => { navigate('/dashboard'); handleMenuClose(); }}>
              <Dashboard sx={{ mr: 1 }} />
              {t('userDashboard', 'لوحة المستخدم')}
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ExitToApp sx={{ mr: 1 }} />
              {t('logout')}
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Drawer */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mt: 8, // Account for AppBar height
        }}
      >
        <Outlet />
      </Box>
    </Box>
  );
};

export default AdminLayout;
