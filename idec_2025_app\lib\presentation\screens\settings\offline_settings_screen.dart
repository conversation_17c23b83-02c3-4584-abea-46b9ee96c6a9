import 'package:flutter/material.dart';
import 'package:provider/provider.dart' as provider;
import '../../../core/constants/app_constants.dart';
import '../../../core/providers/offline_provider.dart';

class OfflineSettingsScreen extends StatefulWidget {
  const OfflineSettingsScreen({super.key});

  @override
  State<OfflineSettingsScreen> createState() => _OfflineSettingsScreenState();
}

class _OfflineSettingsScreenState extends State<OfflineSettingsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final offlineProvider = provider.Provider.of<OfflineProvider>(
        context, 
        listen: false
      );
      offlineProvider.initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الوضع غير المتصل',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showOfflineInfo,
            icon: const Icon(Icons.info_outline),
            tooltip: 'معلومات الوضع غير المتصل',
          ),
        ],
      ),
      body: provider.Consumer<OfflineProvider>(
        builder: (context, offlineProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(AppSizes.paddingL),
            children: [
              // Status Card
              _buildStatusCard(offlineProvider),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Offline Mode Toggle
              _buildOfflineModeCard(offlineProvider),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Sync Data Card
              _buildSyncDataCard(offlineProvider),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Cache Info Card
              _buildCacheInfoCard(offlineProvider),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // Actions Card
              _buildActionsCard(offlineProvider),
              
              const SizedBox(height: AppSizes.paddingXL),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(OfflineProvider provider) {
    final isOnline = !provider.isOfflineMode;
    final statusColor = isOnline ? Colors.green : Colors.orange;
    final statusIcon = isOnline ? Icons.wifi : Icons.wifi_off;
    final statusText = isOnline ? 'متصل' : 'غير متصل';

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: 32,
            ),
          ),
          const SizedBox(width: AppSizes.paddingL),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة الاتصال',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: AppSizes.paddingXS),
                Text(
                  statusText,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
                const SizedBox(height: AppSizes.paddingXS),
                Text(
                  provider.syncStatusMessage,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineModeCard(OfflineProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.offline_bolt,
                color: Color(AppColors.primaryRed),
                size: 24,
              ),
              const SizedBox(width: AppSizes.paddingM),
              const Expanded(
                child: Text(
                  'تفعيل الوضع غير المتصل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Switch(
                value: provider.isOfflineMode,
                onChanged: (value) {
                  if (value) {
                    provider.enableOfflineMode();
                  } else {
                    provider.disableOfflineMode();
                  }
                },
                activeColor: const Color(AppColors.primaryRed),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingM),
          Text(
            'عند تفعيل هذا الوضع، ستتمكن من استخدام التطبيق بدون اتصال بالإنترنت باستخدام البيانات المحفوظة محلياً.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncDataCard(OfflineProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.sync,
                color: Color(AppColors.primaryRed),
                size: 24,
              ),
              const SizedBox(width: AppSizes.paddingM),
              const Expanded(
                child: Text(
                  'مزامنة البيانات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingM),
          Text(
            'قم بمزامنة أحدث البيانات من الخادم لاستخدامها في الوضع غير المتصل.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: AppSizes.paddingL),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: provider.isSyncing ? null : () async {
                final success = await provider.syncDataForOffline();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success ? 'تم تحديث البيانات بنجاح' : 'فشل في تحديث البيانات',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              },
              icon: provider.isSyncing
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.sync),
              label: Text(
                provider.isSyncing ? 'جاري المزامنة...' : 'مزامنة البيانات',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(AppColors.primaryRed),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingL),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusL),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCacheInfoCard(OfflineProvider provider) {
    final cacheInfo = provider.cacheInfo;
    
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.storage,
                color: Color(AppColors.primaryRed),
                size: 24,
              ),
              SizedBox(width: AppSizes.paddingM),
              Text(
                'معلومات البيانات المحفوظة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingL),
          _buildCacheInfoRow('الجلسات', cacheInfo['sessions'] ?? 0, Icons.event),
          _buildCacheInfoRow('المتحدثين', cacheInfo['speakers'] ?? 0, Icons.person),
          _buildCacheInfoRow('الأخبار', cacheInfo['news'] ?? 0, Icons.newspaper),
          _buildCacheInfoRow('العارضين', cacheInfo['exhibitors'] ?? 0, Icons.store),
          _buildCacheInfoRow('المكتبة', cacheInfo['library'] ?? 0, Icons.library_books),
          const Divider(height: AppSizes.paddingL),
          Row(
            children: [
              const Icon(Icons.access_time, size: 16, color: Colors.grey),
              const SizedBox(width: AppSizes.paddingS),
              Text(
                'آخر تحديث: ${_formatLastSync(provider.lastSyncTime)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingS),
          Row(
            children: [
              const Icon(Icons.data_usage, size: 16, color: Colors.grey),
              const SizedBox(width: AppSizes.paddingS),
              Text(
                'حجم البيانات: ${provider.cacheSizeMB.toStringAsFixed(1)} ميجابايت',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCacheInfoRow(String title, int count, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingS),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: AppSizes.paddingM),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSizes.paddingM,
              vertical: AppSizes.paddingXS,
            ),
            decoration: BoxDecoration(
              color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSizes.radiusS),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.primaryRed),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard(OfflineProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.settings,
                color: Color(AppColors.primaryRed),
                size: 24,
              ),
              SizedBox(width: AppSizes.paddingM),
              Text(
                'إجراءات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingL),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showClearCacheDialog(provider),
              icon: const Icon(Icons.delete_outline),
              label: const Text('مسح البيانات المحفوظة'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingL),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusL),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatLastSync(DateTime? lastSync) {
    if (lastSync == null) return 'لم يتم التحديث بعد';
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void _showOfflineInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الوضع غير المتصل'),
        content: const Text(
          'يتيح لك الوضع غير المتصل استخدام التطبيق بدون اتصال بالإنترنت. '
          'يتم حفظ البيانات محلياً على جهازك ويمكنك الوصول إليها في أي وقت.\n\n'
          'تأكد من مزامنة البيانات بانتظام للحصول على أحدث المعلومات.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(OfflineProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح البيانات المحفوظة'),
        content: const Text(
          'هل أنت متأكد من أنك تريد مسح جميع البيانات المحفوظة محلياً؟ '
          'ستحتاج إلى مزامنة البيانات مرة أخرى لاستخدام الوضع غير المتصل.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await provider.clearOfflineCache();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم مسح البيانات المحفوظة'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text(
              'مسح',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
