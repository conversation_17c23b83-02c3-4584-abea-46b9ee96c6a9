import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  <PERSON>per,
  Step,
  StepLabel,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  CheckCircle,
  Schedule,
  Payment,
  Warning,
  Upload,
  Description,
  Info,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { subscriptionAPI, uploadAPI } from '../../services/api';

const SubscriptionPage = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [documentType, setDocumentType] = useState('');

  // Fetch subscription pricing
  const { data: pricingData, isLoading: pricingLoading } = useQuery({
    queryKey: ['subscriptionPricing', user?.qualification],
    queryFn: () => subscriptionAPI.getPricing(user?.qualification),
    enabled: !!user?.qualification,
  });

  // Fetch user subscription
  const { data: subscriptionData, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['userSubscription'],
    queryFn: subscriptionAPI.getMySubscription,
  });

  // Create subscription mutation
  const createSubscriptionMutation = useMutation({
    mutationFn: subscriptionAPI.createSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries(['userSubscription']);
    },
  });

  // Upload document mutation
  const uploadDocumentMutation = useMutation({
    mutationFn: ({ file, type }) => uploadAPI.uploadDocument(file, type),
    onSuccess: () => {
      setUploadDialogOpen(false);
      setSelectedFile(null);
      setDocumentType('');
    },
  });

  const pricing = pricingData?.data;
  const subscription = subscriptionData?.data?.subscription;
  const hasSubscription = subscriptionData?.data?.hasSubscription;

  // Get subscription steps
  const getSubscriptionSteps = () => {
    const steps = [
      t('submitApplication', 'تقديم الطلب'),
      t('documentReview', 'مراجعة الوثائق'),
      t('payment', 'الدفع'),
      t('confirmation', 'التأكيد'),
    ];
    return steps;
  };

  // Get current step based on subscription status
  const getCurrentStep = () => {
    if (!subscription) return 0;
    
    switch (subscription.status) {
      case 'PENDING_REVIEW':
        return 1;
      case 'APPROVED_PENDING_PAYMENT':
        return 2;
      case 'ACTIVE':
        return 3;
      case 'REJECTED':
        return 0;
      default:
        return 0;
    }
  };

  // Get status info
  const getStatusInfo = (status) => {
    switch (status) {
      case 'PENDING_REVIEW':
        return { 
          color: 'warning', 
          icon: <Schedule />, 
          text: t('PENDING_REVIEW'),
          description: t('subscriptionUnderReview', 'طلبك قيد المراجعة من قبل الإدارة')
        };
      case 'APPROVED_PENDING_PAYMENT':
        return { 
          color: 'info', 
          icon: <Payment />, 
          text: t('APPROVED_PENDING_PAYMENT'),
          description: t('subscriptionApprovedPayment', 'تم قبول طلبك، يرجى إكمال عملية الدفع')
        };
      case 'ACTIVE':
        return { 
          color: 'success', 
          icon: <CheckCircle />, 
          text: t('ACTIVE'),
          description: t('subscriptionActive', 'اشتراكك نشط ويمكنك الوصول لجميع الجلسات')
        };
      case 'REJECTED':
        return { 
          color: 'error', 
          icon: <Warning />, 
          text: t('REJECTED'),
          description: t('subscriptionRejected', 'تم رفض طلبك، يرجى مراجعة الملاحظات أدناه')
        };
      default:
        return { 
          color: 'default', 
          icon: <Info />, 
          text: status,
          description: ''
        };
    }
  };

  // Handle subscription creation
  const handleCreateSubscription = async () => {
    try {
      await createSubscriptionMutation.mutateAsync({
        qualification: user.qualification,
      });
    } catch (error) {
      console.error('Error creating subscription:', error);
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile || !documentType) return;

    try {
      await uploadDocumentMutation.mutateAsync({
        file: selectedFile,
        type: documentType,
      });
    } catch (error) {
      console.error('Error uploading document:', error);
    }
  };

  if (pricingLoading || subscriptionLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('subscriptionManagement', 'إدارة الاشتراك')}
      </Typography>

      {/* Current Subscription Status */}
      {hasSubscription && subscription && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  {getStatusInfo(subscription.status).icon}
                  <Typography variant="h6">
                    {t('subscriptionStatus')}: {getStatusInfo(subscription.status).text}
                  </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary" paragraph>
                  {getStatusInfo(subscription.status).description}
                </Typography>
                
                {subscription.reviewNotes && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      <strong>{t('adminNotes', 'ملاحظات الإدارة')}:</strong> {subscription.reviewNotes}
                    </Typography>
                  </Alert>
                )}
              </Grid>
              
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {subscription.amount} {subscription.currency}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('subscriptionAmount', 'مبلغ الاشتراك')}
                  </Typography>
                  <Chip
                    label={subscription.pricingPeriod}
                    size="small"
                    color="primary"
                    sx={{ mt: 1 }}
                  />
                </Paper>
              </Grid>
            </Grid>

            {/* Progress Stepper */}
            <Box sx={{ mt: 3 }}>
              <Stepper activeStep={getCurrentStep()} alternativeLabel>
                {getSubscriptionSteps().map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Box>

            {/* Action Buttons */}
            <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'center' }}>
              {subscription.status === 'APPROVED_PENDING_PAYMENT' && (
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Payment />}
                  onClick={() => window.open('/dashboard/payment', '_blank')}
                >
                  {t('proceedToPayment', 'المتابعة للدفع')}
                </Button>
              )}
              
              {subscription.status === 'PENDING_REVIEW' && (
                <Button
                  variant="outlined"
                  startIcon={<Upload />}
                  onClick={() => setUploadDialogOpen(true)}
                >
                  {t('uploadDocuments', 'رفع الوثائق')}
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Pricing Information */}
      {pricing && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('pricingInformation', 'معلومات التسعير')}
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
                  <Typography variant="h3" gutterBottom>
                    {pricing.currentAmount} {pricing.currency}
                  </Typography>
                  <Typography variant="h6" gutterBottom>
                    {t('currentPrice', 'السعر الحالي')}
                  </Typography>
                  <Chip
                    label={t(pricing.currentPeriod)}
                    sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                  />
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('pricingPeriods', 'فترات التسعير')}:
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary={`${t('earlyBird', 'الطائر المبكر')}: ${pricing.pricing.early} ${pricing.currency}`}
                      secondary={`حتى ${pricing.deadlines.early}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary={`${t('regular', 'عادي')}: ${pricing.pricing.regular} ${pricing.currency}`}
                      secondary={`حتى ${pricing.deadlines.regular}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary={`${t('late', 'متأخر')}: ${pricing.pricing.late} ${pricing.currency}`}
                      secondary={`حتى ${pricing.deadlines.late}`}
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Create Subscription */}
      {!hasSubscription && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('createSubscription', 'إنشاء اشتراك جديد')}
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                {t('subscriptionInfo', 'بعد تقديم طلب الاشتراك، ستتم مراجعته من قبل الإدارة خلال 24-48 ساعة.')}
              </Typography>
            </Alert>

            <Box textAlign="center">
              <Button
                variant="contained"
                size="large"
                onClick={handleCreateSubscription}
                disabled={createSubscriptionMutation.isPending}
                sx={{ px: 4, py: 1.5 }}
              >
                {createSubscriptionMutation.isPending ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  t('submitSubscription')
                )}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Upload Document Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t('uploadDocument', 'رفع وثيقة')}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              select
              label={t('documentType', 'نوع الوثيقة')}
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
              SelectProps={{ native: true }}
              sx={{ mb: 2 }}
            >
              <option value="">{t('selectDocumentType', 'اختر نوع الوثيقة')}</option>
              <option value="ID">{t('nationalId', 'الهوية الوطنية')}</option>
              <option value="DEGREE">{t('degree', 'الشهادة الجامعية')}</option>
              <option value="LICENSE">{t('license', 'رخصة الممارسة')}</option>
              <option value="OTHER">{t('other', 'أخرى')}</option>
            </TextField>

            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={(e) => setSelectedFile(e.target.files[0])}
              style={{ width: '100%', padding: '10px', border: '1px solid #ccc', borderRadius: '4px' }}
            />
            
            {selectedFile && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {t('selectedFile', 'الملف المحدد')}: {selectedFile.name}
              </Typography>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>
            {t('cancel')}
          </Button>
          <Button
            onClick={handleFileUpload}
            variant="contained"
            disabled={!selectedFile || !documentType || uploadDocumentMutation.isPending}
          >
            {uploadDocumentMutation.isPending ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              t('upload', 'رفع')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SubscriptionPage;
