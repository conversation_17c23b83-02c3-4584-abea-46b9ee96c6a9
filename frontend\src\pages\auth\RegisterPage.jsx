import React, { useState, useEffect } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
  Divider,
  MenuItem,
  Grid,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import {
  Email,
  Lock,
  Visibility,
  VisibilityOff,
  Person,
  Phone,
  School,
  PersonAdd,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const RegisterPage = () => {
  const { register: registerUser, isAuthenticated, isLoading, error, clearError } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [registerError, setRegisterError] = useState('');
  const [activeStep, setActiveStep] = useState(0);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm();

  const watchPassword = watch('password');

  // Qualification options
  const qualifications = [
    { value: 'DOCTOR', label: t('DOCTOR') },
    { value: 'STUDENT_YEAR_6', label: t('STUDENT_YEAR_6') },
    { value: 'STUDENT_YEAR_5', label: t('STUDENT_YEAR_5') },
    { value: 'STUDENT_YEAR_4', label: t('STUDENT_YEAR_4') },
    { value: 'STUDENT_YEAR_3', label: t('STUDENT_YEAR_3') },
    { value: 'STUDENT_YEAR_2', label: t('STUDENT_YEAR_2') },
    { value: 'STUDENT_YEAR_1', label: t('STUDENT_YEAR_1') },
  ];

  const steps = [
    t('personalInfo', 'المعلومات الشخصية'),
    t('accountInfo', 'معلومات الحساب'),
    t('academicInfo', 'المعلومات الأكاديمية'),
  ];

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setRegisterError('');
  }, [clearError]);

  const onSubmit = async (data) => {
    setRegisterError('');
    clearError();

    // Remove confirmPassword from data
    const { confirmPassword, ...userData } = data;

    const result = await registerUser(userData);
    
    if (result.success) {
      // Show success message and redirect to verification or dashboard
      navigate('/dashboard', { replace: true });
    } else {
      setRegisterError(result.error);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="80vh"
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      minHeight="80vh"
      py={4}
    >
      <Card sx={{ maxWidth: 600, width: '100%', mx: 2 }}>
        <CardContent sx={{ p: 4 }}>
          {/* Header */}
          <Box textAlign="center" mb={3}>
            <PersonAdd sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom>
              {t('register')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('createNewAccount', 'إنشاء حساب جديد للمؤتمر')}
            </Typography>
          </Box>

          {/* Progress Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Error Alert */}
          {(error || registerError) && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error || registerError}
            </Alert>
          )}

          {/* Registration Form */}
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2}>
              {/* Arabic Name */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('arabicName')}
                  {...register('arabicName', {
                    required: t('arabicNameRequired', 'الاسم العربي مطلوب'),
                    pattern: {
                      value: /^[\u0600-\u06FF\s]+$/,
                      message: t('arabicNameInvalid', 'يجب أن يحتوي على أحرف عربية فقط'),
                    },
                  })}
                  error={!!errors.arabicName}
                  helperText={errors.arabicName?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              {/* English Name */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('englishName')}
                  {...register('englishName', {
                    required: t('englishNameRequired', 'الاسم الإنجليزي مطلوب'),
                    pattern: {
                      value: /^[a-zA-Z\s]+$/,
                      message: t('englishNameInvalid', 'يجب أن يحتوي على أحرف إنجليزية فقط'),
                    },
                  })}
                  error={!!errors.englishName}
                  helperText={errors.englishName?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Person />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              {/* Email */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('email')}
                  type="email"
                  {...register('email', {
                    required: t('emailRequired', 'البريد الإلكتروني مطلوب'),
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: t('emailInvalid', 'البريد الإلكتروني غير صحيح'),
                    },
                  })}
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              {/* Phone */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('phone')}
                  {...register('phone', {
                    required: t('phoneRequired', 'رقم الهاتف مطلوب'),
                    pattern: {
                      value: /^\+966[0-9]{9}$/,
                      message: t('phoneInvalid', 'رقم الهاتف يجب أن يكون بالصيغة: +966xxxxxxxxx'),
                    },
                  })}
                  error={!!errors.phone}
                  helperText={errors.phone?.message || t('phoneFormat', 'مثال: +966501234567')}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Phone />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              {/* Qualification */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  select
                  label={t('qualification')}
                  {...register('qualification', {
                    required: t('qualificationRequired', 'المؤهل العلمي مطلوب'),
                  })}
                  error={!!errors.qualification}
                  helperText={errors.qualification?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <School />
                      </InputAdornment>
                    ),
                  }}
                >
                  {qualifications.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {/* Specialization */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('specialization')}
                  {...register('specialization')}
                  error={!!errors.specialization}
                  helperText={errors.specialization?.message}
                />
              </Grid>

              {/* University */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('university')}
                  {...register('university')}
                  error={!!errors.university}
                  helperText={errors.university?.message}
                />
              </Grid>

              {/* Password */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('password')}
                  type={showPassword ? 'text' : 'password'}
                  {...register('password', {
                    required: t('passwordRequired', 'كلمة المرور مطلوبة'),
                    minLength: {
                      value: 8,
                      message: t('passwordMinLength', 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'),
                    },
                    pattern: {
                      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                      message: t('passwordPattern', 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة ورقم ورمز خاص'),
                    },
                  })}
                  error={!!errors.password}
                  helperText={errors.password?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleTogglePasswordVisibility}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>

              {/* Confirm Password */}
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  {...register('confirmPassword', {
                    required: t('confirmPasswordRequired', 'تأكيد كلمة المرور مطلوب'),
                    validate: (value) =>
                      value === watchPassword || t('passwordsDoNotMatch', 'كلمات المرور غير متطابقة'),
                  })}
                  error={!!errors.confirmPassword}
                  helperText={errors.confirmPassword?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle confirm password visibility"
                          onClick={handleToggleConfirmPasswordVisibility}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
            </Grid>

            {/* Submit Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isSubmitting}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {isSubmitting ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                t('register')
              )}
            </Button>

            {/* Divider */}
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                {t('or', 'أو')}
              </Typography>
            </Divider>

            {/* Login Link */}
            <Box textAlign="center">
              <Typography variant="body2" color="text.secondary">
                {t('alreadyHaveAccount', 'لديك حساب بالفعل؟')}{' '}
                <Link
                  component={RouterLink}
                  to="/login"
                  variant="body2"
                  color="primary"
                  fontWeight="bold"
                >
                  {t('login')}
                </Link>
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default RegisterPage;
