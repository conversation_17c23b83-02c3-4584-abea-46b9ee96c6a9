import { Router } from 'express';
import { body } from 'express-validator';
import { AuthController } from '@/controllers/auth.controller';
import { authenticate } from '@/middleware/auth.middleware';
import { validateRequest } from '@/middleware/validation.middleware';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';

const router = Router();
const authController = new AuthController();

// Register validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('arabicName')
    .notEmpty()
    .isLength({ min: 2, max: 100 })
    .withMessage('Arabic name is required and must be between 2-100 characters'),
  
  body('englishName')
    .notEmpty()
    .isLength({ min: 2, max: 100 })
    .withMessage('English name is required and must be between 2-100 characters'),
  
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  
  body('qualification')
    .isIn(['STUDENT', 'DENTIST', 'SPECIALIST', 'CONSULTANT', 'PROFESSOR', 'RESEARCHER', 'OTHER'])
    .withMessage('Valid qualification is required'),
  
  body('specialization')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Specialization must be less than 100 characters'),
  
  body('university')
    .optional()
    .isLength({ max: 100 })
    .withMessage('University must be less than 100 characters'),
  
  body('graduationYear')
    .optional()
    .isInt({ min: 1950, max: new Date().getFullYear() })
    .withMessage('Valid graduation year is required'),
  
  body('licenseNumber')
    .optional()
    .isLength({ max: 50 })
    .withMessage('License number must be less than 50 characters'),
  
  body('nationality')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Nationality must be less than 50 characters'),
  
  body('gender')
    .optional()
    .isIn(['MALE', 'FEMALE'])
    .withMessage('Valid gender is required'),
  
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Valid birth date is required'),
];

// Login validation rules
const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
];

// Forgot password validation rules
const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
];

// Reset password validation rules
const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('Reset token is required'),
  
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
];

// Change password validation rules
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
];

// Verify email validation rules
const verifyEmailValidation = [
  body('token')
    .notEmpty()
    .withMessage('Verification token is required'),
];

// Refresh token validation rules
const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
];

// Routes
router.post('/register', registerValidation, validateRequest, asyncHandler(authController.register));
router.post('/login', loginValidation, validateRequest, asyncHandler(authController.login));
router.post('/logout', authenticate, asyncHandler(authController.logout));
router.post('/refresh-token', refreshTokenValidation, validateRequest, asyncHandler(authController.refreshToken));

// Password management
router.post('/forgot-password', forgotPasswordValidation, validateRequest, asyncHandler(authController.forgotPassword));
router.post('/reset-password', resetPasswordValidation, validateRequest, asyncHandler(authController.resetPassword));
router.post('/change-password', authenticate, changePasswordValidation, validateRequest, asyncHandler(authController.changePassword));

// Email verification
router.post('/verify-email', verifyEmailValidation, validateRequest, asyncHandler(authController.verifyEmail));
router.post('/resend-verification', authenticate, asyncHandler(authController.resendVerification));

// Phone verification
router.post('/send-phone-verification', authenticate, asyncHandler(authController.sendPhoneVerification));
router.post('/verify-phone', authenticate, 
  [
    body('code')
      .isLength({ min: 4, max: 6 })
      .isNumeric()
      .withMessage('Valid verification code is required'),
  ],
  validateRequest,
  asyncHandler(authController.verifyPhone)
);

// Profile verification
router.get('/me', authenticate, asyncHandler(authController.getProfile));
router.get('/check-token', authenticate, asyncHandler(authController.checkToken));

// Social login (placeholder for future implementation)
router.post('/google', asyncHandler(authController.googleLogin));
router.post('/facebook', asyncHandler(authController.facebookLogin));
router.post('/apple', asyncHandler(authController.appleLogin));

export default router;
