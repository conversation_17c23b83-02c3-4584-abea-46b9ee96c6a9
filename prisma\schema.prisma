// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id                String   @id @default(cuid())
  email             String   @unique
  phone             String   @unique
  passwordHash      String   @map("password_hash")
  arabicName        String   @map("arabic_name")
  englishName       String   @map("english_name")
  birthDate         DateTime? @map("birth_date")
  qualification     String
  specialization    String?
  university        String?
  profileImage      String?  @map("profile_image")
  status            UserStatus @default(ACTIVE)
  emailVerified     Boolean  @default(false) @map("email_verified")
  phoneVerified     Boolean  @default(false) @map("phone_verified")
  lastLogin         DateTime? @map("last_login")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles         UserRole[]
  subscriptions     Subscription[]
  payments          Payment[]
  sessionAttendance SessionAttendance[]
  courseEnrollments CourseEnrollment[]
  notifications     Notification[]
  documents         Document[]
  qrCodes           QRCode[]
  ratings           Rating[]
  notes             Note[]
  auditLogs         AuditLog[]
  userPoints        UserPoint[]
  userBadges        UserBadge[]
  achievements      Achievement[]
  sentConnections   Connection[] @relation("UserConnections")
  receivedConnections Connection[] @relation("ConnectionRequests")
  sentMessages      Message[] @relation("SentMessages")
  receivedMessages  Message[] @relation("ReceivedMessages")
  boothVisits       BoothVisit[]
  libraryRatings    LibraryRating[]
  libraryBookmarks  LibraryBookmark[]
  libraryNotes      LibraryNote[]
  libraryViews      LibraryView[]

  @@map("users")
}

// User status enum
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

// Role model
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions Json
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles   UserRole[]

  @@map("roles")
}

// User-Role junction table
model UserRole {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  roleId    String   @map("role_id")
  assignedBy String? @map("assigned_by")
  assignedAt DateTime @default(now()) @map("assigned_at")

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role      Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// Subscription model
model Subscription {
  id              String            @id @default(cuid())
  userId          String            @map("user_id")
  status          SubscriptionStatus @default(PENDING_REVIEW)
  approvedBy      String?           @map("approved_by")
  approvedAt      DateTime?         @map("approved_at")
  rejectedBy      String?           @map("rejected_by")
  rejectedAt      DateTime?         @map("rejected_at")
  rejectionReason String?           @map("rejection_reason")
  paymentDeadline DateTime?         @map("payment_deadline")
  notes           String?
  createdAt       DateTime          @default(now()) @map("created_at")
  updatedAt       DateTime          @updatedAt @map("updated_at")

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]

  @@map("subscriptions")
}

// Subscription status enum
enum SubscriptionStatus {
  PENDING_REVIEW
  DOCUMENT_VERIFICATION
  APPROVED_PENDING_PAYMENT
  ACTIVE
  SUSPENDED
  REJECTED
  WAITING_LIST
  REACTIVATION_REQUESTED
}

// Payment model
model Payment {
  id              String        @id @default(cuid())
  userId          String        @map("user_id")
  subscriptionId  String?       @map("subscription_id")
  courseId        String?       @map("course_id")
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("SAR")
  paymentMethod   PaymentMethod
  gatewayReference String?      @map("gateway_reference")
  purpose         PaymentPurpose
  status          PaymentStatus @default(PENDING)
  paidAt          DateTime?     @map("paid_at")
  receiptUrl      String?       @map("receipt_url")
  notes           String?
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")

  // Relations
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  course          Course?       @relation(fields: [courseId], references: [id])

  @@map("payments")
}

// Payment enums
enum PaymentMethod {
  PAYPAL
  STRIPE
  BANK_TRANSFER
  CASH
  MANUAL
}

enum PaymentPurpose {
  CONFERENCE_SUBSCRIPTION
  COURSE_ENROLLMENT
  REFUND
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

// Speaker model
model Speaker {
  id          String   @id @default(cuid())
  nameAr      String   @map("name_ar")
  nameEn      String   @map("name_en")
  bio         String?
  image       String?
  linkedin    String?
  researchGate String? @map("research_gate")
  orcid       String?
  email       String?
  phone       String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  sessions    Session[]
  courses     Course[]

  @@map("speakers")
}

// Session model
model Session {
  id          String      @id @default(cuid())
  titleAr     String      @map("title_ar")
  titleEn     String      @map("title_en")
  description String?
  startTime   DateTime    @map("start_time")
  endTime     DateTime    @map("end_time")
  roomId      String?     @map("room_id")
  speakerId   String      @map("speaker_id")
  sessionType SessionType @map("session_type")
  cmeHours    Decimal?    @map("cme_hours") @db.Decimal(3, 1)
  maxAttendees Int?       @map("max_attendees")
  qrCode      String?     @map("qr_code")
  status      SessionStatus @default(SCHEDULED)
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  speaker     Speaker     @relation(fields: [speakerId], references: [id])
  room        Room?       @relation(fields: [roomId], references: [id])
  attendance  SessionAttendance[]
  ratings     Rating[]
  notes       Note[]

  @@map("sessions")
}

// Session enums
enum SessionType {
  LECTURE
  WORKSHOP
  PRESENTATION
  POSTER_SESSION
  PANEL_DISCUSSION
}

enum SessionStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// Room model
model Room {
  id          String   @id @default(cuid())
  name        String
  capacity    Int
  location    String?
  equipment   Json?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  sessions    Session[]

  @@map("rooms")
}

// Session Attendance model
model SessionAttendance {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  sessionId     String    @map("session_id")
  checkInTime   DateTime  @map("check_in_time")
  checkOutTime  DateTime? @map("check_out_time")
  qrCode        String    @map("qr_code")
  attendanceDuration Int? @map("attendance_duration") // in minutes
  createdAt     DateTime  @default(now()) @map("created_at")

  // Relations
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  session       Session   @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@unique([userId, sessionId])
  @@map("session_attendance")
}

// Course model
model Course {
  id              String   @id @default(cuid())
  titleAr         String   @map("title_ar")
  titleEn         String   @map("title_en")
  description     String?
  price           Decimal  @db.Decimal(10, 2)
  maxParticipants Int      @map("max_participants")
  startDate       DateTime @map("start_date")
  endDate         DateTime @map("end_date")
  instructorId    String   @map("instructor_id")
  status          CourseStatus @default(DRAFT)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  instructor      Speaker  @relation(fields: [instructorId], references: [id])
  enrollments     CourseEnrollment[]
  payments        Payment[]

  @@map("courses")
}

// Course enums
enum CourseStatus {
  DRAFT
  PUBLISHED
  FULL
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// Course Enrollment model
model CourseEnrollment {
  id              String    @id @default(cuid())
  userId          String    @map("user_id")
  courseId        String    @map("course_id")
  enrollmentDate  DateTime  @map("enrollment_date") @default(now())
  paymentStatus   PaymentStatus @map("payment_status") @default(PENDING)
  completionDate  DateTime? @map("completion_date")
  certificateUrl  String?   @map("certificate_url")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  course          Course    @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("course_enrollments")
}

// Document model
model Document {
  id          String       @id @default(cuid())
  userId      String       @map("user_id")
  type        DocumentType
  filename    String
  originalName String      @map("original_name")
  path        String
  size        Int
  mimeType    String       @map("mime_type")
  status      DocumentStatus @default(PENDING)
  verifiedBy  String?      @map("verified_by")
  verifiedAt  DateTime?    @map("verified_at")
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @updatedAt @map("updated_at")

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("documents")
}

// Document enums
enum DocumentType {
  QUALIFICATION_CERTIFICATE
  ID_DOCUMENT
  PROFILE_PHOTO
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

// QR Code model
model QRCode {
  id          String    @id @default(cuid())
  userId      String    @map("user_id")
  type        QRCodeType
  code        String    @unique
  data        Json
  expiresAt   DateTime? @map("expires_at")
  usedAt      DateTime? @map("used_at")
  createdAt   DateTime  @default(now()) @map("created_at")

  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("qr_codes")
}

// QR Code enum
enum QRCodeType {
  ENTRY
  SESSION_ATTENDANCE
  COURSE_ATTENDANCE
}

// Rating model
model Rating {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  sessionId   String?  @map("session_id")
  speakerId   String?  @map("speaker_id")
  rating      Int      // 1-5 stars
  comment     String?
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  session     Session? @relation(fields: [sessionId], references: [id])

  @@map("ratings")
}

// Note model
model Note {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  sessionId   String?  @map("session_id")
  title       String?
  content     String
  isPrivate   Boolean  @default(true) @map("is_private")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  session     Session? @relation(fields: [sessionId], references: [id])

  @@map("notes")
}

// Connection model (for networking)
model Connection {
  id          String          @id @default(cuid())
  requesterId String          @map("requester_id")
  receiverId  String          @map("receiver_id")
  status      ConnectionStatus @default(PENDING)
  message     String?
  createdAt   DateTime        @default(now()) @map("created_at")
  updatedAt   DateTime        @updatedAt @map("updated_at")

  // Relations
  requester   User            @relation("UserConnections", fields: [requesterId], references: [id], onDelete: Cascade)
  receiver    User            @relation("ConnectionRequests", fields: [receiverId], references: [id], onDelete: Cascade)

  @@unique([requesterId, receiverId])
  @@map("connections")
}

// Connection enum
enum ConnectionStatus {
  PENDING
  ACCEPTED
  REJECTED
  BLOCKED
}

// Message model
model Message {
  id          String      @id @default(cuid())
  senderId    String      @map("sender_id")
  receiverId  String      @map("receiver_id")
  content     String
  messageType MessageType @map("message_type") @default(TEXT)
  isRead      Boolean     @default(false) @map("is_read")
  readAt      DateTime?   @map("read_at")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  sender      User        @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiver    User        @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// Message enum
enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

// Notification model
model Notification {
  id          String           @id @default(cuid())
  userId      String           @map("user_id")
  title       String
  message     String
  type        NotificationType
  data        Json?
  readAt      DateTime?        @map("read_at")
  createdAt   DateTime         @default(now()) @map("created_at")

  // Relations
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Notification enum
enum NotificationType {
  REGISTRATION_WELCOME
  DOCUMENT_APPROVED
  DOCUMENT_REJECTED
  PAYMENT_REMINDER
  PAYMENT_CONFIRMED
  SESSION_REMINDER
  COURSE_REMINDER
  GENERAL_ANNOUNCEMENT
  SYSTEM_ALERT
}

// Audit Log model
model AuditLog {
  id          String   @id @default(cuid())
  userId      String?  @map("user_id")
  action      String
  resource    String
  resourceId  String?  @map("resource_id")
  oldValues   Json?    @map("old_values")
  newValues   Json?    @map("new_values")
  ipAddress   String?  @map("ip_address")
  userAgent   String?  @map("user_agent")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  user        User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// Gamification Models

// Point System model
model UserPoint {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  points      Int         @default(0)
  pointType   PointType   @map("point_type")
  reason      String
  sessionId   String?     @map("session_id")
  courseId    String?     @map("course_id")
  createdAt   DateTime    @default(now()) @map("created_at")

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_points")
}

// Point Type enum
enum PointType {
  SESSION_ATTENDANCE
  COURSE_COMPLETION
  PROFILE_COMPLETION
  SOCIAL_INTERACTION
  QUESTION_ASKED
  RATING_GIVEN
  DOCUMENT_UPLOAD
  EARLY_REGISTRATION
  REFERRAL
  SPECIAL_ACHIEVEMENT
}

// Badge model
model Badge {
  id          String      @id @default(cuid())
  name        String      @unique
  nameAr      String      @map("name_ar")
  nameEn      String      @map("name_en")
  description String?
  icon        String?
  color       String?
  criteria    Json        // Criteria for earning the badge
  isActive    Boolean     @default(true) @map("is_active")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  userBadges  UserBadge[]

  @@map("badges")
}

// User Badge junction model
model UserBadge {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  badgeId     String      @map("badge_id")
  earnedAt    DateTime    @default(now()) @map("earned_at")
  progress    Json?       // Progress towards earning the badge

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  badge       Badge       @relation(fields: [badgeId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeId])
  @@map("user_badges")
}

// Achievement model
model Achievement {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  type        AchievementType
  title       String
  description String?
  points      Int         @default(0)
  data        Json?       // Additional achievement data
  createdAt   DateTime    @default(now()) @map("created_at")

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("achievements")
}

// Achievement Type enum
enum AchievementType {
  FIRST_LOGIN
  PROFILE_COMPLETED
  FIRST_SESSION_ATTENDED
  FIVE_SESSIONS_ATTENDED
  TEN_SESSIONS_ATTENDED
  COURSE_COMPLETED
  NETWORKING_CHAMPION
  EARLY_BIRD
  FEEDBACK_PROVIDER
  KNOWLEDGE_SEEKER
  SOCIAL_BUTTERFLY
  CONFERENCE_EXPLORER
}

// Leaderboard model
model Leaderboard {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  totalPoints Int         @default(0) @map("total_points")
  rank        Int?
  period      LeaderboardPeriod @default(ALL_TIME)
  lastUpdated DateTime    @default(now()) @map("last_updated")

  @@unique([userId, period])
  @@map("leaderboards")
}

// Leaderboard Period enum
enum LeaderboardPeriod {
  DAILY
  WEEKLY
  MONTHLY
  ALL_TIME
}

// Exhibition Models

// Exhibitor model
model Exhibitor {
  id          String      @id @default(cuid())
  companyName String      @map("company_name")
  companyNameAr String?   @map("company_name_ar")
  companyNameEn String?   @map("company_name_en")
  description String?
  descriptionAr String?   @map("description_ar")
  descriptionEn String?   @map("description_en")
  logo        String?
  website     String?
  email       String?
  phone       String?
  address     String?
  country     String?
  category    ExhibitorCategory
  isActive    Boolean     @default(true) @map("is_active")
  isPremium   Boolean     @default(false) @map("is_premium")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  booths      Booth[]
  products    Product[]
  offers      SpecialOffer[]
  contacts    ExhibitorContact[]

  @@map("exhibitors")
}

// Exhibitor Category enum
enum ExhibitorCategory {
  DENTAL_EQUIPMENT
  DENTAL_MATERIALS
  PHARMACEUTICALS
  TECHNOLOGY
  EDUCATION
  SERVICES
  OTHER
}

// Booth model
model Booth {
  id          String      @id @default(cuid())
  exhibitorId String      @map("exhibitor_id")
  boothNumber String      @unique @map("booth_number")
  hallName    String      @map("hall_name")
  size        String?     // e.g., "3x3", "6x3"
  location    Json?       // Coordinates or location data
  description String?
  isActive    Boolean     @default(true) @map("is_active")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  exhibitor   Exhibitor   @relation(fields: [exhibitorId], references: [id], onDelete: Cascade)
  visits      BoothVisit[]

  @@map("booths")
}

// Product model
model Product {
  id          String      @id @default(cuid())
  exhibitorId String      @map("exhibitor_id")
  name        String
  nameAr      String?     @map("name_ar")
  nameEn      String?     @map("name_en")
  description String?
  descriptionAr String?   @map("description_ar")
  descriptionEn String?   @map("description_en")
  images      String[]    // Array of image URLs
  category    String?
  price       Float?
  currency    String?     @default("USD")
  isActive    Boolean     @default(true) @map("is_active")
  isFeatured  Boolean     @default(false) @map("is_featured")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  exhibitor   Exhibitor   @relation(fields: [exhibitorId], references: [id], onDelete: Cascade)

  @@map("products")
}

// Special Offer model
model SpecialOffer {
  id          String      @id @default(cuid())
  exhibitorId String      @map("exhibitor_id")
  title       String
  titleAr     String?     @map("title_ar")
  titleEn     String?     @map("title_en")
  description String
  descriptionAr String?   @map("description_ar")
  descriptionEn String?   @map("description_en")
  discount    Float?      // Percentage or amount
  validFrom   DateTime    @map("valid_from")
  validTo     DateTime    @map("valid_to")
  terms       String?     // Terms and conditions
  isActive    Boolean     @default(true) @map("is_active")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  exhibitor   Exhibitor   @relation(fields: [exhibitorId], references: [id], onDelete: Cascade)

  @@map("special_offers")
}

// Exhibitor Contact model
model ExhibitorContact {
  id          String      @id @default(cuid())
  exhibitorId String      @map("exhibitor_id")
  name        String
  position    String?
  email       String?
  phone       String?
  isPrimary   Boolean     @default(false) @map("is_primary")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  exhibitor   Exhibitor   @relation(fields: [exhibitorId], references: [id], onDelete: Cascade)

  @@map("exhibitor_contacts")
}

// Booth Visit model (for tracking user visits)
model BoothVisit {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  boothId     String      @map("booth_id")
  visitTime   DateTime    @default(now()) @map("visit_time")
  duration    Int?        // Duration in minutes
  notes       String?     // User notes about the visit

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  booth       Booth       @relation(fields: [boothId], references: [id], onDelete: Cascade)

  @@unique([userId, boothId])
  @@map("booth_visits")
}

// Digital Library Models

// Library Category model
model LibraryCategory {
  id          String      @id @default(cuid())
  name        String      @unique
  nameAr      String      @map("name_ar")
  nameEn      String      @map("name_en")
  description String?
  descriptionAr String?   @map("description_ar")
  descriptionEn String?   @map("description_en")
  icon        String?
  color       String?
  parentId    String?     @map("parent_id")
  isActive    Boolean     @default(true) @map("is_active")
  sortOrder   Int         @default(0) @map("sort_order")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  parent      LibraryCategory? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    LibraryCategory[] @relation("CategoryHierarchy")
  items       LibraryItem[]

  @@map("library_categories")
}

// Library Item model
model LibraryItem {
  id          String      @id @default(cuid())
  categoryId  String      @map("category_id")
  title       String
  titleAr     String?     @map("title_ar")
  titleEn     String?     @map("title_en")
  description String?
  descriptionAr String?   @map("description_ar")
  descriptionEn String?   @map("description_en")
  content     String?     // Rich text content
  contentAr   String?     @map("content_ar")
  contentEn   String?     @map("content_en")
  type        LibraryItemType
  fileUrl     String?     @map("file_url")
  fileName    String?     @map("file_name")
  fileSize    Int?        @map("file_size")
  mimeType    String?     @map("mime_type")
  thumbnailUrl String?    @map("thumbnail_url")
  duration    Int?        // For videos/audio in seconds
  author      String?
  authorAr    String?     @map("author_ar")
  authorEn    String?     @map("author_en")
  tags        String[]    // Array of tags
  isPublic    Boolean     @default(true) @map("is_public")
  isFeatured  Boolean     @default(false) @map("is_featured")
  isActive    Boolean     @default(true) @map("is_active")
  viewCount   Int         @default(0) @map("view_count")
  downloadCount Int       @default(0) @map("download_count")
  rating      Float?      // Average rating
  ratingCount Int         @default(0) @map("rating_count")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  category    LibraryCategory @relation(fields: [categoryId], references: [id])
  ratings     LibraryRating[]
  bookmarks   LibraryBookmark[]
  notes       LibraryNote[]
  views       LibraryView[]

  @@map("library_items")
}

// Library Item Type enum
enum LibraryItemType {
  DOCUMENT
  PRESENTATION
  VIDEO
  AUDIO
  IMAGE
  ARTICLE
  RESEARCH_PAPER
  CASE_STUDY
  GUIDELINE
  PROTOCOL
}

// Library Rating model
model LibraryRating {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  itemId      String      @map("item_id")
  rating      Int         // 1-5 stars
  comment     String?
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  item        LibraryItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@unique([userId, itemId])
  @@map("library_ratings")
}

// Library Bookmark model
model LibraryBookmark {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  itemId      String      @map("item_id")
  createdAt   DateTime    @default(now()) @map("created_at")

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  item        LibraryItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@unique([userId, itemId])
  @@map("library_bookmarks")
}

// Library Note model (user notes on library items)
model LibraryNote {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  itemId      String      @map("item_id")
  content     String
  isPrivate   Boolean     @default(true) @map("is_private")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  item        LibraryItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("library_notes")
}

// Library View model (track views for analytics)
model LibraryView {
  id          String      @id @default(cuid())
  userId      String?     @map("user_id")
  itemId      String      @map("item_id")
  ipAddress   String?     @map("ip_address")
  userAgent   String?     @map("user_agent")
  viewedAt    DateTime    @default(now()) @map("viewed_at")

  // Relations
  user        User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  item        LibraryItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@map("library_views")
}


