const express = require('express');
const { query } = require('express-validator');
const reportsController = require('../controllers/reportsController');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// All routes require admin authentication
router.use(auth.authenticate);
router.use(auth.requireRole(['SUPER_ADMIN', 'ADMIN']));

// Get dashboard statistics
router.get('/dashboard',
  reportsController.getDashboardStats
);

// Get user engagement report
router.get('/engagement',
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000')
  ],
  validation.handleValidationErrors,
  reportsController.getUserEngagementReport
);

// Get financial report
router.get('/financial',
  [
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
    query('groupBy').optional().isIn(['day', 'week', 'month']).withMessage('Invalid groupBy value')
  ],
  validation.handleValidationErrors,
  reportsController.getFinancialReport
);

// Get session analytics
router.get('/sessions',
  reportsController.getSessionAnalytics
);

// Export reports
router.get('/export',
  [
    query('reportType').isIn(['users', 'financial', 'sessions', 'engagement']).withMessage('Invalid report type'),
    query('format').optional().isIn(['json', 'csv']).withMessage('Invalid format'),
    query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
    query('endDate').optional().isISO8601().withMessage('Invalid end date format')
  ],
  validation.handleValidationErrors,
  reportsController.exportReport
);

module.exports = router;
