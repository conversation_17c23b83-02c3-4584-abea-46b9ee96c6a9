import 'package:flutter/foundation.dart';
import '../models/session_model.dart';
import '../services/api_service.dart';

class SessionsProvider extends ChangeNotifier {
  List<Session> _sessions = [];
  List<Session> _registeredSessions = [];
  List<SessionAttendance> _attendanceHistory = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Session> get sessions => _sessions;
  List<Session> get registeredSessions => _registeredSessions;
  List<SessionAttendance> get attendanceHistory => _attendanceHistory;
  bool get isLoading => _isLoading;
  String? get error => _error;

  final ApiService _apiService = ApiService();

  // Get all sessions
  Future<void> fetchSessions() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.getSessions();
      
      if (response['success']) {
        final List<dynamic> sessionsData = response['data'];
        _sessions = sessionsData.map((json) => Session.fromJson(json)).toList();
        
        // Sort sessions by start time
        _sessions.sort((a, b) => a.startTime.compareTo(b.startTime));
        
        notifyListeners();
      } else {
        _error = response['message'] ?? 'Failed to fetch sessions';
        notifyListeners();
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // Get session by ID
  Future<Session?> getSessionById(String id) async {
    try {
      final response = await _apiService.getSessionById(id);
      
      if (response['success']) {
        return Session.fromJson(response['data']);
      }
      return null;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Register for session
  Future<bool> registerForSession(String sessionId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.registerForSession(sessionId);
      
      if (response['success']) {
        // Update local session data
        final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
        if (sessionIndex != -1) {
          // Create updated session with registration status
          final updatedSession = Session(
            id: _sessions[sessionIndex].id,
            titleAr: _sessions[sessionIndex].titleAr,
            titleEn: _sessions[sessionIndex].titleEn,
            description: _sessions[sessionIndex].description,
            startTime: _sessions[sessionIndex].startTime,
            endTime: _sessions[sessionIndex].endTime,
            roomId: _sessions[sessionIndex].roomId,
            speakerId: _sessions[sessionIndex].speakerId,
            sessionType: _sessions[sessionIndex].sessionType,
            cmeHours: _sessions[sessionIndex].cmeHours,
            maxAttendees: _sessions[sessionIndex].maxAttendees,
            qrCode: _sessions[sessionIndex].qrCode,
            status: _sessions[sessionIndex].status,
            speaker: _sessions[sessionIndex].speaker,
            room: _sessions[sessionIndex].room,
            isRegistered: true,
            isAttended: _sessions[sessionIndex].isAttended,
          );
          
          _sessions[sessionIndex] = updatedSession;
          _registeredSessions.add(updatedSession);
        }
        
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Registration failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check in to session
  Future<bool> checkInToSession(String sessionId, String qrCode) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await _apiService.checkInSession(sessionId, qrCode);
      
      if (response['success']) {
        // Update local session data
        final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
        if (sessionIndex != -1) {
          final updatedSession = Session(
            id: _sessions[sessionIndex].id,
            titleAr: _sessions[sessionIndex].titleAr,
            titleEn: _sessions[sessionIndex].titleEn,
            description: _sessions[sessionIndex].description,
            startTime: _sessions[sessionIndex].startTime,
            endTime: _sessions[sessionIndex].endTime,
            roomId: _sessions[sessionIndex].roomId,
            speakerId: _sessions[sessionIndex].speakerId,
            sessionType: _sessions[sessionIndex].sessionType,
            cmeHours: _sessions[sessionIndex].cmeHours,
            maxAttendees: _sessions[sessionIndex].maxAttendees,
            qrCode: _sessions[sessionIndex].qrCode,
            status: _sessions[sessionIndex].status,
            speaker: _sessions[sessionIndex].speaker,
            room: _sessions[sessionIndex].room,
            isRegistered: _sessions[sessionIndex].isRegistered,
            isAttended: true,
          );
          
          _sessions[sessionIndex] = updatedSession;
        }
        
        // Add to attendance history
        if (response['data'] != null) {
          final attendance = SessionAttendance.fromJson(response['data']);
          _attendanceHistory.add(attendance);
        }
        
        notifyListeners();
        return true;
      } else {
        _error = response['message'] ?? 'Check-in failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get sessions by day
  List<Session> getSessionsByDay(DateTime day) {
    return _sessions.where((session) {
      return session.startTime.year == day.year &&
             session.startTime.month == day.month &&
             session.startTime.day == day.day;
    }).toList();
  }

  // Get sessions by type
  List<Session> getSessionsByType(String type) {
    return _sessions.where((session) => session.sessionType == type).toList();
  }

  // Get live sessions
  List<Session> get liveSessions {
    final now = DateTime.now();
    return _sessions.where((session) {
      return session.startTime.isBefore(now) && session.endTime.isAfter(now);
    }).toList();
  }

  // Get upcoming sessions
  List<Session> get upcomingSessions {
    final now = DateTime.now();
    return _sessions.where((session) {
      return session.startTime.isAfter(now);
    }).take(5).toList();
  }

  // Get today's sessions
  List<Session> get todaySessions {
    final today = DateTime.now();
    return getSessionsByDay(today);
  }

  // Search sessions
  List<Session> searchSessions(String query) {
    if (query.isEmpty) return _sessions;
    
    final lowerQuery = query.toLowerCase();
    return _sessions.where((session) {
      return session.titleAr.toLowerCase().contains(lowerQuery) ||
             session.titleEn.toLowerCase().contains(lowerQuery) ||
             session.speaker?.nameAr.toLowerCase().contains(lowerQuery) == true ||
             session.speaker?.nameEn.toLowerCase().contains(lowerQuery) == true ||
             session.description?.toLowerCase().contains(lowerQuery) == true;
    }).toList();
  }

  // Filter sessions
  List<Session> filterSessions({
    String? type,
    String? status,
    bool? isRegistered,
    DateTime? date,
  }) {
    return _sessions.where((session) {
      if (type != null && session.sessionType != type) return false;
      if (status != null && session.status != status) return false;
      if (isRegistered != null && session.isRegistered != isRegistered) return false;
      if (date != null) {
        if (session.startTime.year != date.year ||
            session.startTime.month != date.month ||
            session.startTime.day != date.day) return false;
      }
      return true;
    }).toList();
  }

  // Get session statistics
  Map<String, int> get sessionStats {
    return {
      'total': _sessions.length,
      'registered': _registeredSessions.length,
      'attended': _attendanceHistory.length,
      'live': liveSessions.length,
      'upcoming': upcomingSessions.length,
    };
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Refresh data
  Future<void> refresh() async {
    await fetchSessions();
  }

  // Clear all data
  void clear() {
    _sessions.clear();
    _registeredSessions.clear();
    _attendanceHistory.clear();
    _error = null;
    notifyListeners();
  }
}
