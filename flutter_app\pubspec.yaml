name: idec_2025
description: IDEC 2025 Conference Management App

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_animations: ^1.1.1

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3
  auto_route: ^7.9.2

  # HTTP & API
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  pretty_dio_logger: ^1.3.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  secure_storage: ^1.1.0

  # Authentication & Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  jwt_decoder: ^2.0.1

  # QR Code & Camera
  qr_code_scanner: ^1.0.1
  mobile_scanner: ^3.5.6
  qr_flutter: ^4.1.0
  camera: ^0.10.5+7
  image_picker: ^1.0.4

  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.0

  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  geolocator: ^10.1.0

  # Utils
  intl: ^0.19.0
  url_launcher: ^6.2.2
  share_plus: ^7.2.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # File Handling
  file_picker: ^6.1.1
  open_file: ^3.3.2
  pdf_viewer_plugin: ^2.0.1

  # Charts & Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^23.2.7

  # Social Features
  flutter_mentions: ^2.0.2
  emoji_picker_flutter: ^1.6.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1
  auto_route_generator: ^7.3.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
