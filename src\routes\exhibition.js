const express = require('express');
const { body, param, query } = require('express-validator');
const exhibitionController = require('../controllers/exhibitionController');
const auth = require('../middleware/auth');
const validation = require('../middleware/validation');

const router = express.Router();

// Public routes (no authentication required)

// Get all exhibitors
router.get('/exhibitors',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('category').optional().isIn([
      'DENTAL_EQUIPMENT',
      'DENTAL_MATERIALS', 
      'PHARMACEUTICALS',
      'TECHNOLOGY',
      'EDUCATION',
      'SERVICES',
      'OTHER'
    ]).withMessage('Invalid category'),
    query('search').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
    query('isPremium').optional().isBoolean().withMessage('isPremium must be boolean'),
    query('isActive').optional().isBoolean().withMessage('isActive must be boolean')
  ],
  validation.handleValidationErrors,
  exhibitionController.getExhibitors
);

// Get exhibitor by ID
router.get('/exhibitors/:id',
  [
    param('id').notEmpty().withMessage('Exhibitor ID is required')
  ],
  validation.handleValidationErrors,
  exhibitionController.getExhibitorById
);

// Get all booths
router.get('/booths',
  [
    query('hallName').optional().isString(),
    query('search').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters')
  ],
  validation.handleValidationErrors,
  exhibitionController.getBooths
);

// Get products
router.get('/products',
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('exhibitorId').optional().isString(),
    query('category').optional().isString(),
    query('search').optional().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
    query('isFeatured').optional().isBoolean().withMessage('isFeatured must be boolean')
  ],
  validation.handleValidationErrors,
  exhibitionController.getProducts
);

// Get special offers
router.get('/offers',
  [
    query('exhibitorId').optional().isString()
  ],
  validation.handleValidationErrors,
  exhibitionController.getSpecialOffers
);

// Search exhibition
router.get('/search',
  [
    query('q').isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
    query('category').optional().isIn([
      'DENTAL_EQUIPMENT',
      'DENTAL_MATERIALS', 
      'PHARMACEUTICALS',
      'TECHNOLOGY',
      'EDUCATION',
      'SERVICES',
      'OTHER'
    ]).withMessage('Invalid category')
  ],
  validation.handleValidationErrors,
  exhibitionController.searchExhibition
);

// Get exhibition statistics
router.get('/stats', exhibitionController.getExhibitionStats);

// Get exhibition map/layout
router.get('/map', exhibitionController.getExhibitionMap);

// Get featured content
router.get('/featured', exhibitionController.getFeaturedContent);

// Protected routes (require authentication)
router.use(auth.authenticate);

// Visit booth
router.post('/booths/:id/visit',
  [
    param('id').notEmpty().withMessage('Booth ID is required'),
    body('notes').optional().isLength({ max: 500 }).withMessage('Notes must be less than 500 characters')
  ],
  validation.handleValidationErrors,
  exhibitionController.visitBooth
);

// Get user's booth visits
router.get('/my-visits', exhibitionController.getUserBoothVisits);

module.exports = router;
