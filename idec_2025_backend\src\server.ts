import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Import configurations and middleware
import { config } from '@/config/config';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { logger } from '@/utils/logger';
import { connectDatabase } from '@/config/database';
import { connectRedis } from '@/config/redis';

// Import routes
import authRoutes from '@/routes/auth.routes';
import userRoutes from '@/routes/user.routes';
import conferenceRoutes from '@/routes/conference.routes';
import sessionRoutes from '@/routes/session.routes';
import speakerRoutes from '@/routes/speaker.routes';
import courseRoutes from '@/routes/course.routes';
import paymentRoutes from '@/routes/payment.routes';
import qrRoutes from '@/routes/qr.routes';
import newsRoutes from '@/routes/news.routes';
import libraryRoutes from '@/routes/library.routes';
import exhibitorRoutes from '@/routes/exhibitor.routes';
import socialRoutes from '@/routes/social.routes';
import notificationRoutes from '@/routes/notification.routes';
import adminRoutes from '@/routes/admin.routes';
import analyticsRoutes from '@/routes/analytics.routes';

// Load environment variables
dotenv.config();

class Server {
  private app: express.Application;
  private httpServer: any;
  private io: SocketIOServer;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.port;
    this.httpServer = createServer(this.app);
    this.io = new SocketIOServer(this.httpServer, {
      cors: {
        origin: config.cors.origin,
        credentials: config.cors.credentials,
      },
    });

    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSocketIO();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: config.security.helmet.csp,
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: 'Too many requests from this IP, please try again later.',
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Compression middleware
    this.app.use(compression());

    // Logging middleware
    if (config.nodeEnv !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => logger.info(message.trim()),
        },
      }));
    }

    // Trust proxy if behind reverse proxy
    if (config.security.trustProxy) {
      this.app.set('trust proxy', 1);
    }

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
        version: process.env.npm_package_version || '1.0.0',
      });
    });

    // API documentation endpoint
    if (config.swagger.enabled) {
      this.app.get('/api/docs', (req, res) => {
        res.json({
          message: 'API Documentation',
          version: config.apiVersion,
          endpoints: {
            auth: '/api/v1/auth',
            users: '/api/v1/users',
            conference: '/api/v1/conference',
            sessions: '/api/v1/sessions',
            speakers: '/api/v1/speakers',
            courses: '/api/v1/courses',
            payments: '/api/v1/payments',
            qr: '/api/v1/qr',
            news: '/api/v1/news',
            library: '/api/v1/library',
            exhibitors: '/api/v1/exhibitors',
            social: '/api/v1/social',
            notifications: '/api/v1/notifications',
            admin: '/api/v1/admin',
            analytics: '/api/v1/analytics',
          },
        });
      });
    }
  }

  private initializeRoutes(): void {
    const apiPrefix = `/api/${config.apiVersion}`;

    // API routes
    this.app.use(`${apiPrefix}/auth`, authRoutes);
    this.app.use(`${apiPrefix}/users`, userRoutes);
    this.app.use(`${apiPrefix}/conference`, conferenceRoutes);
    this.app.use(`${apiPrefix}/sessions`, sessionRoutes);
    this.app.use(`${apiPrefix}/speakers`, speakerRoutes);
    this.app.use(`${apiPrefix}/courses`, courseRoutes);
    this.app.use(`${apiPrefix}/payments`, paymentRoutes);
    this.app.use(`${apiPrefix}/qr`, qrRoutes);
    this.app.use(`${apiPrefix}/news`, newsRoutes);
    this.app.use(`${apiPrefix}/library`, libraryRoutes);
    this.app.use(`${apiPrefix}/exhibitors`, exhibitorRoutes);
    this.app.use(`${apiPrefix}/social`, socialRoutes);
    this.app.use(`${apiPrefix}/notifications`, notificationRoutes);
    this.app.use(`${apiPrefix}/admin`, adminRoutes);
    this.app.use(`${apiPrefix}/analytics`, analyticsRoutes);

    // Serve static files
    this.app.use('/uploads', express.static('uploads'));
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Socket connected: ${socket.id}`);

      // Join user to their personal room
      socket.on('join-user-room', (userId: string) => {
        socket.join(`user-${userId}`);
        logger.info(`User ${userId} joined their room`);
      });

      // Join session room for live updates
      socket.on('join-session', (sessionId: string) => {
        socket.join(`session-${sessionId}`);
        logger.info(`Socket ${socket.id} joined session ${sessionId}`);
      });

      // Leave session room
      socket.on('leave-session', (sessionId: string) => {
        socket.leave(`session-${sessionId}`);
        logger.info(`Socket ${socket.id} left session ${sessionId}`);
      });

      // Handle live Q&A
      socket.on('session-question', (data) => {
        this.io.to(`session-${data.sessionId}`).emit('new-question', data);
      });

      // Handle live polls
      socket.on('poll-vote', (data) => {
        this.io.to(`session-${data.sessionId}`).emit('poll-update', data);
      });

      // Handle chat messages
      socket.on('chat-message', (data) => {
        this.io.to(`session-${data.sessionId}`).emit('new-message', data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info(`Socket disconnected: ${socket.id}`);
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await connectDatabase();
      logger.info('Database connected successfully');

      // Connect to Redis
      await connectRedis();
      logger.info('Redis connected successfully');

      // Start server
      this.httpServer.listen(this.port, () => {
        logger.info(`🚀 Server running on port ${this.port}`);
        logger.info(`📚 API Documentation: http://localhost:${this.port}/api/docs`);
        logger.info(`🏥 Health Check: http://localhost:${this.port}/health`);
        logger.info(`🌍 Environment: ${config.nodeEnv}`);
      });

      // Graceful shutdown
      process.on('SIGTERM', this.gracefulShutdown.bind(this));
      process.on('SIGINT', this.gracefulShutdown.bind(this));

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    logger.info(`Received ${signal}. Starting graceful shutdown...`);

    this.httpServer.close(() => {
      logger.info('HTTP server closed');
      process.exit(0);
    });

    // Force close after 30 seconds
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 30000);
  }

  public getApp(): express.Application {
    return this.app;
  }

  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Create and start server
const server = new Server();

if (require.main === module) {
  server.start();
}

export default server;
