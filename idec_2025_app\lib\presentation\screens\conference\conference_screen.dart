import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class ConferenceScreen extends StatefulWidget {
  const ConferenceScreen({super.key});

  @override
  State<ConferenceScreen> createState() => _ConferenceScreenState();
}

class _ConferenceScreenState extends State<ConferenceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'المؤتمر',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Background Pattern
                      Positioned.fill(
                        child: Opacity(
                          opacity: 0.1,
                          child: Image.asset(
                            'assets/images/pattern.png',
                            repeat: ImageRepeat.repeat,
                          ),
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60), // Account for app bar
                            
                            // Conference Icon
                            Container(
                              padding: const EdgeInsets.all(AppSizes.paddingL),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                              ),
                              child: const Icon(
                                Icons.event,
                                size: 60,
                                color: Color(AppColors.primaryWhite),
                              ),
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Conference Info
                            Text(
                              AppConstants.conferenceName,
                              style: const TextStyle(
                                color: Color(AppColors.primaryWhite),
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            
                            const SizedBox(height: AppSizes.paddingS),
                            
                            Text(
                              '15-17 مارس 2025 • ${AppConstants.conferenceLocation}',
                              style: TextStyle(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                fontSize: 14,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                tabs: const [
                  Tab(text: 'نظرة عامة'),
                  Tab(text: 'الجلسات'),
                  Tab(text: 'المعرض'),
                ],
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(),
            _buildSessionsTab(),
            _buildExhibitionTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Stats
          _buildQuickStats(),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // About Section
          _buildAboutSection(),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Key Features
          _buildKeyFeatures(),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Schedule Overview
          _buildScheduleOverview(),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(AppColors.primaryRed).withValues(alpha: 0.1),
            const Color(AppColors.primaryBlack).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات المؤتمر',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.people,
                  title: '50+',
                  subtitle: 'متحدث خبير',
                  color: const Color(AppColors.primaryRed),
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.event,
                  title: '100+',
                  subtitle: 'جلسة علمية',
                  color: const Color(AppColors.primaryBlack),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  icon: Icons.schedule,
                  title: '24',
                  subtitle: 'ساعة تعليم مستمر',
                  color: const Color(AppColors.primaryRed),
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: _buildStatCard(
                  icon: Icons.location_on,
                  title: '3',
                  subtitle: 'أيام مكثفة',
                  color: const Color(AppColors.primaryBlack),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: AppSizes.iconL,
            color: color,
          ),
          const SizedBox(height: AppSizes.paddingS),
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عن المؤتمر',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          Text(
            AppConstants.conferenceDescription,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: const Color(AppColors.primaryBlack),
              height: 1.6,
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          Text(
            'يهدف هذا المؤتمر إلى جمع أفضل الخبراء والمختصين في مجال طب الأسنان لتبادل المعرفة والخبرات، ومناقشة أحدث التطورات والتقنيات في هذا المجال الحيوي.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: const Color(AppColors.grey),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyFeatures() {
    final features = [
      {
        'icon': Icons.science,
        'title': 'أحدث التقنيات',
        'description': 'تعرف على أحدث التقنيات والابتكارات في طب الأسنان',
      },
      {
        'icon': Icons.group,
        'title': 'شبكة مهنية',
        'description': 'تواصل مع زملاء المهنة وبناء شبكة علاقات قوية',
      },
      {
        'icon': Icons.school,
        'title': 'تعليم مستمر',
        'description': 'احصل على ساعات التعليم المستمر المعتمدة',
      },
      {
        'icon': Icons.workspace_premium,
        'title': 'شهادات معتمدة',
        'description': 'شهادات حضور وإتمام معتمدة من الجهات المختصة',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المميزات الرئيسية',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(AppColors.primaryBlack),
          ),
        ),
        
        const SizedBox(height: AppSizes.paddingM),
        
        ...features.map((feature) => Container(
          margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
          padding: const EdgeInsets.all(AppSizes.paddingM),
          decoration: BoxDecoration(
            color: const Color(AppColors.primaryWhite),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
            boxShadow: [
              BoxShadow(
                color: const Color(AppColors.primaryBlack).withValues(alpha: 0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(AppColors.primaryRed),
                      const Color(AppColors.darkRed),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: const Color(AppColors.primaryWhite),
                  size: AppSizes.iconM,
                ),
              ),
              
              const SizedBox(width: AppSizes.paddingM),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingXS),
                    Text(
                      feature['description'] as String,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(AppColors.grey),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildScheduleOverview() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(AppColors.primaryBlack),
            const Color(AppColors.primaryBlack).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الجدول الزمني',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryWhite),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          _buildDaySchedule('اليوم الأول', '15 مارس 2025', 'الافتتاح والجلسات الرئيسية'),
          _buildDaySchedule('اليوم الثاني', '16 مارس 2025', 'ورش العمل والجلسات التخصصية'),
          _buildDaySchedule('اليوم الثالث', '17 مارس 2025', 'المعرض والختام'),
        ],
      ),
    );
  }

  Widget _buildDaySchedule(String day, String date, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(AppColors.primaryRed),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          const SizedBox(width: AppSizes.paddingM),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  day,
                  style: const TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  date,
                  style: TextStyle(
                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionsTab() {
    return const Center(
      child: Text(
        'جلسات المؤتمر\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildExhibitionTab() {
    return const Center(
      child: Text(
        'المعرض المصاحب\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }
}
