const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class GamificationService {
  // Award points to user
  async awardPoints(userId, pointType, points, reason, sessionId = null, courseId = null) {
    try {
      // Create point record
      const userPoint = await prisma.userPoint.create({
        data: {
          userId,
          points,
          pointType,
          reason,
          sessionId,
          courseId
        }
      });

      // Update leaderboard
      await this.updateLeaderboard(userId);

      // Check for badge achievements
      await this.checkBadgeAchievements(userId);

      // Check for general achievements
      await this.checkAchievements(userId, pointType);

      logger.info(`Awarded ${points} points to user ${userId} for ${reason}`);
      return userPoint;
    } catch (error) {
      logger.error('Error awarding points:', error);
      throw error;
    }
  }

  // Get user's total points
  async getUserPoints(userId) {
    try {
      const result = await prisma.userPoint.aggregate({
        where: { userId },
        _sum: { points: true }
      });

      return result._sum.points || 0;
    } catch (error) {
      logger.error('Error getting user points:', error);
      throw error;
    }
  }

  // Get user's point history
  async getUserPointHistory(userId, limit = 50) {
    try {
      return await prisma.userPoint.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          user: {
            select: {
              arabicName: true,
              englishName: true
            }
          }
        }
      });
    } catch (error) {
      logger.error('Error getting user point history:', error);
      throw error;
    }
  }

  // Update leaderboard for user
  async updateLeaderboard(userId) {
    try {
      const totalPoints = await this.getUserPoints(userId);

      // Update all-time leaderboard
      await prisma.leaderboard.upsert({
        where: {
          userId_period: {
            userId,
            period: 'ALL_TIME'
          }
        },
        update: {
          totalPoints,
          lastUpdated: new Date()
        },
        create: {
          userId,
          totalPoints,
          period: 'ALL_TIME'
        }
      });

      // Update ranks for all users
      await this.updateRanks('ALL_TIME');

    } catch (error) {
      logger.error('Error updating leaderboard:', error);
      throw error;
    }
  }

  // Update ranks for leaderboard
  async updateRanks(period = 'ALL_TIME') {
    try {
      const leaderboard = await prisma.leaderboard.findMany({
        where: { period },
        orderBy: { totalPoints: 'desc' }
      });

      for (let i = 0; i < leaderboard.length; i++) {
        await prisma.leaderboard.update({
          where: { id: leaderboard[i].id },
          data: { rank: i + 1 }
        });
      }
    } catch (error) {
      logger.error('Error updating ranks:', error);
      throw error;
    }
  }

  // Get leaderboard
  async getLeaderboard(period = 'ALL_TIME', limit = 100) {
    try {
      return await prisma.leaderboard.findMany({
        where: { period },
        orderBy: { rank: 'asc' },
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true
            }
          }
        }
      });
    } catch (error) {
      logger.error('Error getting leaderboard:', error);
      throw error;
    }
  }

  // Check and award badges
  async checkBadgeAchievements(userId) {
    try {
      const badges = await prisma.badge.findMany({
        where: { isActive: true }
      });

      for (const badge of badges) {
        const hasEarned = await this.checkBadgeCriteria(userId, badge);
        if (hasEarned) {
          await this.awardBadge(userId, badge.id);
        }
      }
    } catch (error) {
      logger.error('Error checking badge achievements:', error);
      throw error;
    }
  }

  // Check if user meets badge criteria
  async checkBadgeCriteria(userId, badge) {
    try {
      // Check if user already has this badge
      const existingBadge = await prisma.userBadge.findUnique({
        where: {
          userId_badgeId: {
            userId,
            badgeId: badge.id
          }
        }
      });

      if (existingBadge) return false;

      const criteria = badge.criteria;
      
      // Example criteria checks
      switch (criteria.type) {
        case 'session_attendance':
          const sessionCount = await prisma.sessionAttendance.count({
            where: { userId }
          });
          return sessionCount >= criteria.count;

        case 'total_points':
          const totalPoints = await this.getUserPoints(userId);
          return totalPoints >= criteria.points;

        case 'course_completion':
          const completedCourses = await prisma.courseEnrollment.count({
            where: { 
              userId,
              completionDate: { not: null }
            }
          });
          return completedCourses >= criteria.count;

        case 'social_connections':
          const connections = await prisma.connection.count({
            where: {
              OR: [
                { requesterId: userId, status: 'ACCEPTED' },
                { receiverId: userId, status: 'ACCEPTED' }
              ]
            }
          });
          return connections >= criteria.count;

        default:
          return false;
      }
    } catch (error) {
      logger.error('Error checking badge criteria:', error);
      return false;
    }
  }

  // Award badge to user
  async awardBadge(userId, badgeId) {
    try {
      const userBadge = await prisma.userBadge.create({
        data: {
          userId,
          badgeId
        }
      });

      // Award points for earning badge
      await this.awardPoints(userId, 'SPECIAL_ACHIEVEMENT', 50, 'Badge earned');

      logger.info(`Awarded badge ${badgeId} to user ${userId}`);
      return userBadge;
    } catch (error) {
      if (error.code === 'P2002') {
        // User already has this badge
        return null;
      }
      logger.error('Error awarding badge:', error);
      throw error;
    }
  }

  // Get user badges
  async getUserBadges(userId) {
    try {
      return await prisma.userBadge.findMany({
        where: { userId },
        include: {
          badge: true
        },
        orderBy: { earnedAt: 'desc' }
      });
    } catch (error) {
      logger.error('Error getting user badges:', error);
      throw error;
    }
  }

  // Check and create achievements
  async checkAchievements(userId, pointType) {
    try {
      const achievements = [];

      // First login achievement
      if (pointType === 'PROFILE_COMPLETION') {
        const existingAchievement = await prisma.achievement.findFirst({
          where: { userId, type: 'FIRST_LOGIN' }
        });

        if (!existingAchievement) {
          const achievement = await prisma.achievement.create({
            data: {
              userId,
              type: 'FIRST_LOGIN',
              title: 'Welcome to IDEC 2025!',
              description: 'Completed your first login',
              points: 10
            }
          });
          achievements.push(achievement);
        }
      }

      // Session attendance achievements
      if (pointType === 'SESSION_ATTENDANCE') {
        const sessionCount = await prisma.sessionAttendance.count({
          where: { userId }
        });

        const milestones = [
          { count: 1, type: 'FIRST_SESSION_ATTENDED', title: 'First Session!', points: 20 },
          { count: 5, type: 'FIVE_SESSIONS_ATTENDED', title: 'Session Explorer', points: 50 },
          { count: 10, type: 'TEN_SESSIONS_ATTENDED', title: 'Knowledge Seeker', points: 100 }
        ];

        for (const milestone of milestones) {
          if (sessionCount === milestone.count) {
            const existingAchievement = await prisma.achievement.findFirst({
              where: { userId, type: milestone.type }
            });

            if (!existingAchievement) {
              const achievement = await prisma.achievement.create({
                data: {
                  userId,
                  type: milestone.type,
                  title: milestone.title,
                  description: `Attended ${milestone.count} session(s)`,
                  points: milestone.points
                }
              });
              achievements.push(achievement);
              
              // Award additional points for achievement
              await this.awardPoints(userId, 'SPECIAL_ACHIEVEMENT', milestone.points, milestone.title);
            }
          }
        }
      }

      return achievements;
    } catch (error) {
      logger.error('Error checking achievements:', error);
      throw error;
    }
  }

  // Get user achievements
  async getUserAchievements(userId) {
    try {
      return await prisma.achievement.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      });
    } catch (error) {
      logger.error('Error getting user achievements:', error);
      throw error;
    }
  }

  // Get user's gamification summary
  async getUserGamificationSummary(userId) {
    try {
      const [totalPoints, badges, achievements, leaderboardPosition] = await Promise.all([
        this.getUserPoints(userId),
        this.getUserBadges(userId),
        this.getUserAchievements(userId),
        prisma.leaderboard.findUnique({
          where: {
            userId_period: {
              userId,
              period: 'ALL_TIME'
            }
          }
        })
      ]);

      return {
        totalPoints,
        badges: badges.length,
        achievements: achievements.length,
        rank: leaderboardPosition?.rank || null,
        badgeDetails: badges,
        achievementDetails: achievements
      };
    } catch (error) {
      logger.error('Error getting user gamification summary:', error);
      throw error;
    }
  }
}

module.exports = new GamificationService();
