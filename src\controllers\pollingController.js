const pollingService = require('../services/pollingService');
const logger = require('../utils/logger');

class PollingController {
  // Create poll
  async createPoll(req, res) {
    try {
      const pollData = req.body;
      const createdBy = req.user.id;

      const poll = await pollingService.createPoll(pollData, createdBy);

      res.status(201).json({
        success: true,
        message: 'Poll created successfully',
        data: poll
      });
    } catch (error) {
      logger.error('Error creating poll:', error);
      res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to create poll',
          code: 'CREATE_POLL_ERROR'
        }
      });
    }
  }

  // Get poll by ID
  async getPollById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const poll = await pollingService.getPollById(id, userId);

      res.json({
        success: true,
        data: poll
      });
    } catch (error) {
      logger.error('Error getting poll by ID:', error);
      
      if (error.message === 'Poll not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Poll not found',
            code: 'POLL_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get poll',
          code: 'GET_POLL_ERROR'
        }
      });
    }
  }

  // Submit poll response
  async submitPollResponse(req, res) {
    try {
      const { id: pollId } = req.params;
      const responseData = req.body;
      const userId = req.user?.id;

      const response = await pollingService.submitPollResponse(
        pollId,
        userId,
        responseData,
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: 'Response submitted successfully',
        data: response
      });
    } catch (error) {
      logger.error('Error submitting poll response:', error);
      
      const errorMessages = {
        'Poll not found': 'POLL_NOT_FOUND',
        'Poll is not active or live': 'POLL_NOT_ACTIVE',
        'Poll has ended': 'POLL_ENDED',
        'User has already responded to this poll': 'ALREADY_RESPONDED',
        'Text response is required for text polls': 'TEXT_REQUIRED',
        'Rating value must be between 1 and 5': 'INVALID_RATING',
        'Option selection is required': 'OPTION_REQUIRED'
      };

      const errorCode = errorMessages[error.message] || 'SUBMIT_RESPONSE_ERROR';
      const statusCode = errorCode === 'POLL_NOT_FOUND' ? 404 : 400;

      res.status(statusCode).json({
        success: false,
        error: {
          message: error.message || 'Failed to submit response',
          code: errorCode
        }
      });
    }
  }

  // Get live polls for session
  async getLivePolls(req, res) {
    try {
      const { sessionId } = req.params;

      const polls = await pollingService.getLivePolls(sessionId);

      res.json({
        success: true,
        data: polls
      });
    } catch (error) {
      logger.error('Error getting live polls:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get live polls',
          code: 'GET_LIVE_POLLS_ERROR'
        }
      });
    }
  }

  // Toggle poll live status
  async togglePollLive(req, res) {
    try {
      const { id: pollId } = req.params;
      const { isLive } = req.body;
      const userId = req.user.id;

      const poll = await pollingService.togglePollLive(pollId, isLive, userId);

      res.json({
        success: true,
        message: `Poll ${isLive ? 'started' : 'stopped'} successfully`,
        data: poll
      });
    } catch (error) {
      logger.error('Error toggling poll live status:', error);
      
      if (error.message === 'Poll not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Poll not found',
            code: 'POLL_NOT_FOUND'
          }
        });
      }

      if (error.message === 'Unauthorized to modify this poll') {
        return res.status(403).json({
          success: false,
          error: {
            message: 'Unauthorized to modify this poll',
            code: 'UNAUTHORIZED'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to toggle poll status',
          code: 'TOGGLE_POLL_ERROR'
        }
      });
    }
  }

  // Get poll results
  async getPollResults(req, res) {
    try {
      const { id: pollId } = req.params;

      const results = await pollingService.getPollResults(pollId);

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      logger.error('Error getting poll results:', error);
      
      if (error.message === 'Poll not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Poll not found',
            code: 'POLL_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get poll results',
          code: 'GET_POLL_RESULTS_ERROR'
        }
      });
    }
  }

  // Get user's polls
  async getUserPolls(req, res) {
    try {
      const userId = req.user.id;
      const { sessionId, isActive, isLive } = req.query;

      const filters = {};
      if (sessionId) filters.sessionId = sessionId;
      if (isActive !== undefined) filters.isActive = isActive === 'true';
      if (isLive !== undefined) filters.isLive = isLive === 'true';

      const polls = await pollingService.getUserPolls(userId, filters);

      res.json({
        success: true,
        data: polls
      });
    } catch (error) {
      logger.error('Error getting user polls:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get user polls',
          code: 'GET_USER_POLLS_ERROR'
        }
      });
    }
  }

  // Delete poll
  async deletePoll(req, res) {
    try {
      const { id: pollId } = req.params;
      const userId = req.user.id;

      await pollingService.deletePoll(pollId, userId);

      res.json({
        success: true,
        message: 'Poll deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting poll:', error);
      
      const errorMessages = {
        'Poll not found': { code: 'POLL_NOT_FOUND', status: 404 },
        'Unauthorized to delete this poll': { code: 'UNAUTHORIZED', status: 403 },
        'Cannot delete a live poll': { code: 'POLL_IS_LIVE', status: 400 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'DELETE_POLL_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to delete poll',
          code: errorInfo.code
        }
      });
    }
  }

  // Get polling statistics
  async getPollingStats(req, res) {
    try {
      const stats = await pollingService.getPollingStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting polling stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get polling statistics',
          code: 'GET_POLLING_STATS_ERROR'
        }
      });
    }
  }

  // Update poll
  async updatePoll(req, res) {
    try {
      const { id: pollId } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      // Check if poll exists and user owns it
      const existingPoll = await pollingService.getPollById(pollId);
      
      if (existingPoll.createdBy !== userId) {
        return res.status(403).json({
          success: false,
          error: {
            message: 'Unauthorized to update this poll',
            code: 'UNAUTHORIZED'
          }
        });
      }

      if (existingPoll.isLive) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Cannot update a live poll',
            code: 'POLL_IS_LIVE'
          }
        });
      }

      // Update poll (implementation would depend on specific requirements)
      // For now, we'll return a placeholder response
      res.json({
        success: true,
        message: 'Poll update functionality to be implemented',
        data: existingPoll
      });
    } catch (error) {
      logger.error('Error updating poll:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to update poll',
          code: 'UPDATE_POLL_ERROR'
        }
      });
    }
  }
}

module.exports = new PollingController();
