import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Language,
  Dashboard,
  Person,
  ExitToApp,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const MainLayout = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const { toggleLanguage } = useLanguage();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [anchorEl, setAnchorEl] = useState(null);

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    await logout();
    handleMenuClose();
    navigate('/');
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar position="sticky" elevation={1}>
        <Toolbar>
          {/* Logo/Title */}
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, cursor: 'pointer' }}
            onClick={() => navigate('/')}
          >
            IDEC 2025
          </Typography>

          {/* Desktop Navigation */}
          <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
            <Button color="inherit" onClick={() => navigate('/')}>
              الرئيسية
            </Button>
            <Button color="inherit" onClick={() => navigate('/conference')}>
              المؤتمر
            </Button>
            <Button color="inherit" onClick={() => navigate('/speakers')}>
              المتحدثون
            </Button>

            {isAuthenticated && (
              <Button color="inherit" onClick={() => navigate('/dashboard')}>
                لوحة التحكم
              </Button>
            )}
          </Box>

          {/* Language Toggle */}
          <IconButton color="inherit" onClick={toggleLanguage} sx={{ mr: 1 }}>
            <Language />
          </IconButton>

          {/* User Menu */}
          {isAuthenticated ? (
            <Box>
              <IconButton
                size="large"
                onClick={handleProfileMenuOpen}
                color="inherit"
              >
                <Avatar sx={{ width: 32, height: 32 }}>
                  {user?.arabicName?.charAt(0) || user?.englishName?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <MenuItem onClick={() => { navigate('/dashboard/profile'); handleMenuClose(); }}>
                  <Person sx={{ mr: 1 }} />
                  الملف الشخصي
                </MenuItem>
                <MenuItem onClick={() => { navigate('/dashboard'); handleMenuClose(); }}>
                  <Dashboard sx={{ mr: 1 }} />
                  لوحة التحكم
                </MenuItem>
                <MenuItem onClick={handleLogout}>
                  <ExitToApp sx={{ mr: 1 }} />
                  تسجيل الخروج
                </MenuItem>
              </Menu>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button color="inherit" onClick={() => navigate('/login')}>
                تسجيل الدخول
              </Button>
              <Button
                variant="outlined"
                color="inherit"
                onClick={() => navigate('/register')}
              >
                إنشاء حساب
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1 }}>
        <Outlet />
      </Box>

      {/* Footer */}
      <Box
        component="footer"
        sx={{
          py: 3,
          px: 2,
          mt: 'auto',
          backgroundColor: theme.palette.grey[100],
          borderTop: 1,
          borderColor: 'divider',
        }}
      >
        <Container maxWidth="lg">
          <Typography variant="body2" color="text.secondary" align="center">
            © 2025 المؤتمر الدولي لطب الأسنان. جميع الحقوق محفوظة.
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default MainLayout;
