import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  bool _isEditing = false;
  
  // Mock user data
  final Map<String, dynamic> _userData = {
    'arabicName': 'د. أحمد محمد علي',
    'englishName': 'Dr. <PERSON>',
    'email': '<EMAIL>',
    'phone': '+967 777 123 456',
    'qualification': 'DOCTOR',
    'specialization': 'جراحة الفم والوجه والفكين',
    'university': 'جامعة صنعاء',
    'registrationDate': '2024-01-15',
    'subscriptionStatus': 'ACTIVE',
    'totalSessions': 12,
    'completedSessions': 8,
    'totalCredits': 18.0,
    'earnedCredits': 12.0,
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // Profile Header
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: const Color(AppColors.primaryRed),
            actions: [
              IconButton(
                onPressed: () => _showSettingsMenu(),
                icon: const Icon(
                  Icons.settings,
                  color: Color(AppColors.primaryWhite),
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(AppColors.primaryRed),
                      Color(AppColors.darkRed),
                      Color(AppColors.primaryBlack),
                    ],
                    stops: [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background Pattern
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: Container(
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/profile_bg.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    // Profile Content
                    Center(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 60),
                              
                              // Profile Avatar
                              Stack(
                                children: [
                                  Container(
                                    width: 120,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          const Color(AppColors.primaryWhite),
                                          const Color(AppColors.offWhite),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(60),
                                      border: Border.all(
                                        color: const Color(AppColors.primaryWhite),
                                        width: 4,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: const Color(AppColors.primaryBlack).withValues(alpha: 0.3),
                                          blurRadius: 20,
                                          offset: const Offset(0, 10),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.person,
                                      size: 60,
                                      color: Color(AppColors.primaryRed),
                                    ),
                                  ),
                                  
                                  // Status Badge
                                  Positioned(
                                    bottom: 0,
                                    right: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(AppSizes.paddingS),
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: const Color(AppColors.primaryWhite),
                                          width: 3,
                                        ),
                                      ),
                                      child: Icon(
                                        _getStatusIcon(),
                                        color: const Color(AppColors.primaryWhite),
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              
                              const SizedBox(height: AppSizes.paddingM),
                              
                              // Name
                              Text(
                                _userData['arabicName'],
                                style: const TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              
                              const SizedBox(height: AppSizes.paddingS),
                              
                              // Specialization
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingM,
                                  vertical: AppSizes.paddingS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                ),
                                child: Text(
                                  _userData['specialization'],
                                  style: TextStyle(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Profile Content
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  children: [
                    // Progress Cards
                    _buildProgressSection(),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Personal Information
                    _buildPersonalInfoSection(),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Academic Information
                    _buildAcademicInfoSection(),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Quick Actions
                    _buildQuickActionsSection(),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Achievements
                    _buildAchievementsSection(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final sessionProgress = _userData['completedSessions'] / _userData['totalSessions'];
    final creditsProgress = _userData['earnedCredits'] / _userData['totalCredits'];
    
    return Row(
      children: [
        Expanded(
          child: _buildProgressCard(
            title: 'الجلسات',
            progress: sessionProgress,
            current: _userData['completedSessions'],
            total: _userData['totalSessions'],
            icon: Icons.event,
            color: const Color(AppColors.primaryRed),
          ),
        ),
        const SizedBox(width: AppSizes.paddingM),
        Expanded(
          child: _buildProgressCard(
            title: 'ساعات التعليم',
            progress: creditsProgress,
            current: _userData['earnedCredits'],
            total: _userData['totalCredits'],
            icon: Icons.school,
            color: const Color(AppColors.primaryBlack),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressCard({
    required String title,
    required double progress,
    required dynamic current,
    required dynamic total,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Icon and Title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: AppSizes.iconM,
                ),
              ),
              const SizedBox(width: AppSizes.paddingS),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppColors.primaryBlack),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Progress Bar
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            borderRadius: BorderRadius.circular(AppSizes.radiusS),
          ),
          
          const SizedBox(height: AppSizes.paddingS),
          
          // Progress Text
          Text(
            '$current من $total',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return _buildInfoSection(
      title: 'المعلومات الشخصية',
      icon: Icons.person,
      children: [
        _buildInfoItem('الاسم العربي', _userData['arabicName']),
        _buildInfoItem('الاسم الإنجليزي', _userData['englishName']),
        _buildInfoItem('البريد الإلكتروني', _userData['email']),
        _buildInfoItem('رقم الهاتف', _userData['phone']),
      ],
    );
  }

  Widget _buildAcademicInfoSection() {
    return _buildInfoSection(
      title: 'المعلومات الأكاديمية',
      icon: Icons.school,
      children: [
        _buildInfoItem('المؤهل العلمي', _getQualificationText(_userData['qualification'])),
        _buildInfoItem('التخصص', _userData['specialization']),
        _buildInfoItem('الجامعة', _userData['university']),
        _buildInfoItem('تاريخ التسجيل', _userData['registrationDate']),
      ],
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingS),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(AppColors.primaryRed),
                      const Color(AppColors.darkRed),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                ),
                child: Icon(
                  icon,
                  color: const Color(AppColors.primaryWhite),
                  size: AppSizes.iconM,
                ),
              ),
              const SizedBox(width: AppSizes.paddingM),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppColors.primaryBlack),
                  ),
                ),
              ),
              IconButton(
                onPressed: () => setState(() => _isEditing = !_isEditing),
                icon: Icon(
                  _isEditing ? Icons.check : Icons.edit,
                  color: const Color(AppColors.primaryRed),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          // Section Content
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: const Color(AppColors.grey),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: AppSizes.paddingM),
          Expanded(
            child: _isEditing
                ? TextFormField(
                    initialValue: value,
                    decoration: InputDecoration(
                      isDense: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                    ),
                  )
                : Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(AppColors.primaryBlack),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    final actions = [
      {
        'icon': Icons.download,
        'title': 'تحميل الشهادة',
        'subtitle': 'شهادة الحضور',
        'color': const Color(AppColors.success),
        'onTap': () => _downloadCertificate(),
      },
      {
        'icon': Icons.qr_code,
        'title': 'رمز QR',
        'subtitle': 'للتحقق من الهوية',
        'color': const Color(AppColors.primaryRed),
        'onTap': () => _showQRCode(),
      },
      {
        'icon': Icons.payment,
        'title': 'سجل المدفوعات',
        'subtitle': 'عرض المدفوعات',
        'color': const Color(AppColors.info),
        'onTap': () => _showPaymentHistory(),
      },
      {
        'icon': Icons.support,
        'title': 'الدعم الفني',
        'subtitle': 'تواصل معنا',
        'color': const Color(AppColors.warning),
        'onTap': () => _contactSupport(),
      },
    ];

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppSizes.paddingM,
              mainAxisSpacing: AppSizes.paddingM,
              childAspectRatio: 1.2,
            ),
            itemCount: actions.length,
            itemBuilder: (context, index) {
              final action = actions[index];
              return InkWell(
                onTap: action['onTap'] as VoidCallback,
                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                child: Container(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    color: (action['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                    border: Border.all(
                      color: (action['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        action['icon'] as IconData,
                        color: action['color'] as Color,
                        size: AppSizes.iconL,
                      ),
                      const SizedBox(height: AppSizes.paddingS),
                      Text(
                        action['title'] as String,
                        style: TextStyle(
                          color: action['color'] as Color,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        action['subtitle'] as String,
                        style: TextStyle(
                          color: (action['color'] as Color).withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementsSection() {
    final achievements = [
      {
        'icon': Icons.star,
        'title': 'مشارك نشط',
        'description': 'حضر أكثر من 10 جلسات',
        'earned': true,
      },
      {
        'icon': Icons.school,
        'title': 'طالب متميز',
        'description': 'حصل على 15 ساعة تعليم مستمر',
        'earned': false,
      },
      {
        'icon': Icons.group,
        'title': 'عضو مجتمع',
        'description': 'شارك في المناقشات',
        'earned': true,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        boxShadow: [
          BoxShadow(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإنجازات',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          ...achievements.map((achievement) => Container(
            margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
            padding: const EdgeInsets.all(AppSizes.paddingM),
            decoration: BoxDecoration(
              color: achievement['earned'] as bool
                  ? const Color(AppColors.success).withValues(alpha: 0.1)
                  : const Color(AppColors.grey).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
              border: Border.all(
                color: achievement['earned'] as bool
                    ? const Color(AppColors.success).withValues(alpha: 0.3)
                    : const Color(AppColors.grey).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  achievement['icon'] as IconData,
                  color: achievement['earned'] as bool
                      ? const Color(AppColors.success)
                      : const Color(AppColors.grey),
                  size: AppSizes.iconL,
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        achievement['title'] as String,
                        style: TextStyle(
                          color: achievement['earned'] as bool
                              ? const Color(AppColors.success)
                              : const Color(AppColors.grey),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        achievement['description'] as String,
                        style: TextStyle(
                          color: achievement['earned'] as bool
                              ? const Color(AppColors.success).withValues(alpha: 0.8)
                              : const Color(AppColors.grey).withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (achievement['earned'] as bool)
                  const Icon(
                    Icons.check_circle,
                    color: Color(AppColors.success),
                  ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (_userData['subscriptionStatus']) {
      case 'ACTIVE':
        return const Color(AppColors.success);
      case 'PENDING':
        return const Color(AppColors.warning);
      case 'EXPIRED':
        return const Color(AppColors.error);
      default:
        return const Color(AppColors.grey);
    }
  }

  IconData _getStatusIcon() {
    switch (_userData['subscriptionStatus']) {
      case 'ACTIVE':
        return Icons.check;
      case 'PENDING':
        return Icons.schedule;
      case 'EXPIRED':
        return Icons.close;
      default:
        return Icons.help;
    }
  }

  String _getQualificationText(String qualification) {
    switch (qualification) {
      case 'DOCTOR':
        return 'طبيب';
      case 'STUDENT_YEAR_6':
        return 'طالب سنة سادسة';
      case 'STUDENT_YEAR_5':
        return 'طالب سنة خامسة';
      case 'STUDENT_YEAR_4':
        return 'طالب سنة رابعة';
      case 'STUDENT_YEAR_3':
        return 'طالب سنة ثالثة';
      case 'STUDENT_YEAR_2':
        return 'طالب سنة ثانية';
      case 'STUDENT_YEAR_1':
        return 'طالب سنة أولى';
      default:
        return qualification;
    }
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('تغيير اللغة'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('إعدادات الإشعارات'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('الخصوصية والأمان'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('تسجيل الخروج'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _downloadCertificate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تحميل الشهادة...'),
        backgroundColor: Color(AppColors.success),
      ),
    );
  }

  void _showQRCode() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رمز QR الخاص بك'),
        content: Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: const Color(AppColors.lightGrey),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Center(
            child: Icon(
              Icons.qr_code,
              size: 100,
              color: Color(AppColors.primaryBlack),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showPaymentHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض سجل المدفوعات...'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _contactSupport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('فتح نافذة الدعم الفني...'),
        backgroundColor: Color(AppColors.warning),
      ),
    );
  }
}
