const { body, param, query } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const { handleValidationErrors, pagination } = require('../middleware/validation');
const contentService = require('../services/contentService');
const logger = require('../utils/logger');

// Validation rules
const sessionValidation = [
  body('titleAr').notEmpty().withMessage('العنوان العربي مطلوب'),
  body('titleEn').notEmpty().withMessage('العنوان الإنجليزي مطلوب'),
  body('descriptionAr').notEmpty().withMessage('الوصف العربي مطلوب'),
  body('descriptionEn').notEmpty().withMessage('الوصف الإنجليزي مطلوب'),
  body('startTime').isISO8601().withMessage('وقت البداية غير صحيح'),
  body('endTime').isISO8601().withMessage('وقت النهاية غير صحيح'),
  body('type').isIn(['LECTURE', 'WORKSHOP', 'PRESENTATION', 'POSTER_SESSION', 'PANEL_DISCUSSION']).withMessage('نوع الجلسة غير صحيح'),
  body('cmeHours').optional().isFloat({ min: 0 }).withMessage('ساعات التعليم الطبي المستمر يجب أن تكون رقم موجب'),
  body('maxAttendees').optional().isInt({ min: 1 }).withMessage('الحد الأقصى للحضور يجب أن يكون رقم موجب'),
  body('speakerId').notEmpty().withMessage('معرف المتحدث مطلوب'),
  body('location').optional().isString(),
  handleValidationErrors
];

const speakerValidation = [
  body('nameAr').notEmpty().withMessage('الاسم العربي مطلوب'),
  body('nameEn').notEmpty().withMessage('الاسم الإنجليزي مطلوب'),
  body('bio').notEmpty().withMessage('السيرة الذاتية مطلوبة'),
  body('bioEn').optional().isString(),
  body('title').optional().isString(),
  body('organization').optional().isString(),
  body('email').optional().isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('linkedin').optional().isURL().withMessage('رابط LinkedIn غير صحيح'),
  body('twitter').optional().isString(),
  body('website').optional().isURL().withMessage('رابط الموقع غير صحيح'),
  handleValidationErrors
];

const announcementValidation = [
  body('titleAr').notEmpty().withMessage('العنوان العربي مطلوب'),
  body('titleEn').notEmpty().withMessage('العنوان الإنجليزي مطلوب'),
  body('contentAr').notEmpty().withMessage('المحتوى العربي مطلوب'),
  body('contentEn').notEmpty().withMessage('المحتوى الإنجليزي مطلوب'),
  body('type').optional().isIn(['GENERAL', 'URGENT', 'SYSTEM', 'EVENT']).withMessage('نوع الإعلان غير صحيح'),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).withMessage('الأولوية غير صحيحة'),
  body('publishAt').optional().isISO8601().withMessage('تاريخ النشر غير صحيح'),
  body('expiresAt').optional().isISO8601().withMessage('تاريخ انتهاء الصلاحية غير صحيح'),
  handleValidationErrors
];

// Session Management
const createSession = [
  sessionValidation,
  asyncHandler(async (req, res) => {
    const sessionData = req.body;
    const createdBy = req.user.id;

    const session = await contentService.createSession(sessionData);

    logger.logAdminAction(createdBy, 'SESSION_CREATED', { 
      sessionId: session.id, 
      title: session.titleEn 
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الجلسة بنجاح',
      data: { session }
    });
  })
];

const updateSession = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  sessionValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user.id;

    const session = await contentService.updateSession(id, updateData);

    logger.logAdminAction(updatedBy, 'SESSION_UPDATED', { 
      sessionId: id, 
      updates: Object.keys(updateData) 
    });

    res.json({
      success: true,
      message: 'تم تحديث الجلسة بنجاح',
      data: { session }
    });
  })
];

const deleteSession = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  handleValidationErrors,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const deletedBy = req.user.id;

    await contentService.deleteSession(id);

    logger.logAdminAction(deletedBy, 'SESSION_DELETED', { sessionId: id });

    res.json({
      success: true,
      message: 'تم حذف الجلسة بنجاح'
    });
  })
];

// Speaker Management
const createSpeaker = [
  speakerValidation,
  asyncHandler(async (req, res) => {
    const speakerData = req.body;
    const createdBy = req.user.id;

    const speaker = await contentService.createSpeaker(speakerData);

    logger.logAdminAction(createdBy, 'SPEAKER_CREATED', { 
      speakerId: speaker.id, 
      name: speaker.nameEn 
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء المتحدث بنجاح',
      data: { speaker }
    });
  })
];

const updateSpeaker = [
  param('id').notEmpty().withMessage('معرف المتحدث مطلوب'),
  speakerValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user.id;

    const speaker = await contentService.updateSpeaker(id, updateData);

    logger.logAdminAction(updatedBy, 'SPEAKER_UPDATED', { 
      speakerId: id, 
      updates: Object.keys(updateData) 
    });

    res.json({
      success: true,
      message: 'تم تحديث المتحدث بنجاح',
      data: { speaker }
    });
  })
];

const deleteSpeaker = [
  param('id').notEmpty().withMessage('معرف المتحدث مطلوب'),
  handleValidationErrors,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const deletedBy = req.user.id;

    await contentService.deleteSpeaker(id);

    logger.logAdminAction(deletedBy, 'SPEAKER_DELETED', { speakerId: id });

    res.json({
      success: true,
      message: 'تم حذف المتحدث بنجاح'
    });
  })
];

// Announcement Management
const getAnnouncements = [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('type').optional().isString(),
  query('isActive').optional().isBoolean(),
  handleValidationErrors,
  pagination,
  asyncHandler(async (req, res) => {
    const filters = {
      type: req.query.type,
      isActive: req.query.isActive
    };

    const result = await contentService.getAnnouncements(filters, req.pagination);

    logger.logAdminAction(req.user?.id, 'ANNOUNCEMENTS_VIEWED', { filters });

    req.paginationResult = result.pagination;

    res.json({
      success: true,
      data: {
        announcements: result.announcements,
        filters
      }
    });
  })
];

const createAnnouncement = [
  announcementValidation,
  asyncHandler(async (req, res) => {
    const announcementData = req.body;
    const createdBy = req.user.id;

    const announcement = await contentService.createAnnouncement(announcementData);

    logger.logAdminAction(createdBy, 'ANNOUNCEMENT_CREATED', { 
      announcementId: announcement.id, 
      title: announcement.titleEn 
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الإعلان بنجاح',
      data: { announcement }
    });
  })
];

const updateAnnouncement = [
  param('id').notEmpty().withMessage('معرف الإعلان مطلوب'),
  announcementValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;
    const updatedBy = req.user.id;

    const announcement = await contentService.updateAnnouncement(id, updateData);

    logger.logAdminAction(updatedBy, 'ANNOUNCEMENT_UPDATED', { 
      announcementId: id, 
      updates: Object.keys(updateData) 
    });

    res.json({
      success: true,
      message: 'تم تحديث الإعلان بنجاح',
      data: { announcement }
    });
  })
];

const deleteAnnouncement = [
  param('id').notEmpty().withMessage('معرف الإعلان مطلوب'),
  handleValidationErrors,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const deletedBy = req.user.id;

    await contentService.deleteAnnouncement(id);

    logger.logAdminAction(deletedBy, 'ANNOUNCEMENT_DELETED', { announcementId: id });

    res.json({
      success: true,
      message: 'تم حذف الإعلان بنجاح'
    });
  })
];

// Content Statistics
const getContentStats = asyncHandler(async (req, res) => {
  const stats = await contentService.getContentStats();

  logger.logAdminAction(req.user?.id, 'CONTENT_STATS_VIEWED');

  res.json({
    success: true,
    data: {
      statistics: stats,
      generatedAt: new Date().toISOString()
    }
  });
});

module.exports = {
  // Sessions
  createSession,
  updateSession,
  deleteSession,
  
  // Speakers
  createSpeaker,
  updateSpeaker,
  deleteSpeaker,
  
  // Announcements
  getAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  
  // Statistics
  getContentStats
};
