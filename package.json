{"name": "idec2025-backend", "version": "1.0.0", "description": "Backend API for IDEC 2025 Dental Conference Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "node prisma/seed.js", "db:studio": "npx prisma studio", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "babel src -d dist", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js"}, "keywords": ["dental", "conference", "management", "api", "nodejs", "express", "postgresql"], "author": "IDEC 2025 Development Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "node-cache": "^5.1.2", "redis": "^4.6.11", "nodemailer": "^6.9.7", "axios": "^1.6.2", "qrcode": "^1.5.3", "pdf-lib": "^1.17.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "dotenv": "^16.3.1", "joi": "^17.11.0", "socket.io": "^4.7.4", "web-push": "^3.6.6", "cron": "^3.1.6", "archiver": "^6.0.1"}, "devDependencies": {"prisma": "^5.7.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/cli": "^7.23.4", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.js": ["eslint --fix", "git add"]}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/server.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}