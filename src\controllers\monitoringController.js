const { asyncHandler } = require('../middleware/errorHandler');
const { systemMonitor } = require('../utils/monitoring');
const advancedMonitor = require('../utils/advancedMonitoring');
const subscriptionService = require('../services/subscriptionService');
const paymentService = require('../services/paymentService');
const userService = require('../services/userService');
const logger = require('../utils/logger');

// Get basic system metrics
const getSystemMetrics = asyncHandler(async (req, res) => {
  const metrics = systemMonitor.getMetrics();
  
  res.json({
    success: true,
    data: {
      metrics,
      timestamp: new Date().toISOString()
    }
  });
});

// Get advanced monitoring metrics
const getAdvancedMetrics = asyncHandler(async (req, res) => {
  const metrics = advancedMonitor.getDetailedMetrics();
  
  logger.logAdminAction(req.user?.id, 'ADVANCED_METRICS_VIEWED');
  
  res.json({
    success: true,
    data: {
      metrics,
      timestamp: new Date().toISOString()
    }
  });
});

// Get system health report
const getHealthReport = asyncHandler(async (req, res) => {
  const healthReport = advancedMonitor.getHealthReport();
  
  logger.logAdminAction(req.user?.id, 'HEALTH_REPORT_VIEWED');
  
  res.json({
    success: true,
    data: {
      health: healthReport,
      timestamp: new Date().toISOString()
    }
  });
});

// Get performance insights
const getPerformanceInsights = asyncHandler(async (req, res) => {
  const insights = advancedMonitor.getPerformanceInsights();
  
  logger.logAdminAction(req.user?.id, 'PERFORMANCE_INSIGHTS_VIEWED');
  
  res.json({
    success: true,
    data: {
      insights,
      timestamp: new Date().toISOString()
    }
  });
});

// Get comprehensive dashboard data
const getDashboardData = asyncHandler(async (req, res) => {
  try {
    // Get all statistics in parallel
    const [
      subscriptionStats,
      paymentStats,
      systemHealth,
      performanceInsights
    ] = await Promise.all([
      subscriptionService.getSubscriptionStats(),
      paymentService.getPaymentStats(),
      advancedMonitor.getHealthReport(),
      advancedMonitor.getPerformanceInsights()
    ]);

    // Get recent activity metrics
    const recentMetrics = advancedMonitor.getMetrics();
    
    const dashboardData = {
      overview: {
        totalUsers: subscriptionStats.total,
        activeSubscriptions: subscriptionStats.byStatus.active,
        totalRevenue: paymentStats.totalRevenue,
        systemHealth: systemHealth.status,
        uptime: Math.round(process.uptime() / 3600 * 100) / 100 // hours
      },
      subscriptions: {
        total: subscriptionStats.total,
        byStatus: subscriptionStats.byStatus,
        byQualification: subscriptionStats.byQualification
      },
      payments: {
        totalRevenue: paymentStats.totalRevenue,
        totalPayments: paymentStats.totalPayments,
        byStatus: paymentStats.byStatus
      },
      system: {
        health: systemHealth,
        performance: performanceInsights,
        metrics: {
          cpu: recentMetrics.system.cpuUsage,
          memory: recentMetrics.system.memoryUsage,
          requests: recentMetrics.requests.total,
          errors: recentMetrics.requests.failed,
          responseTime: recentMetrics.requests.averageResponseTime
        }
      },
      alerts: recentMetrics.alerts || [],
      topEndpoints: performanceInsights.topEndpoints,
      recentActivity: {
        requests: recentMetrics.requests.total,
        errors: recentMetrics.requests.failed,
        dbQueries: recentMetrics.database.totalQueries,
        cacheCommands: recentMetrics.redis.commands
      }
    };

    logger.logAdminAction(req.user?.id, 'DASHBOARD_DATA_VIEWED');

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting dashboard data:', error);
    throw error;
  }
});

// Get real-time statistics
const getRealTimeStats = asyncHandler(async (req, res) => {
  const metrics = advancedMonitor.getMetrics();
  
  const realTimeStats = {
    system: {
      cpu: metrics.system.cpuUsage,
      memory: metrics.system.memoryUsage,
      uptime: process.uptime()
    },
    requests: {
      total: metrics.requests.total,
      successful: metrics.requests.successful,
      failed: metrics.requests.failed,
      averageResponseTime: metrics.requests.averageResponseTime,
      requestsPerMinute: Math.round(metrics.requests.total / (process.uptime() / 60))
    },
    database: {
      totalQueries: metrics.database.totalQueries,
      slowQueries: metrics.database.slowQueries,
      errors: metrics.database.errors,
      averageQueryTime: metrics.database.averageQueryTime
    },
    cache: {
      commands: metrics.redis.commands,
      hitRate: Math.round(metrics.redis.hitRate * 100),
      errors: metrics.redis.errors
    },
    alerts: metrics.alerts.length,
    timestamp: new Date().toISOString()
  };

  res.json({
    success: true,
    data: realTimeStats
  });
});

// Get error logs
const getErrorLogs = asyncHandler(async (req, res) => {
  const { page = 1, limit = 50 } = req.query;
  
  // TODO: Implement error log retrieval from log files
  // For now, return recent errors from monitoring
  const metrics = advancedMonitor.getDetailedMetrics();
  const errors = metrics.errors || [];
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedErrors = errors.slice(startIndex, endIndex);
  
  logger.logAdminAction(req.user?.id, 'ERROR_LOGS_VIEWED', { page, limit });
  
  res.json({
    success: true,
    data: {
      errors: paginatedErrors,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: errors.length,
        pages: Math.ceil(errors.length / limit)
      }
    }
  });
});

// Get API usage statistics
const getApiUsageStats = asyncHandler(async (req, res) => {
  const { timeframe = '24h' } = req.query;
  
  const metrics = advancedMonitor.getDetailedMetrics();
  
  const apiStats = {
    timeframe,
    totalRequests: metrics.requests.total,
    successfulRequests: metrics.requests.successful,
    failedRequests: metrics.requests.failed,
    averageResponseTime: metrics.requests.averageResponseTime,
    endpoints: metrics.detailed.endpoints,
    methods: metrics.detailed.methods,
    statusCodes: metrics.detailed.statusCodes,
    userAgents: metrics.detailed.userAgents,
    topSlowEndpoints: metrics.detailed.topSlowEndpoints,
    topErrorEndpoints: metrics.detailed.topErrorEndpoints
  };
  
  logger.logAdminAction(req.user?.id, 'API_USAGE_STATS_VIEWED', { timeframe });
  
  res.json({
    success: true,
    data: apiStats
  });
});

// Reset monitoring metrics (admin only)
const resetMetrics = asyncHandler(async (req, res) => {
  advancedMonitor.reset();
  
  logger.logAdminAction(req.user.id, 'MONITORING_METRICS_RESET');
  
  res.json({
    success: true,
    message: 'تم إعادة تعيين إحصائيات المراقبة بنجاح',
    timestamp: new Date().toISOString()
  });
});

// Export metrics data
const exportMetrics = asyncHandler(async (req, res) => {
  const { format = 'json', timeframe = '24h' } = req.query;
  
  const metrics = advancedMonitor.getDetailedMetrics();
  
  logger.logAdminAction(req.user.id, 'METRICS_EXPORTED', { format, timeframe });
  
  if (format === 'csv') {
    // TODO: Implement CSV export
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=metrics.csv');
    res.send('CSV export not implemented yet');
  } else {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=metrics.json');
    res.json({
      exportedAt: new Date().toISOString(),
      timeframe,
      metrics
    });
  }
});

// Get system alerts
const getSystemAlerts = asyncHandler(async (req, res) => {
  const { severity, limit = 50 } = req.query;
  
  const metrics = advancedMonitor.getMetrics();
  let alerts = metrics.alerts || [];
  
  // Filter by severity if specified
  if (severity) {
    alerts = alerts.filter(alert => alert.level === severity);
  }
  
  // Limit results
  alerts = alerts.slice(0, parseInt(limit));
  
  logger.logAdminAction(req.user?.id, 'SYSTEM_ALERTS_VIEWED', { severity, limit });
  
  res.json({
    success: true,
    data: {
      alerts,
      total: alerts.length,
      filters: { severity, limit }
    }
  });
});

module.exports = {
  getSystemMetrics,
  getAdvancedMetrics,
  getHealthReport,
  getPerformanceInsights,
  getDashboardData,
  getRealTimeStats,
  getErrorLogs,
  getApiUsageStats,
  resetMetrics,
  exportMetrics,
  getSystemAlerts
};
