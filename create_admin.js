const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    console.log('Creating admin user...');
    
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingAdmin) {
      console.log('Admin user already exists!');
      
      // Update password
      const hashedPassword = await bcrypt.hash('Admin123!@#', 12);
      await prisma.user.update({
        where: { id: existingAdmin.id },
        data: {
          passwordHash: hashedPassword,
          status: 'ACTIVE'
        }
      });
      
      console.log('Admin password updated successfully!');
      return;
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash('Admin123!@#', 12);
    
    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        arabicName: 'المدير العام',
        englishName: 'System Administrator',
        phone: '+966501234567',
        status: 'ACTIVE',
        emailVerified: true,
        qualification: 'MASTER',
        specialization: 'إدارة النظم',
        workPlace: 'IDEC 2025',
        city: 'الرياض',
        country: 'السعودية'
      }
    });
    
    console.log('Admin user created:', adminUser.email);
    
    // Get super admin role
    const superAdminRole = await prisma.role.findFirst({
      where: { name: 'super_admin' }
    });
    
    if (!superAdminRole) {
      console.log('Creating super admin role...');
      const newRole = await prisma.role.create({
        data: {
          name: 'super_admin',
          description: 'مدير النظام - صلاحية كاملة',
          permissions: ['*']
        }
      });
      
      // Assign role to admin
      await prisma.userRole.create({
        data: {
          userId: adminUser.id,
          roleId: newRole.id
        }
      });
    } else {
      // Assign existing role to admin
      await prisma.userRole.create({
        data: {
          userId: adminUser.id,
          roleId: superAdminRole.id
        }
      });
    }
    
    console.log('Super admin role assigned successfully!');
    
    // Create admission committee user
    const admissionUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        arabicName: 'لجنة القبول',
        englishName: 'Admission Committee',
        phone: '+966501234568',
        status: 'ACTIVE',
        emailVerified: true,
        qualification: 'MASTER',
        specialization: 'إدارة القبول',
        workPlace: 'IDEC 2025',
        city: 'الرياض',
        country: 'السعودية'
      }
    });
    
    // Get or create admission committee role
    let admissionRole = await prisma.role.findFirst({
      where: { name: 'admission_committee' }
    });
    
    if (!admissionRole) {
      admissionRole = await prisma.role.create({
        data: {
          name: 'admission_committee',
          description: 'لجنة القبول - مراجعة الطلبات والوثائق',
          permissions: ['applications:*', 'users:read', 'users:update', 'reports:read']
        }
      });
    }
    
    await prisma.userRole.create({
      data: {
        userId: admissionUser.id,
        roleId: admissionRole.id
      }
    });
    
    console.log('Admission committee user created:', admissionUser.email);
    
    // Create accountant user
    const accountantUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        passwordHash: hashedPassword,
        arabicName: 'المحاسب المالي',
        englishName: 'Financial Accountant',
        phone: '+************',
        status: 'ACTIVE',
        emailVerified: true,
        qualification: 'BACHELOR',
        specialization: 'المحاسبة المالية',
        workPlace: 'IDEC 2025',
        city: 'الرياض',
        country: 'السعودية'
      }
    });
    
    // Get or create accountant role
    let accountantRole = await prisma.role.findFirst({
      where: { name: 'accountant' }
    });
    
    if (!accountantRole) {
      accountantRole = await prisma.role.create({
        data: {
          name: 'accountant',
          description: 'المحاسب - إدارة المالية والمدفوعات',
          permissions: ['payments:*', 'transactions:*', 'financial_reports:*', 'users:read']
        }
      });
    }
    
    await prisma.userRole.create({
      data: {
        userId: accountantUser.id,
        roleId: accountantRole.id
      }
    });
    
    console.log('Accountant user created:', accountantUser.email);
    
    console.log('\n=== Admin Users Created Successfully ===');
    console.log('Super Admin:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: Admin123!@#');
    console.log('\nAdmission Committee:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: Admin123!@#');
    console.log('\nAccountant:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: Admin123!@#');
    console.log('\nYou can now login to the admin dashboard!');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
