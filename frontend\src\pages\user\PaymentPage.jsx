import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
} from '@mui/material';
import {
  Payment,
  CreditCard,
  AccountBalance,
  Phone,
  Receipt,
  Download,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { userAPI, subscriptionAPI } from '../../services/api';

const PaymentPage = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const queryClient = useQueryClient();

  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('credit_card');
  const [paymentDetails, setPaymentDetails] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  });

  // Fetch subscription
  const { data: subscriptionData, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['userSubscription'],
    queryFn: subscriptionAPI.getMySubscription,
  });

  // Fetch payment history
  const { data: paymentsData, isLoading: paymentsLoading } = useQuery({
    queryKey: ['paymentHistory'],
    queryFn: () => userAPI.getPaymentHistory({ page: 1, limit: 10 }),
  });

  const subscription = subscriptionData?.data?.subscription;
  const payments = paymentsData?.data?.payments || [];

  // Payment methods
  const paymentMethods = [
    {
      value: 'credit_card',
      label: t('creditCard', 'بطاقة ائتمانية'),
      icon: <CreditCard />,
    },
    {
      value: 'bank_transfer',
      label: t('bankTransfer', 'تحويل بنكي'),
      icon: <AccountBalance />,
    },
    {
      value: 'stc_pay',
      label: 'STC Pay',
      icon: <Phone />,
    },
  ];

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'FAILED':
        return 'error';
      default:
        return 'default';
    }
  };

  const handlePayment = () => {
    // Here you would integrate with actual payment gateway
    console.log('Processing payment...', { paymentMethod, paymentDetails });
    setPaymentDialogOpen(false);
  };

  const canMakePayment = subscription && subscription.status === 'APPROVED_PENDING_PAYMENT';

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('payment')}
      </Typography>

      {/* Current Subscription Payment */}
      {subscription && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('subscriptionPayment', 'دفع الاشتراك')}
            </Typography>

            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="body1" gutterBottom>
                    {t('subscriptionAmount', 'مبلغ الاشتراك')}: 
                    <strong> {subscription.amount} {subscription.currency}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {t('qualification')}: {t(subscription.qualification)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {t('pricingPeriod', 'فترة التسعير')}: {t(subscription.pricingPeriod)}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box textAlign="center">
                  <Chip
                    label={t(subscription.status)}
                    color={subscription.status === 'ACTIVE' ? 'success' : 'warning'}
                    sx={{ mb: 2 }}
                  />
                  
                  {canMakePayment ? (
                    <Box>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={<Payment />}
                        onClick={() => setPaymentDialogOpen(true)}
                        sx={{ mb: 1 }}
                      >
                        {t('payNow', 'ادفع الآن')}
                      </Button>
                      <Typography variant="body2" color="text.secondary">
                        {t('securePayment', 'دفع آمن ومشفر')}
                      </Typography>
                    </Box>
                  ) : subscription.status === 'ACTIVE' ? (
                    <Alert severity="success">
                      {t('paymentCompleted', 'تم الدفع بنجاح')}
                    </Alert>
                  ) : (
                    <Alert severity="info">
                      {t('paymentNotAvailable', 'الدفع غير متاح حالياً')}
                    </Alert>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Payment History */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('paymentHistory')}
          </Typography>

          {paymentsLoading ? (
            <Box display="flex" justifyContent="center" py={3}>
              <CircularProgress />
            </Box>
          ) : payments.length > 0 ? (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('date', 'التاريخ')}</TableCell>
                    <TableCell>{t('amount')}</TableCell>
                    <TableCell>{t('method', 'الطريقة')}</TableCell>
                    <TableCell>{t('status')}</TableCell>
                    <TableCell>{t('reference', 'المرجع')}</TableCell>
                    <TableCell>{t('actions', 'الإجراءات')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        {new Date(payment.createdAt).toLocaleDateString('ar-SA')}
                      </TableCell>
                      <TableCell>
                        {payment.amount} {payment.currency}
                      </TableCell>
                      <TableCell>
                        {t(payment.method)}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={t(payment.status)}
                          color={getStatusColor(payment.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {payment.reference || '-'}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          startIcon={<Receipt />}
                          onClick={() => window.open(`/api/v1/payments/${payment.id}/receipt`, '_blank')}
                        >
                          {t('receipt', 'الإيصال')}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Alert severity="info">
              {t('noPayments', 'لا توجد مدفوعات')}
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Payment Dialog */}
      <Dialog open={paymentDialogOpen} onClose={() => setPaymentDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <Payment />
            {t('completePayment', 'إكمال الدفع')}
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {/* Payment Summary */}
            <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
              <Typography variant="h6" gutterBottom>
                {t('paymentSummary', 'ملخص الدفع')}
              </Typography>
              <Box display="flex" justifyContent="space-between">
                <Typography>{t('subscriptionFee', 'رسوم الاشتراك')}</Typography>
                <Typography fontWeight="bold">
                  {subscription?.amount} {subscription?.currency}
                </Typography>
              </Box>
            </Paper>

            {/* Payment Method Selection */}
            <FormControl component="fieldset" sx={{ mb: 3 }}>
              <FormLabel component="legend">{t('selectPaymentMethod', 'اختر طريقة الدفع')}</FormLabel>
              <RadioGroup
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
              >
                {paymentMethods.map((method) => (
                  <FormControlLabel
                    key={method.value}
                    value={method.value}
                    control={<Radio />}
                    label={
                      <Box display="flex" alignItems="center" gap={1}>
                        {method.icon}
                        {method.label}
                      </Box>
                    }
                  />
                ))}
              </RadioGroup>
            </FormControl>

            {/* Credit Card Form */}
            {paymentMethod === 'credit_card' && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={t('cardNumber', 'رقم البطاقة')}
                    value={paymentDetails.cardNumber}
                    onChange={(e) => setPaymentDetails(prev => ({ ...prev, cardNumber: e.target.value }))}
                    placeholder="1234 5678 9012 3456"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={t('cardholderName', 'اسم حامل البطاقة')}
                    value={paymentDetails.cardholderName}
                    onChange={(e) => setPaymentDetails(prev => ({ ...prev, cardholderName: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label={t('expiryDate', 'تاريخ الانتهاء')}
                    value={paymentDetails.expiryDate}
                    onChange={(e) => setPaymentDetails(prev => ({ ...prev, expiryDate: e.target.value }))}
                    placeholder="MM/YY"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="CVV"
                    value={paymentDetails.cvv}
                    onChange={(e) => setPaymentDetails(prev => ({ ...prev, cvv: e.target.value }))}
                    placeholder="123"
                  />
                </Grid>
              </Grid>
            )}

            {/* Bank Transfer Info */}
            {paymentMethod === 'bank_transfer' && (
              <Alert severity="info">
                <Typography variant="body2">
                  {t('bankTransferInfo', 'سيتم توجيهك لصفحة التحويل البنكي لإكمال العملية')}
                </Typography>
              </Alert>
            )}

            {/* STC Pay Info */}
            {paymentMethod === 'stc_pay' && (
              <Alert severity="info">
                <Typography variant="body2">
                  {t('stcPayInfo', 'سيتم توجيهك لتطبيق STC Pay لإكمال العملية')}
                </Typography>
              </Alert>
            )}
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setPaymentDialogOpen(false)}>
            {t('cancel')}
          </Button>
          <Button
            onClick={handlePayment}
            variant="contained"
            startIcon={<Payment />}
          >
            {t('pay', 'ادفع')} {subscription?.amount} {subscription?.currency}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PaymentPage;
