const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedExhibition() {
  console.log('🏢 Seeding exhibition data...');

  // Create exhibitors
  const exhibitors = [
    {
      companyName: 'DentalTech Solutions',
      companyNameAr: 'حلول تقنية الأسنان',
      companyNameEn: 'DentalTech Solutions',
      description: 'Leading provider of advanced dental equipment and technology solutions.',
      descriptionAr: 'مزود رائد لمعدات وحلول تقنية الأسنان المتقدمة.',
      descriptionEn: 'Leading provider of advanced dental equipment and technology solutions.',
      website: 'https://dentaltech.com',
      email: '<EMAIL>',
      phone: '******-0101',
      country: 'USA',
      category: 'DENTAL_EQUIPMENT',
      isPremium: true
    },
    {
      companyName: 'OralCare Materials',
      companyNameAr: 'مواد العناية بالفم',
      companyNameEn: 'OralCare Materials',
      description: 'High-quality dental materials and consumables for modern dentistry.',
      descriptionAr: 'مواد أسنان عالية الجودة ومستهلكات لطب الأسنان الحديث.',
      descriptionEn: 'High-quality dental materials and consumables for modern dentistry.',
      website: 'https://oralcare.com',
      email: '<EMAIL>',
      phone: '******-0102',
      country: 'Germany',
      category: 'DENTAL_MATERIALS',
      isPremium: true
    },
    {
      companyName: 'SmilePharma',
      companyNameAr: 'سمايل فارما',
      companyNameEn: 'SmilePharma',
      description: 'Pharmaceutical solutions for dental and oral health.',
      descriptionAr: 'حلول صيدلانية لصحة الأسنان والفم.',
      descriptionEn: 'Pharmaceutical solutions for dental and oral health.',
      website: 'https://smilepharma.com',
      email: '<EMAIL>',
      phone: '******-0103',
      country: 'Switzerland',
      category: 'PHARMACEUTICALS',
      isPremium: false
    },
    {
      companyName: 'Digital Dental Systems',
      companyNameAr: 'أنظمة الأسنان الرقمية',
      companyNameEn: 'Digital Dental Systems',
      description: 'Cutting-edge digital solutions for modern dental practices.',
      descriptionAr: 'حلول رقمية متطورة لعيادات الأسنان الحديثة.',
      descriptionEn: 'Cutting-edge digital solutions for modern dental practices.',
      website: 'https://digitaldental.com',
      email: '<EMAIL>',
      phone: '******-0104',
      country: 'Japan',
      category: 'TECHNOLOGY',
      isPremium: true
    },
    {
      companyName: 'Dental Education Hub',
      companyNameAr: 'مركز تعليم الأسنان',
      companyNameEn: 'Dental Education Hub',
      description: 'Educational resources and training programs for dental professionals.',
      descriptionAr: 'موارد تعليمية وبرامج تدريبية لمهنيي الأسنان.',
      descriptionEn: 'Educational resources and training programs for dental professionals.',
      website: 'https://dentaledu.com',
      email: '<EMAIL>',
      phone: '******-0105',
      country: 'UK',
      category: 'EDUCATION',
      isPremium: false
    }
  ];

  const createdExhibitors = [];
  for (const exhibitorData of exhibitors) {
    try {
      const exhibitor = await prisma.exhibitor.create({
        data: exhibitorData
      });
      createdExhibitors.push(exhibitor);
      console.log(`✅ Created exhibitor: ${exhibitor.companyNameEn}`);
    } catch (error) {
      console.error(`❌ Error creating exhibitor ${exhibitorData.companyNameEn}:`, error.message);
    }
  }

  // Create booths for exhibitors
  const booths = [
    { exhibitorId: createdExhibitors[0]?.id, boothNumber: 'A01', hallName: 'Hall A', size: '6x3' },
    { exhibitorId: createdExhibitors[1]?.id, boothNumber: 'A02', hallName: 'Hall A', size: '6x3' },
    { exhibitorId: createdExhibitors[2]?.id, boothNumber: 'B01', hallName: 'Hall B', size: '3x3' },
    { exhibitorId: createdExhibitors[3]?.id, boothNumber: 'A03', hallName: 'Hall A', size: '9x3' },
    { exhibitorId: createdExhibitors[4]?.id, boothNumber: 'C01', hallName: 'Hall C', size: '3x3' }
  ];

  for (const boothData of booths) {
    if (boothData.exhibitorId) {
      try {
        await prisma.booth.create({
          data: boothData
        });
        console.log(`✅ Created booth: ${boothData.boothNumber}`);
      } catch (error) {
        console.error(`❌ Error creating booth ${boothData.boothNumber}:`, error.message);
      }
    }
  }

  // Create products
  const products = [
    {
      exhibitorId: createdExhibitors[0]?.id,
      name: 'Advanced X-Ray System',
      nameAr: 'نظام الأشعة السينية المتقدم',
      nameEn: 'Advanced X-Ray System',
      description: 'High-resolution digital X-ray system with AI-powered diagnostics.',
      descriptionAr: 'نظام أشعة سينية رقمي عالي الدقة مع تشخيص مدعوم بالذكاء الاصطناعي.',
      descriptionEn: 'High-resolution digital X-ray system with AI-powered diagnostics.',
      category: 'Imaging Equipment',
      price: 25000,
      currency: 'USD',
      isFeatured: true
    },
    {
      exhibitorId: createdExhibitors[1]?.id,
      name: 'Premium Composite Resin',
      nameAr: 'راتنج مركب ممتاز',
      nameEn: 'Premium Composite Resin',
      description: 'High-strength composite resin for aesthetic restorations.',
      descriptionAr: 'راتنج مركب عالي القوة للترميمات التجميلية.',
      descriptionEn: 'High-strength composite resin for aesthetic restorations.',
      category: 'Restorative Materials',
      price: 150,
      currency: 'USD',
      isFeatured: true
    },
    {
      exhibitorId: createdExhibitors[2]?.id,
      name: 'Oral Health Supplement',
      nameAr: 'مكمل صحة الفم',
      nameEn: 'Oral Health Supplement',
      description: 'Natural supplement for maintaining optimal oral health.',
      descriptionAr: 'مكمل طبيعي للحفاظ على صحة الفم المثلى.',
      descriptionEn: 'Natural supplement for maintaining optimal oral health.',
      category: 'Supplements',
      price: 45,
      currency: 'USD',
      isFeatured: false
    }
  ];

  for (const productData of products) {
    if (productData.exhibitorId) {
      try {
        await prisma.product.create({
          data: productData
        });
        console.log(`✅ Created product: ${productData.nameEn}`);
      } catch (error) {
        console.error(`❌ Error creating product ${productData.nameEn}:`, error.message);
      }
    }
  }

  // Create special offers
  const offers = [
    {
      exhibitorId: createdExhibitors[0]?.id,
      title: 'Conference Special - 20% Off X-Ray Systems',
      titleAr: 'عرض خاص للمؤتمر - خصم 20% على أنظمة الأشعة السينية',
      titleEn: 'Conference Special - 20% Off X-Ray Systems',
      description: 'Limited time offer for IDEC 2025 attendees. Get 20% off our advanced X-ray systems.',
      descriptionAr: 'عرض محدود الوقت لحضور IDEC 2025. احصل على خصم 20% على أنظمة الأشعة السينية المتقدمة.',
      descriptionEn: 'Limited time offer for IDEC 2025 attendees. Get 20% off our advanced X-ray systems.',
      discount: 20,
      validFrom: new Date('2025-03-01'),
      validTo: new Date('2025-03-31'),
      terms: 'Valid for IDEC 2025 attendees only. Cannot be combined with other offers.'
    },
    {
      exhibitorId: createdExhibitors[1]?.id,
      title: 'Buy 2 Get 1 Free - Composite Resins',
      titleAr: 'اشتري 2 واحصل على 1 مجاناً - الراتنجات المركبة',
      titleEn: 'Buy 2 Get 1 Free - Composite Resins',
      description: 'Special promotion on our premium composite resin collection.',
      descriptionAr: 'عرض ترويجي خاص على مجموعة الراتنجات المركبة الممتازة.',
      descriptionEn: 'Special promotion on our premium composite resin collection.',
      discount: 33.33,
      validFrom: new Date('2025-03-01'),
      validTo: new Date('2025-03-15'),
      terms: 'Minimum purchase of 2 units required. Offer valid while supplies last.'
    }
  ];

  for (const offerData of offers) {
    if (offerData.exhibitorId) {
      try {
        await prisma.specialOffer.create({
          data: offerData
        });
        console.log(`✅ Created offer: ${offerData.titleEn}`);
      } catch (error) {
        console.error(`❌ Error creating offer ${offerData.titleEn}:`, error.message);
      }
    }
  }

  // Create exhibitor contacts
  const contacts = [
    {
      exhibitorId: createdExhibitors[0]?.id,
      name: 'John Smith',
      position: 'Sales Manager',
      email: '<EMAIL>',
      phone: '******-0111',
      isPrimary: true
    },
    {
      exhibitorId: createdExhibitors[1]?.id,
      name: 'Maria Garcia',
      position: 'Product Specialist',
      email: '<EMAIL>',
      phone: '******-0112',
      isPrimary: true
    },
    {
      exhibitorId: createdExhibitors[2]?.id,
      name: 'Dr. Ahmed Hassan',
      position: 'Clinical Director',
      email: '<EMAIL>',
      phone: '******-0113',
      isPrimary: true
    }
  ];

  for (const contactData of contacts) {
    if (contactData.exhibitorId) {
      try {
        await prisma.exhibitorContact.create({
          data: contactData
        });
        console.log(`✅ Created contact: ${contactData.name}`);
      } catch (error) {
        console.error(`❌ Error creating contact ${contactData.name}:`, error.message);
      }
    }
  }

  console.log('🏢 Exhibition seeding completed!');
}

module.exports = { seedExhibition };

// Run if called directly
if (require.main === module) {
  seedExhibition()
    .catch((e) => {
      console.error('❌ Error seeding exhibition:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
