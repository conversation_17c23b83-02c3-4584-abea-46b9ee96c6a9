import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  
  String _selectedCategory = 'الكل';
  
  final List<String> _categories = [
    'الكل', 'إعلانات', 'أخبار علمية', 'تحديثات', 'فعاليات', 'جوائز'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<double>(
      begin: 0.5,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 280,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'أخبار المؤتمر',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.7, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _slideAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                100 * _slideAnimation.value,
                                -50 * _slideAnimation.value,
                              ),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/news_bg.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 60),
                              
                              // News Icon
                              Container(
                                padding: const EdgeInsets.all(AppSizes.paddingL),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                  border: Border.all(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    width: 2,
                                  ),
                                ),
                                child: const Icon(
                                  Icons.newspaper,
                                  size: 50,
                                  color: Color(AppColors.primaryWhite),
                                ),
                              ),
                              
                              const SizedBox(height: AppSizes.paddingM),
                              
                              // Stats
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('25+', 'خبر جديد'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('5', 'إعلانات مهمة'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('10', 'تحديثات'),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                tabs: const [
                  Tab(text: 'الأحدث'),
                  Tab(text: 'الأهم'),
                  Tab(text: 'المحفوظة'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Category Filter
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _categories.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Container(
                      margin: const EdgeInsets.only(right: AppSizes.paddingS),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                        backgroundColor: const Color(AppColors.primaryWhite),
                        selectedColor: const Color(AppColors.primaryRed),
                        labelStyle: TextStyle(
                          color: isSelected
                              ? const Color(AppColors.primaryWhite)
                              : const Color(AppColors.primaryBlack),
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusL),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildLatestNews(),
                    _buildImportantNews(),
                    _buildSavedNews(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showNewsSubscription,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.notifications_active),
        label: const Text('اشتراك'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildLatestNews() {
    final news = _getLatestNews();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: news.length,
      itemBuilder: (context, index) {
        final article = news[index];
        return _buildNewsCard(article, index);
      },
    );
  }

  Widget _buildImportantNews() {
    final news = _getImportantNews();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: news.length,
      itemBuilder: (context, index) {
        final article = news[index];
        return _buildNewsCard(article, index);
      },
    );
  }

  Widget _buildSavedNews() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Color(AppColors.grey),
          ),
          SizedBox(height: AppSizes.paddingM),
          Text(
            'لا توجد أخبار محفوظة',
            style: TextStyle(
              fontSize: 18,
              color: Color(AppColors.grey),
            ),
          ),
          SizedBox(height: AppSizes.paddingS),
          Text(
            'احفظ الأخبار المهمة لقراءتها لاحقاً',
            style: TextStyle(
              color: Color(AppColors.grey),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNewsCard(Map<String, dynamic> article, int index) {
    final isImportant = article['isImportant'] ?? false;
    final isSaved = article['isSaved'] ?? false;
    final category = article['category'] ?? 'عام';
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: InkWell(
          onTap: () => _showNewsDetails(article),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
              gradient: LinearGradient(
                colors: [
                  const Color(AppColors.primaryWhite),
                  const Color(AppColors.offWhite),
                ],
              ),
              border: Border.all(
                color: isImportant 
                    ? const Color(AppColors.primaryRed)
                    : const Color(AppColors.lightGrey),
                width: isImportant ? 2 : 1,
              ),
            ),
            child: Column(
              children: [
                // News Image
                if (article['hasImage'] == true)
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(AppColors.primaryRed).withValues(alpha: 0.8),
                          const Color(AppColors.darkRed).withValues(alpha: 0.6),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(AppSizes.radiusL),
                        topRight: Radius.circular(AppSizes.radiusL),
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Placeholder image
                        const Center(
                          child: Icon(
                            Icons.image,
                            size: 60,
                            color: Color(AppColors.primaryWhite),
                          ),
                        ),
                        
                        // Badges
                        Positioned(
                          top: AppSizes.paddingS,
                          left: AppSizes.paddingS,
                          child: Row(
                            children: [
                              if (isImportant)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: AppSizes.paddingS,
                                    vertical: AppSizes.paddingXS,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(AppColors.primaryRed),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                  ),
                                  child: const Text(
                                    'مهم',
                                    style: TextStyle(
                                      color: Color(AppColors.primaryWhite),
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              
                              const SizedBox(width: AppSizes.paddingXS),
                              
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingS,
                                  vertical: AppSizes.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryBlack).withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                ),
                                child: Text(
                                  category,
                                  style: const TextStyle(
                                    color: Color(AppColors.primaryWhite),
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Save button
                        Positioned(
                          top: AppSizes.paddingS,
                          right: AppSizes.paddingS,
                          child: IconButton(
                            onPressed: () => _toggleSaveNews(article),
                            icon: Icon(
                              isSaved ? Icons.bookmark : Icons.bookmark_border,
                              color: const Color(AppColors.primaryWhite),
                            ),
                            style: IconButton.styleFrom(
                              backgroundColor: const Color(AppColors.primaryBlack).withValues(alpha: 0.5),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                
                // News Content
                Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Category and Date (if no image)
                      if (article['hasImage'] != true)
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingS,
                                vertical: AppSizes.paddingXS,
                              ),
                              decoration: BoxDecoration(
                                color: _getCategoryColor(category).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              ),
                              child: Text(
                                category,
                                style: TextStyle(
                                  color: _getCategoryColor(category),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const Spacer(),
                            if (isImportant)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingS,
                                  vertical: AppSizes.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryRed),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                ),
                                child: const Text(
                                  'مهم',
                                  style: TextStyle(
                                    color: Color(AppColors.primaryWhite),
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            const SizedBox(width: AppSizes.paddingS),
                            IconButton(
                              onPressed: () => _toggleSaveNews(article),
                              icon: Icon(
                                isSaved ? Icons.bookmark : Icons.bookmark_border,
                                color: isSaved 
                                    ? const Color(AppColors.primaryRed)
                                    : const Color(AppColors.grey),
                                size: AppSizes.iconS,
                              ),
                            ),
                          ],
                        ),
                      
                      if (article['hasImage'] != true)
                        const SizedBox(height: AppSizes.paddingS),
                      
                      // Title
                      Text(
                        article['title'],
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(AppColors.primaryBlack),
                        ),
                      ),
                      
                      const SizedBox(height: AppSizes.paddingS),
                      
                      // Summary
                      Text(
                        article['summary'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(AppColors.grey),
                          height: 1.4,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: AppSizes.paddingM),
                      
                      // Footer
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: AppSizes.iconS,
                            color: const Color(AppColors.grey),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            article['publishedAt'],
                            style: const TextStyle(
                              color: Color(AppColors.grey),
                              fontSize: 12,
                            ),
                          ),
                          const Spacer(),
                          Icon(
                            Icons.visibility,
                            size: AppSizes.iconS,
                            color: const Color(AppColors.grey),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            '${article['views']} مشاهدة',
                            style: const TextStyle(
                              color: Color(AppColors.grey),
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: AppSizes.paddingM),
                          IconButton(
                            onPressed: () => _shareNews(article),
                            icon: const Icon(
                              Icons.share,
                              color: Color(AppColors.primaryRed),
                              size: AppSizes.iconS,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'إعلانات':
        return const Color(AppColors.primaryRed);
      case 'أخبار علمية':
        return const Color(AppColors.info);
      case 'تحديثات':
        return const Color(AppColors.warning);
      case 'فعاليات':
        return const Color(AppColors.success);
      case 'جوائز':
        return const Color(AppColors.primaryBlack);
      default:
        return const Color(AppColors.grey);
    }
  }

  void _showNewsSubscription() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اشتراك الإشعارات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            SwitchListTile(
              title: const Text('الأخبار العاجلة'),
              subtitle: const Text('إشعارات فورية للأخبار المهمة'),
              value: true,
              onChanged: (value) {},
            ),
            
            SwitchListTile(
              title: const Text('التحديثات اليومية'),
              subtitle: const Text('ملخص يومي للأخبار الجديدة'),
              value: false,
              onChanged: (value) {},
            ),
            
            SwitchListTile(
              title: const Text('الفعاليات والجوائز'),
              subtitle: const Text('إشعارات الفعاليات والمسابقات'),
              value: true,
              onChanged: (value) {},
            ),
          ],
        ),
      ),
    );
  }

  void _showNewsDetails(Map<String, dynamic> article) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      article['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    // Meta info
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: _getCategoryColor(article['category']).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            article['category'],
                            style: TextStyle(
                              color: _getCategoryColor(article['category']),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingM),
                        Text(
                          article['publishedAt'],
                          style: const TextStyle(
                            color: Color(AppColors.grey),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    // Content
                    Text(
                      article['content'] ?? article['summary'],
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSaveNews(Map<String, dynamic> article) {
    setState(() {
      article['isSaved'] = !(article['isSaved'] ?? false);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          article['isSaved'] 
              ? 'تم حفظ الخبر'
              : 'تم إلغاء حفظ الخبر',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _shareNews(Map<String, dynamic> article) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة: ${article['title']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  List<Map<String, dynamic>> _getLatestNews() {
    return [
      {
        'title': 'افتتاح التسجيل المبكر للمؤتمر',
        'summary': 'بدء التسجيل المبكر للمؤتمر الدولي لطب الأسنان مع خصومات خاصة للمسجلين الأوائل',
        'content': 'يسر اللجنة المنظمة للمؤتمر الدولي لطب الأسنان الإعلان عن بدء التسجيل المبكر...',
        'category': 'إعلانات',
        'publishedAt': 'منذ ساعتين',
        'views': 1250,
        'isImportant': true,
        'isSaved': false,
        'hasImage': true,
      },
      {
        'title': 'اكتشاف جديد في علاج تسوس الأسنان',
        'summary': 'باحثون يطورون تقنية جديدة لعلاج التسوس دون الحاجة للحفر التقليدي',
        'content': 'توصل فريق من الباحثين في جامعة هارفارد إلى تقنية ثورية...',
        'category': 'أخبار علمية',
        'publishedAt': 'منذ 4 ساعات',
        'views': 890,
        'isImportant': false,
        'isSaved': true,
        'hasImage': false,
      },
      {
        'title': 'تحديث جدول المؤتمر',
        'summary': 'تم إضافة جلسات جديدة وتعديل مواعيد بعض الفعاليات',
        'content': 'نود إعلامكم بالتحديثات الجديدة على جدول المؤتمر...',
        'category': 'تحديثات',
        'publishedAt': 'أمس',
        'views': 567,
        'isImportant': true,
        'isSaved': false,
        'hasImage': false,
      },
    ];
  }

  List<Map<String, dynamic>> _getImportantNews() {
    return [
      {
        'title': 'إعلان المتحدثين الرئيسيين',
        'summary': 'الكشف عن قائمة المتحدثين الرئيسيين في المؤتمر من أشهر الخبراء العالميين',
        'content': 'يسعدنا الإعلان عن المتحدثين الرئيسيين في المؤتمر...',
        'category': 'إعلانات',
        'publishedAt': 'منذ يوم',
        'views': 2100,
        'isImportant': true,
        'isSaved': false,
        'hasImage': true,
      },
      {
        'title': 'جائزة أفضل بحث علمي',
        'summary': 'فتح باب التقديم لجائزة أفضل بحث علمي في المؤتمر',
        'content': 'تعلن اللجنة العلمية عن فتح باب التقديم...',
        'category': 'جوائز',
        'publishedAt': 'منذ يومين',
        'views': 1800,
        'isImportant': true,
        'isSaved': true,
        'hasImage': false,
      },
    ];
  }
}
