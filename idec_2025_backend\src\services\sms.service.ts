import twilio from 'twilio';
import { config } from '@/config/config';
import { logger, logSmsEvent } from '@/utils/logger';

export class SmsService {
  private client: twilio.Twilio;

  constructor() {
    this.client = twilio(config.sms.accountSid, config.sms.authToken);
  }

  // Send verification code
  public async sendVerificationCode(
    phoneNumber: string,
    code: string
  ): Promise<void> {
    const message = `رمز التحقق الخاص بك في مؤتمر IDEC 2025 هو: ${code}\nهذا الرمز صالح لمدة 10 دقائق فقط.`;

    try {
      await this.client.messages.create({
        body: message,
        from: config.sms.phoneNumber,
        to: phoneNumber,
      });

      logSmsEvent('VERIFICATION_CODE_SENT', phoneNumber, true);
    } catch (error) {
      logSmsEvent('VERIFICATION_CODE_FAILED', phoneNumber, false, { error });
      throw error;
    }
  }

  // Send session reminder
  public async sendSessionReminder(
    phoneNumber: string,
    sessionTitle: string,
    sessionTime: string,
    location: string
  ): Promise<void> {
    const message = `تذكير: جلسة "${sessionTitle}" ستبدأ في ${sessionTime} في ${location}. مؤتمر IDEC 2025`;

    try {
      await this.client.messages.create({
        body: message,
        from: config.sms.phoneNumber,
        to: phoneNumber,
      });

      logSmsEvent('SESSION_REMINDER_SENT', phoneNumber, true);
    } catch (error) {
      logSmsEvent('SESSION_REMINDER_FAILED', phoneNumber, false, { error });
      throw error;
    }
  }

  // Send payment confirmation
  public async sendPaymentConfirmation(
    phoneNumber: string,
    amount: number,
    currency: string,
    paymentId: string
  ): Promise<void> {
    const message = `تم استلام دفعتك بنجاح: ${amount} ${currency}. رقم العملية: ${paymentId}. شكراً لك - مؤتمر IDEC 2025`;

    try {
      await this.client.messages.create({
        body: message,
        from: config.sms.phoneNumber,
        to: phoneNumber,
      });

      logSmsEvent('PAYMENT_CONFIRMATION_SENT', phoneNumber, true);
    } catch (error) {
      logSmsEvent('PAYMENT_CONFIRMATION_FAILED', phoneNumber, false, { error });
      throw error;
    }
  }

  // Send subscription approval
  public async sendSubscriptionApproval(
    phoneNumber: string,
    name: string,
    subscriptionType: string
  ): Promise<void> {
    const message = `مبروك ${name}! تم قبول طلب اشتراكك (${subscriptionType}) في مؤتمر IDEC 2025. يمكنك الآن إتمام عملية الدفع.`;

    try {
      await this.client.messages.create({
        body: message,
        from: config.sms.phoneNumber,
        to: phoneNumber,
      });

      logSmsEvent('SUBSCRIPTION_APPROVAL_SENT', phoneNumber, true);
    } catch (error) {
      logSmsEvent('SUBSCRIPTION_APPROVAL_FAILED', phoneNumber, false, { error });
      throw error;
    }
  }

  // Send general notification
  public async sendNotification(
    phoneNumber: string,
    message: string
  ): Promise<void> {
    const fullMessage = `${message}\n\nمؤتمر IDEC 2025`;

    try {
      await this.client.messages.create({
        body: fullMessage,
        from: config.sms.phoneNumber,
        to: phoneNumber,
      });

      logSmsEvent('NOTIFICATION_SENT', phoneNumber, true);
    } catch (error) {
      logSmsEvent('NOTIFICATION_FAILED', phoneNumber, false, { error });
      throw error;
    }
  }

  // Send bulk SMS
  public async sendBulkSms(
    phoneNumbers: string[],
    message: string
  ): Promise<{ success: string[]; failed: string[] }> {
    const results = { success: [], failed: [] };
    const fullMessage = `${message}\n\nمؤتمر IDEC 2025`;

    for (const phoneNumber of phoneNumbers) {
      try {
        await this.client.messages.create({
          body: fullMessage,
          from: config.sms.phoneNumber,
          to: phoneNumber,
        });

        results.success.push(phoneNumber);
        logSmsEvent('BULK_SMS_SENT', phoneNumber, true);
      } catch (error) {
        results.failed.push(phoneNumber);
        logSmsEvent('BULK_SMS_FAILED', phoneNumber, false, { error });
      }

      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  }

  // Validate phone number format
  public validatePhoneNumber(phoneNumber: string): boolean {
    // Basic validation for international format
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  // Format phone number for Saudi Arabia
  public formatSaudiPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // Handle different formats
    if (digits.startsWith('966')) {
      return `+${digits}`;
    } else if (digits.startsWith('05')) {
      return `+966${digits.substring(1)}`;
    } else if (digits.startsWith('5')) {
      return `+966${digits}`;
    }
    
    return phoneNumber; // Return original if format not recognized
  }

  // Check SMS delivery status
  public async checkDeliveryStatus(messageSid: string): Promise<string> {
    try {
      const message = await this.client.messages(messageSid).fetch();
      return message.status;
    } catch (error) {
      logger.error('Failed to check SMS delivery status:', error);
      throw error;
    }
  }

  // Get SMS usage statistics
  public async getUsageStatistics(
    startDate?: Date,
    endDate?: Date
  ): Promise<any> {
    try {
      const usage = await this.client.usage.records.list({
        category: 'sms',
        startDate: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        endDate: endDate || new Date(),
      });

      return usage.map(record => ({
        date: record.startDate,
        count: record.count,
        price: record.price,
        priceUnit: record.priceUnit,
      }));
    } catch (error) {
      logger.error('Failed to get SMS usage statistics:', error);
      throw error;
    }
  }
}
