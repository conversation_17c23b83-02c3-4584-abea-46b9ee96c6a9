import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  Avatar,
  Alert,
  CircularProgress,
  MenuItem,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  PhotoCamera,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { userAPI, uploadAPI } from '../../services/api';

const ProfilePage = () => {
  const { user, updateProfile } = useAuth();
  const { t } = useLanguage();
  const queryClient = useQueryClient();
  
  const [isEditing, setIsEditing] = useState(false);
  const [updateMessage, setUpdateMessage] = useState('');
  const [updateError, setUpdateError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      arabicName: user?.arabicName || '',
      englishName: user?.englishName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      qualification: user?.qualification || '',
      specialization: user?.specialization || '',
      university: user?.university || '',
    },
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: userAPI.updateProfile,
    onSuccess: (response) => {
      setUpdateMessage(t('profileUpdateSuccess'));
      setUpdateError('');
      setIsEditing(false);
      updateProfile(response.data);
      queryClient.invalidateQueries(['userProfile']);
    },
    onError: (error) => {
      setUpdateError(error.response?.data?.message || t('profileUpdateError'));
      setUpdateMessage('');
    },
  });

  // Upload profile image mutation
  const uploadImageMutation = useMutation({
    mutationFn: uploadAPI.uploadProfileImage,
    onSuccess: () => {
      queryClient.invalidateQueries(['userProfile']);
    },
  });

  const qualifications = [
    { value: 'DOCTOR', label: t('DOCTOR') },
    { value: 'STUDENT_YEAR_6', label: t('STUDENT_YEAR_6') },
    { value: 'STUDENT_YEAR_5', label: t('STUDENT_YEAR_5') },
    { value: 'STUDENT_YEAR_4', label: t('STUDENT_YEAR_4') },
    { value: 'STUDENT_YEAR_3', label: t('STUDENT_YEAR_3') },
    { value: 'STUDENT_YEAR_2', label: t('STUDENT_YEAR_2') },
    { value: 'STUDENT_YEAR_1', label: t('STUDENT_YEAR_1') },
  ];

  const onSubmit = async (data) => {
    setUpdateMessage('');
    setUpdateError('');
    await updateProfileMutation.mutateAsync(data);
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setUpdateMessage('');
    setUpdateError('');
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      uploadImageMutation.mutate(file);
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('profile')}
      </Typography>

      {/* Success/Error Messages */}
      {updateMessage && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {updateMessage}
        </Alert>
      )}
      
      {updateError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {updateError}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Profile Picture */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Box position="relative" display="inline-block">
                <Avatar
                  sx={{ 
                    width: 150, 
                    height: 150, 
                    mx: 'auto', 
                    mb: 2,
                    bgcolor: 'primary.main',
                    fontSize: '3rem'
                  }}
                >
                  {user?.arabicName?.charAt(0) || user?.englishName?.charAt(0) || 'U'}
                </Avatar>
                
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="profile-image-upload"
                  type="file"
                  onChange={handleImageUpload}
                />
                <label htmlFor="profile-image-upload">
                  <IconButton
                    color="primary"
                    aria-label="upload picture"
                    component="span"
                    sx={{
                      position: 'absolute',
                      bottom: 16,
                      right: 0,
                      bgcolor: 'background.paper',
                      boxShadow: 2,
                      '&:hover': {
                        bgcolor: 'background.paper',
                      },
                    }}
                  >
                    <PhotoCamera />
                  </IconButton>
                </label>
              </Box>
              
              <Typography variant="h6" gutterBottom>
                {user?.arabicName || user?.englishName}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                {t(user?.qualification || 'DOCTOR')}
              </Typography>
              
              {uploadImageMutation.isPending && (
                <Box mt={2}>
                  <CircularProgress size={24} />
                  <Typography variant="body2" color="text.secondary">
                    {t('uploadingImage', 'جاري رفع الصورة...')}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Profile Information */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
                <Typography variant="h6">
                  {t('personalInformation', 'المعلومات الشخصية')}
                </Typography>
                
                {!isEditing ? (
                  <Button
                    startIcon={<Edit />}
                    onClick={() => setIsEditing(true)}
                    variant="outlined"
                  >
                    {t('edit')}
                  </Button>
                ) : (
                  <Box display="flex" gap={1}>
                    <Button
                      startIcon={<Save />}
                      onClick={handleSubmit(onSubmit)}
                      variant="contained"
                      disabled={updateProfileMutation.isPending}
                    >
                      {updateProfileMutation.isPending ? (
                        <CircularProgress size={20} color="inherit" />
                      ) : (
                        t('save')
                      )}
                    </Button>
                    <Button
                      startIcon={<Cancel />}
                      onClick={handleCancel}
                      variant="outlined"
                    >
                      {t('cancel')}
                    </Button>
                  </Box>
                )}
              </Box>

              <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                <Grid container spacing={3}>
                  {/* Arabic Name */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t('arabicName')}
                      {...register('arabicName', {
                        required: t('arabicNameRequired', 'الاسم العربي مطلوب'),
                      })}
                      error={!!errors.arabicName}
                      helperText={errors.arabicName?.message}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* English Name */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t('englishName')}
                      {...register('englishName', {
                        required: t('englishNameRequired', 'الاسم الإنجليزي مطلوب'),
                      })}
                      error={!!errors.englishName}
                      helperText={errors.englishName?.message}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* Email */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t('email')}
                      type="email"
                      {...register('email', {
                        required: t('emailRequired', 'البريد الإلكتروني مطلوب'),
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: t('emailInvalid', 'البريد الإلكتروني غير صحيح'),
                        },
                      })}
                      error={!!errors.email}
                      helperText={errors.email?.message}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* Phone */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t('phone')}
                      {...register('phone', {
                        required: t('phoneRequired', 'رقم الهاتف مطلوب'),
                      })}
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* Qualification */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      select
                      label={t('qualification')}
                      {...register('qualification', {
                        required: t('qualificationRequired', 'المؤهل العلمي مطلوب'),
                      })}
                      error={!!errors.qualification}
                      helperText={errors.qualification?.message}
                      disabled={!isEditing}
                    >
                      {qualifications.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>

                  {/* Specialization */}
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={t('specialization')}
                      {...register('specialization')}
                      error={!!errors.specialization}
                      helperText={errors.specialization?.message}
                      disabled={!isEditing}
                    />
                  </Grid>

                  {/* University */}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t('university')}
                      {...register('university')}
                      error={!!errors.university}
                      helperText={errors.university?.message}
                      disabled={!isEditing}
                    />
                  </Grid>
                </Grid>
              </Box>

              <Divider sx={{ my: 3 }} />

              {/* Account Information */}
              <Typography variant="h6" gutterBottom>
                {t('accountInformation', 'معلومات الحساب')}
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('memberSince', 'عضو منذ')}
                  </Typography>
                  <Typography variant="body1">
                    {new Date(user?.createdAt).toLocaleDateString('ar-SA')}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    {t('lastUpdate', 'آخر تحديث')}
                  </Typography>
                  <Typography variant="body1">
                    {new Date(user?.updatedAt).toLocaleDateString('ar-SA')}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProfilePage;
