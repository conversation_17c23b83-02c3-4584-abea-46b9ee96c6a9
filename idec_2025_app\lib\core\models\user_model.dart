class User {
  final String id;
  final String email;
  final String? arabicName;
  final String? englishName;
  final String? phone;
  final String? qualification;
  final String? specialization;
  final String? workPlace;
  final String? city;
  final String? country;
  final String role;
  final String status;
  final String? profileImage;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.email,
    this.arabicName,
    this.englishName,
    this.phone,
    this.qualification,
    this.specialization,
    this.workPlace,
    this.city,
    this.country,
    required this.role,
    required this.status,
    this.profileImage,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      arabicName: json['arabicName'],
      englishName: json['englishName'],
      phone: json['phone'],
      qualification: json['qualification'],
      specialization: json['specialization'],
      workPlace: json['workPlace'],
      city: json['city'],
      country: json['country'],
      role: json['role'],
      status: json['status'],
      profileImage: json['profileImage'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'arabicName': arabicName,
      'englishName': englishName,
      'phone': phone,
      'qualification': qualification,
      'specialization': specialization,
      'workPlace': workPlace,
      'city': city,
      'country': country,
      'role': role,
      'status': status,
      'profileImage': profileImage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get displayName => englishName ?? arabicName ?? email;
  
  bool get isActive => status == 'ACTIVE';
  bool get isAdmin => ['SUPER_ADMIN', 'ADMIN'].contains(role);
}

class AuthResponse {
  final bool success;
  final String? message;
  final String? token;
  final String? refreshToken;
  final User? user;

  AuthResponse({
    required this.success,
    this.message,
    this.token,
    this.refreshToken,
    this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'],
      token: json['data']?['token'],
      refreshToken: json['data']?['refreshToken'],
      user: json['data']?['user'] != null 
          ? User.fromJson(json['data']['user']) 
          : null,
    );
  }
}

class UserStats {
  final int totalPoints;
  final int totalBadges;
  final int sessionsAttended;
  final int connectionsCount;
  final String currentLevel;

  UserStats({
    required this.totalPoints,
    required this.totalBadges,
    required this.sessionsAttended,
    required this.connectionsCount,
    required this.currentLevel,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalPoints: json['totalPoints'] ?? 0,
      totalBadges: json['totalBadges'] ?? 0,
      sessionsAttended: json['sessionsAttended'] ?? 0,
      connectionsCount: json['connectionsCount'] ?? 0,
      currentLevel: json['currentLevel'] ?? 'Bronze',
    );
  }
}
