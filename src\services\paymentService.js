const { PrismaClient } = require('@prisma/client');
const { NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { createPaginationResult } = require('../middleware/validation');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
} catch (error) {
  logger.error('Failed to initialize Prisma client:', error);
  prisma = null;
}

class PaymentService {
  // Create payment record
  async createPayment(paymentData) {
    try {
      const payment = {
        userId: paymentData.userId,
        subscriptionId: paymentData.subscriptionId,
        amount: paymentData.amount,
        currency: paymentData.currency || 'SAR',
        purpose: paymentData.purpose || 'CONFERENCE_SUBSCRIPTION',
        method: paymentData.method || 'ONLINE',
        status: 'PENDING',
        reference: paymentData.reference || this.generatePaymentReference(),
        gatewayTransactionId: paymentData.gatewayTransactionId || null,
        createdAt: new Date()
      };

      let createdPayment;
      if (prisma) {
        createdPayment = await prisma.payment.create({
          data: payment,
          include: {
            user: {
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                email: true
              }
            },
            subscription: {
              select: {
                id: true,
                qualification: true
              }
            }
          }
        });
      } else {
        // Mock payment
        createdPayment = {
          id: 'payment_' + Date.now(),
          ...payment,
          user: {
            id: paymentData.userId,
            arabicName: 'مستخدم تجريبي',
            englishName: 'Test User',
            email: '<EMAIL>'
          },
          subscription: {
            id: paymentData.subscriptionId,
            qualification: 'DOCTOR'
          }
        };
      }

      logger.logUserAction(paymentData.userId, 'PAYMENT_CREATED', {
        paymentId: createdPayment.id,
        amount: payment.amount,
        purpose: payment.purpose
      });

      return createdPayment;
    } catch (error) {
      logger.error('Error creating payment:', error);
      throw error;
    }
  }

  // Generate payment reference
  generatePaymentReference() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `IDEC2025-${timestamp}-${random}`;
  }

  // Update payment status
  async updatePaymentStatus(paymentId, status, gatewayData = {}) {
    try {
      const updateData = {
        status,
        updatedAt: new Date()
      };

      if (gatewayData.transactionId) {
        updateData.gatewayTransactionId = gatewayData.transactionId;
      }

      if (gatewayData.gatewayResponse) {
        updateData.gatewayResponse = gatewayData.gatewayResponse;
      }

      if (status === 'COMPLETED') {
        updateData.completedAt = new Date();
      }

      let updatedPayment;
      if (prisma) {
        updatedPayment = await prisma.payment.update({
          where: { id: paymentId },
          data: updateData,
          include: {
            user: {
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                email: true
              }
            },
            subscription: true
          }
        });

        // If payment is completed, update subscription status
        if (status === 'COMPLETED' && updatedPayment.subscription) {
          await prisma.subscription.update({
            where: { id: updatedPayment.subscription.id },
            data: { status: 'ACTIVE' }
          });
        }
      } else {
        // Mock update
        updatedPayment = {
          id: paymentId,
          status,
          updatedAt: new Date(),
          user: {
            id: 'user123',
            arabicName: 'مستخدم تجريبي',
            englishName: 'Test User',
            email: '<EMAIL>'
          }
        };
      }

      logger.logUserAction(updatedPayment.user.id, 'PAYMENT_STATUS_UPDATED', {
        paymentId,
        newStatus: status
      });

      return updatedPayment;
    } catch (error) {
      logger.error('Error updating payment status:', error);
      throw error;
    }
  }

  // Get payment by ID
  async getPaymentById(paymentId) {
    try {
      if (prisma) {
        const payment = await prisma.payment.findUnique({
          where: { id: paymentId },
          include: {
            user: {
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                email: true
              }
            },
            subscription: {
              select: {
                id: true,
                qualification: true,
                amount: true
              }
            }
          }
        });

        if (!payment) {
          throw new NotFoundError('Payment not found');
        }

        return payment;
      } else {
        // Mock payment
        return {
          id: paymentId,
          amount: 500.00,
          currency: 'SAR',
          status: 'PENDING',
          purpose: 'CONFERENCE_SUBSCRIPTION',
          reference: 'IDEC2025-1234567890-ABC123',
          createdAt: new Date(),
          user: {
            id: 'user123',
            arabicName: 'مستخدم تجريبي',
            englishName: 'Test User',
            email: '<EMAIL>'
          }
        };
      }
    } catch (error) {
      logger.error('Error getting payment by ID:', error);
      throw error;
    }
  }

  // Get user payments
  async getUserPayments(userId, pagination = {}) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      if (prisma) {
        const [payments, total] = await Promise.all([
          prisma.payment.findMany({
            where: { userId },
            skip: offset,
            take: limit,
            include: {
              subscription: {
                select: {
                  id: true,
                  qualification: true
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          }),
          prisma.payment.count({ where: { userId } })
        ]);

        return {
          payments,
          pagination: createPaginationResult(page, limit, total)
        };
      } else {
        // Mock payments
        const mockPayments = [
          {
            id: 'payment1',
            userId,
            amount: 500.00,
            currency: 'SAR',
            status: 'COMPLETED',
            purpose: 'CONFERENCE_SUBSCRIPTION',
            reference: 'IDEC2025-1234567890-ABC123',
            createdAt: new Date(),
            completedAt: new Date(),
            subscription: {
              id: 'sub1',
              qualification: 'DOCTOR'
            }
          }
        ];

        return {
          payments: mockPayments,
          pagination: createPaginationResult(1, 10, mockPayments.length)
        };
      }
    } catch (error) {
      logger.error('Error getting user payments:', error);
      throw error;
    }
  }

  // Get all payments (admin)
  async getAllPayments(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      if (prisma) {
        const where = {};
        
        if (filters.status) {
          where.status = filters.status;
        }
        
        if (filters.purpose) {
          where.purpose = filters.purpose;
        }

        if (filters.dateFrom || filters.dateTo) {
          where.createdAt = {};
          if (filters.dateFrom) {
            where.createdAt.gte = new Date(filters.dateFrom);
          }
          if (filters.dateTo) {
            where.createdAt.lte = new Date(filters.dateTo);
          }
        }

        const [payments, total] = await Promise.all([
          prisma.payment.findMany({
            where,
            skip: offset,
            take: limit,
            include: {
              user: {
                select: {
                  id: true,
                  arabicName: true,
                  englishName: true,
                  email: true
                }
              },
              subscription: {
                select: {
                  id: true,
                  qualification: true
                }
              }
            },
            orderBy: { createdAt: 'desc' }
          }),
          prisma.payment.count({ where })
        ]);

        return {
          payments,
          pagination: createPaginationResult(page, limit, total)
        };
      } else {
        // Mock payments
        const mockPayments = [
          {
            id: 'payment1',
            userId: 'user1',
            amount: 500.00,
            currency: 'SAR',
            status: 'COMPLETED',
            purpose: 'CONFERENCE_SUBSCRIPTION',
            reference: 'IDEC2025-1234567890-ABC123',
            createdAt: new Date(),
            user: {
              id: 'user1',
              arabicName: 'د. سارة أحمد',
              englishName: 'Dr. Sarah Ahmed',
              email: '<EMAIL>'
            },
            subscription: {
              id: 'sub1',
              qualification: 'DOCTOR'
            }
          }
        ];

        return {
          payments: mockPayments,
          pagination: createPaginationResult(1, 10, mockPayments.length)
        };
      }
    } catch (error) {
      logger.error('Error getting all payments:', error);
      throw error;
    }
  }

  // Get payment statistics
  async getPaymentStats() {
    try {
      if (prisma) {
        const [
          totalRevenue,
          totalPayments,
          completedPayments,
          pendingPayments,
          failedPayments
        ] = await Promise.all([
          prisma.payment.aggregate({
            where: { status: 'COMPLETED' },
            _sum: { amount: true }
          }),
          prisma.payment.count(),
          prisma.payment.count({ where: { status: 'COMPLETED' } }),
          prisma.payment.count({ where: { status: 'PENDING' } }),
          prisma.payment.count({ where: { status: 'FAILED' } })
        ]);

        return {
          totalRevenue: totalRevenue._sum.amount || 0,
          totalPayments,
          byStatus: {
            completed: completedPayments,
            pending: pendingPayments,
            failed: failedPayments
          }
        };
      } else {
        // Mock stats
        return {
          totalRevenue: 75000.00,
          totalPayments: 150,
          byStatus: {
            completed: 120,
            pending: 20,
            failed: 10
          }
        };
      }
    } catch (error) {
      logger.error('Error getting payment stats:', error);
      throw error;
    }
  }

  // Process refund
  async processRefund(paymentId, refundAmount, reason, processedBy) {
    try {
      // TODO: Implement refund logic with payment gateway
      
      logger.logAudit(processedBy, 'PAYMENT_REFUNDED', 'payment', paymentId, {
        refundAmount,
        reason
      });

      return {
        paymentId,
        refundAmount,
        reason,
        processedAt: new Date(),
        processedBy
      };
    } catch (error) {
      logger.error('Error processing refund:', error);
      throw error;
    }
  }
}

module.exports = new PaymentService();
