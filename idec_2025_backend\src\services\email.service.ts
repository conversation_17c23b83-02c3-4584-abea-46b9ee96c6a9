import nodemailer from 'nodemailer';
import { config } from '@/config/config';
import { logger, logEmailEvent } from '@/utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: config.email.smtp.host,
      port: config.email.smtp.port,
      secure: config.email.smtp.secure,
      auth: {
        user: config.email.smtp.user,
        pass: config.email.smtp.pass,
      },
    });
  }

  // Send verification email
  public async sendVerificationEmail(
    email: string,
    name: string,
    token: string
  ): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    const subject = 'تأكيد البريد الإلكتروني - مؤتمر IDEC 2025';
    const html = this.getVerificationEmailTemplate(name, verificationUrl);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('VERIFICATION_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('VERIFICATION_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Send password reset email
  public async sendPasswordResetEmail(
    email: string,
    name: string,
    token: string
  ): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    const subject = 'إعادة تعيين كلمة المرور - مؤتمر IDEC 2025';
    const html = this.getPasswordResetEmailTemplate(name, resetUrl);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('PASSWORD_RESET_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('PASSWORD_RESET_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Send welcome email
  public async sendWelcomeEmail(
    email: string,
    name: string
  ): Promise<void> {
    const subject = 'مرحباً بك في مؤتمر IDEC 2025';
    const html = this.getWelcomeEmailTemplate(name);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('WELCOME_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('WELCOME_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Send subscription approval email
  public async sendSubscriptionApprovalEmail(
    email: string,
    name: string,
    subscriptionType: string
  ): Promise<void> {
    const subject = 'تم قبول طلب الاشتراك - مؤتمر IDEC 2025';
    const html = this.getSubscriptionApprovalEmailTemplate(name, subscriptionType);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('SUBSCRIPTION_APPROVAL_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('SUBSCRIPTION_APPROVAL_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Send payment confirmation email
  public async sendPaymentConfirmationEmail(
    email: string,
    name: string,
    amount: number,
    currency: string,
    paymentId: string
  ): Promise<void> {
    const subject = 'تأكيد الدفع - مؤتمر IDEC 2025';
    const html = this.getPaymentConfirmationEmailTemplate(name, amount, currency, paymentId);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('PAYMENT_CONFIRMATION_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('PAYMENT_CONFIRMATION_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Send session reminder email
  public async sendSessionReminderEmail(
    email: string,
    name: string,
    sessionTitle: string,
    sessionDate: string,
    sessionTime: string,
    location: string
  ): Promise<void> {
    const subject = `تذكير بالجلسة: ${sessionTitle}`;
    const html = this.getSessionReminderEmailTemplate(name, sessionTitle, sessionDate, sessionTime, location);

    try {
      await this.transporter.sendMail({
        from: config.email.from,
        to: email,
        subject,
        html,
      });

      logEmailEvent('SESSION_REMINDER_EMAIL_SENT', email, subject, true);
    } catch (error) {
      logEmailEvent('SESSION_REMINDER_EMAIL_FAILED', email, subject, false, { error });
      throw error;
    }
  }

  // Email templates
  private getVerificationEmailTemplate(name: string, verificationUrl: string): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأكيد البريد الإلكتروني</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .button { display: inline-block; background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>مؤتمر IDEC 2025</h1>
            <p>المؤتمر الدولي لطب الأسنان</p>
          </div>
          <div class="content">
            <h2>مرحباً ${name}</h2>
            <p>شكراً لك على التسجيل في مؤتمر IDEC 2025. يرجى تأكيد بريدك الإلكتروني بالضغط على الرابط أدناه:</p>
            <a href="${verificationUrl}" class="button">تأكيد البريد الإلكتروني</a>
            <p>إذا لم تقم بإنشاء هذا الحساب، يرجى تجاهل هذا البريد الإلكتروني.</p>
            <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>الرياض، المملكة العربية السعودية</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getPasswordResetEmailTemplate(name: string, resetUrl: string): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إعادة تعيين كلمة المرور</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .button { display: inline-block; background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
          .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>مؤتمر IDEC 2025</h1>
            <p>إعادة تعيين كلمة المرور</p>
          </div>
          <div class="content">
            <h2>مرحباً ${name}</h2>
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. اضغط على الرابط أدناه لإعادة تعيين كلمة المرور:</p>
            <a href="${resetUrl}" class="button">إعادة تعيين كلمة المرور</a>
            <div class="warning">
              <strong>تنبيه أمني:</strong>
              <ul>
                <li>هذا الرابط صالح لمدة ساعة واحدة فقط</li>
                <li>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد</li>
                <li>لا تشارك هذا الرابط مع أي شخص آخر</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>الرياض، المملكة العربية السعودية</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getWelcomeEmailTemplate(name: string): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>مرحباً بك في مؤتمر IDEC 2025</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .feature { background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>مرحباً بك في مؤتمر IDEC 2025</h1>
            <p>المؤتمر الدولي لطب الأسنان</p>
          </div>
          <div class="content">
            <h2>أهلاً وسهلاً ${name}</h2>
            <p>نرحب بك في مؤتمر IDEC 2025، أكبر تجمع لأطباء الأسنان في المنطقة.</p>
            
            <h3>ما يمكنك فعله الآن:</h3>
            <div class="feature">
              <strong>📅 تصفح الجلسات العلمية</strong><br>
              اكتشف أحدث التطورات في طب الأسنان
            </div>
            <div class="feature">
              <strong>🎓 التسجيل في الدورات التدريبية</strong><br>
              احصل على ساعات التعليم المستمر المعتمدة
            </div>
            <div class="feature">
              <strong>🏪 زيارة المعرض المصاحب</strong><br>
              تعرف على أحدث المنتجات والتقنيات
            </div>
            <div class="feature">
              <strong>🤝 التواصل مع الخبراء</strong><br>
              بناء شبكة علاقات مهنية قوية
            </div>
            
            <p>نتطلع لرؤيتك في المؤتمر!</p>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>15-17 مارس 2025 | الرياض، المملكة العربية السعودية</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getSubscriptionApprovalEmailTemplate(name: string, subscriptionType: string): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تم قبول طلب الاشتراك</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #16a085, #27ae60); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; color: #155724; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 تهانينا!</h1>
            <p>تم قبول طلب الاشتراك</p>
          </div>
          <div class="content">
            <h2>عزيزي ${name}</h2>
            <div class="success">
              <strong>تم قبول طلب اشتراكك في مؤتمر IDEC 2025!</strong><br>
              نوع الاشتراك: ${subscriptionType}
            </div>
            <p>يمكنك الآن المتابعة لإتمام عملية الدفع والاستفادة من جميع مزايا المؤتمر.</p>
            
            <h3>الخطوات التالية:</h3>
            <ol>
              <li>قم بتسجيل الدخول إلى حسابك</li>
              <li>أتمم عملية الدفع</li>
              <li>احصل على بطاقة المؤتمر الرقمية</li>
              <li>ابدأ في التسجيل للجلسات والدورات</li>
            </ol>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>الرياض، المملكة العربية السعودية</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getPaymentConfirmationEmailTemplate(
    name: string,
    amount: number,
    currency: string,
    paymentId: string
  ): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأكيد الدفع</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #16a085, #27ae60); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .payment-details { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ تم الدفع بنجاح</h1>
            <p>شكراً لك على الدفع</p>
          </div>
          <div class="content">
            <h2>عزيزي ${name}</h2>
            <p>تم استلام دفعتك بنجاح. إليك تفاصيل العملية:</p>
            
            <div class="payment-details">
              <h3>تفاصيل الدفع:</h3>
              <p><strong>المبلغ:</strong> ${amount} ${currency}</p>
              <p><strong>رقم العملية:</strong> ${paymentId}</p>
              <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
              <p><strong>الحالة:</strong> مكتملة ✅</p>
            </div>
            
            <p>يمكنك الآن الاستفادة من جميع مزايا المؤتمر والبدء في التسجيل للجلسات والدورات التدريبية.</p>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>للاستفسارات: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private getSessionReminderEmailTemplate(
    name: string,
    sessionTitle: string,
    sessionDate: string,
    sessionTime: string,
    location: string
  ): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تذكير بالجلسة</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
          .container { max-width: 600px; margin: 0 auto; background-color: white; }
          .header { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 30px; text-align: center; }
          .content { padding: 30px; }
          .session-details { background-color: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #2196f3; }
          .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⏰ تذكير بالجلسة</h1>
            <p>لا تفوت هذه الجلسة المهمة</p>
          </div>
          <div class="content">
            <h2>عزيزي ${name}</h2>
            <p>نذكرك بالجلسة التي سجلت فيها:</p>
            
            <div class="session-details">
              <h3>📋 ${sessionTitle}</h3>
              <p><strong>📅 التاريخ:</strong> ${sessionDate}</p>
              <p><strong>🕐 الوقت:</strong> ${sessionTime}</p>
              <p><strong>📍 المكان:</strong> ${location}</p>
            </div>
            
            <p>ننصحك بالحضور قبل 15 دقيقة من بداية الجلسة لضمان الحصول على أفضل مقعد.</p>
            
            <p>نتطلع لرؤيتك في الجلسة!</p>
          </div>
          <div class="footer">
            <p>© 2025 مؤتمر IDEC. جميع الحقوق محفوظة.</p>
            <p>للاستفسارات: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}
