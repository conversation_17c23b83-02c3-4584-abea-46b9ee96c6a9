import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography, Button } from '@mui/material';
import { AdminPanelSettings, Home } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

const AdminRoute = ({ children }) => {
  const { isAuthenticated, isLoading, isAdmin } = useAuth();
  const { t } = useLanguage();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={2}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" color="text.secondary">
          {t('loading')}
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Show access denied if not admin
  if (!isAdmin()) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={3}
        p={3}
        textAlign="center"
      >
        <AdminPanelSettings sx={{ fontSize: 80, color: 'error.main' }} />
        
        <Typography variant="h3" color="error" gutterBottom>
          {t('adminAccessRequired', 'مطلوب صلاحيات إدارية')}
        </Typography>
        
        <Typography variant="h6" color="text.secondary" maxWidth={600}>
          {t('adminAccessMessage', 'هذه الصفحة مخصصة للمديرين فقط. إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بمدير النظام.')}
        </Typography>
        
        <Box display="flex" gap={2} mt={2}>
          <Button
            variant="contained"
            startIcon={<Home />}
            onClick={() => window.location.href = '/'}
            size="large"
          >
            {t('backToHome', 'العودة للرئيسية')}
          </Button>
          
          <Button
            variant="outlined"
            onClick={() => window.location.href = '/dashboard'}
            size="large"
          >
            {t('goToDashboard', 'الذهاب للوحة التحكم')}
          </Button>
        </Box>
      </Box>
    );
  }

  // Render admin content if authenticated and authorized
  return children;
};

export default AdminRoute;
