import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class ARExperienceScreen extends StatefulWidget {
  const ARExperienceScreen({super.key});

  @override
  State<ARExperienceScreen> createState() => _ARExperienceScreenState();
}

class _ARExperienceScreenState extends State<ARExperienceScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  
  bool _isARActive = false;
  String _selectedExperience = 'dental_anatomy';
  
  final List<Map<String, dynamic>> _arExperiences = [
    {
      'id': 'dental_anatomy',
      'title': 'تشريح الأسنان ثلاثي الأبعاد',
      'description': 'استكشف تشريح الأسنان بتقنية الواقع المعزز',
      'icon': Icons.view_in_ar,
      'color': AppColors.info,
    },
    {
      'id': 'implant_procedure',
      'title': 'محاكاة زراعة الأسنان',
      'description': 'شاهد عملية زراعة الأسنان خطوة بخطوة',
      'icon': Icons.precision_manufacturing,
      'color': AppColors.success,
    },
    {
      'id': 'orthodontic_treatment',
      'title': 'علاج التقويم التفاعلي',
      'description': 'تجربة تفاعلية لعلاج التقويم',
      'icon': Icons.straighten,
      'color': AppColors.warning,
    },
    {
      'id': 'oral_surgery',
      'title': 'جراحة الفم والوجه',
      'description': 'محاكاة العمليات الجراحية المعقدة',
      'icon': Icons.medical_services,
      'color': AppColors.primaryRed,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(AppColors.primaryBlack),
              Color(AppColors.darkBlack),
              Color(AppColors.primaryRed),
            ],
            stops: [0.0, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingM),
                    Expanded(
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: const Text(
                          'تجربة الواقع المعزز',
                          style: TextStyle(
                            color: Color(AppColors.primaryWhite),
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _showARInfo,
                      icon: const Icon(
                        Icons.info_outline,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),
                  ],
                ),
              ),
              
              // AR Status Indicator
              FadeTransition(
                opacity: _fadeAnimation,
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    color: _isARActive 
                        ? const Color(AppColors.success).withValues(alpha: 0.2)
                        : const Color(AppColors.warning).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    border: Border.all(
                      color: _isARActive 
                          ? const Color(AppColors.success)
                          : const Color(AppColors.warning),
                      width: 2,
                    ),
                  ),
                  child: Row(
                    children: [
                      AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _isARActive ? _pulseAnimation.value : 1.0,
                            child: Icon(
                              _isARActive ? Icons.visibility : Icons.visibility_off,
                              color: _isARActive 
                                  ? const Color(AppColors.success)
                                  : const Color(AppColors.warning),
                              size: 24,
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: AppSizes.paddingM),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _isARActive ? 'الواقع المعزز نشط' : 'الواقع المعزز غير نشط',
                              style: TextStyle(
                                color: _isARActive 
                                    ? const Color(AppColors.success)
                                    : const Color(AppColors.warning),
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _isARActive 
                                  ? 'وجه الكاميرا نحو المحتوى المطلوب'
                                  : 'اضغط على تجربة لبدء الواقع المعزز',
                              style: TextStyle(
                                color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_isARActive)
                        ElevatedButton(
                          onPressed: _stopAR,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(AppColors.error),
                            foregroundColor: const Color(AppColors.primaryWhite),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusM),
                            ),
                          ),
                          child: const Text('إيقاف'),
                        ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: AppSizes.paddingL),
              
              // AR Experiences Grid
              Expanded(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: GridView.builder(
                      padding: const EdgeInsets.all(AppSizes.paddingL),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        crossAxisSpacing: AppSizes.paddingM,
                        mainAxisSpacing: AppSizes.paddingM,
                        childAspectRatio: 0.9,
                      ),
                      itemCount: _arExperiences.length,
                      itemBuilder: (context, index) {
                        final experience = _arExperiences[index];
                        return _buildARExperienceCard(experience);
                      },
                    ),
                  ),
                ),
              ),
              
              // AR Camera View (when active)
              if (_isARActive) _buildARCameraView(),
              
              // Bottom Controls
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _calibrateAR,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppColors.info),
                          foregroundColor: const Color(AppColors.primaryWhite),
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusL),
                          ),
                        ),
                        icon: const Icon(Icons.tune),
                        label: const Text('معايرة'),
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingM),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _shareARExperience,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppColors.success),
                          foregroundColor: const Color(AppColors.primaryWhite),
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusL),
                          ),
                        ),
                        icon: const Icon(Icons.share),
                        label: const Text('مشاركة'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildARExperienceCard(Map<String, dynamic> experience) {
    final isSelected = experience['id'] == _selectedExperience;
    
    return Card(
      elevation: 12,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _startARExperience(experience),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(experience['color']).withValues(alpha: 0.1),
                const Color(AppColors.primaryWhite),
              ],
            ),
            border: Border.all(
              color: isSelected 
                  ? Color(experience['color'])
                  : Colors.transparent,
              width: 3,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon with Animation
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: isSelected ? _pulseAnimation.value : 1.0,
                      child: Container(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Color(experience['color']),
                              Color(experience['color']).withValues(alpha: 0.8),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                          boxShadow: [
                            BoxShadow(
                              color: Color(experience['color']).withValues(alpha: 0.3),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Icon(
                          experience['icon'],
                          size: 40,
                          color: const Color(AppColors.primaryWhite),
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: AppSizes.paddingM),
                
                // Title
                Text(
                  experience['title'],
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppColors.primaryBlack),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: AppSizes.paddingS),
                
                // Description
                Text(
                  experience['description'],
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(AppColors.grey),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: AppSizes.paddingM),
                
                // Start Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _startARExperience(experience),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(experience['color']),
                      foregroundColor: const Color(AppColors.primaryWhite),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      isSelected && _isARActive ? 'نشط' : 'ابدأ',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildARCameraView() {
    return Container(
      height: 200,
      margin: const EdgeInsets.all(AppSizes.paddingL),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryBlack),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        border: Border.all(
          color: const Color(AppColors.success),
          width: 3,
        ),
      ),
      child: Stack(
        children: [
          // Camera Preview (Mock)
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: const Icon(
                        Icons.camera_alt,
                        size: 60,
                        color: Color(AppColors.success),
                      ),
                    );
                  },
                ),
                const SizedBox(height: AppSizes.paddingM),
                const Text(
                  'عرض الكاميرا - الواقع المعزز',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppSizes.paddingS),
                Text(
                  'التجربة: ${_getExperienceTitle(_selectedExperience)}',
                  style: TextStyle(
                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // AR Overlay Elements
          Positioned(
            top: AppSizes.paddingM,
            left: AppSizes.paddingM,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingS,
                vertical: AppSizes.paddingXS,
              ),
              decoration: BoxDecoration(
                color: const Color(AppColors.success).withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(AppSizes.radiusS),
              ),
              child: const Text(
                'AR نشط',
                style: TextStyle(
                  color: Color(AppColors.primaryWhite),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          // AR Tracking Points
          ...List.generate(3, (index) {
            return Positioned(
              top: 50.0 + (index * 30),
              right: 20.0 + (index * 40),
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: 1.0 + (0.3 * _pulseAnimation.value),
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: const Color(AppColors.warning),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: const Color(AppColors.warning).withValues(alpha: 0.5),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  String _getExperienceTitle(String id) {
    final experience = _arExperiences.firstWhere(
      (exp) => exp['id'] == id,
      orElse: () => {'title': 'غير محدد'},
    );
    return experience['title'];
  }

  void _startARExperience(Map<String, dynamic> experience) {
    setState(() {
      _selectedExperience = experience['id'];
      _isARActive = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('بدء تجربة: ${experience['title']}'),
        backgroundColor: Color(experience['color']),
      ),
    );
  }

  void _stopAR() {
    setState(() {
      _isARActive = false;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إيقاف الواقع المعزز'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _calibrateAR() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معايرة الواقع المعزز'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.tune,
              size: 60,
              color: Color(AppColors.info),
            ),
            SizedBox(height: AppSizes.paddingM),
            Text(
              'يتم معايرة الكاميرا والمستشعرات...',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تم'),
          ),
        ],
      ),
    );
  }

  void _shareARExperience() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مشاركة تجربة الواقع المعزز'),
        backgroundColor: Color(AppColors.success),
      ),
    );
  }

  void _showARInfo() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'حول الواقع المعزز',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingL),
                    
                    const Text(
                      'تقنية الواقع المعزز في طب الأسنان تتيح لك:\n\n'
                      '• استكشاف تشريح الأسنان بشكل تفاعلي\n'
                      '• محاكاة العمليات الجراحية\n'
                      '• التدريب على الإجراءات المعقدة\n'
                      '• فهم أفضل للحالات المرضية\n\n'
                      'للحصول على أفضل تجربة، تأكد من:\n'
                      '• إضاءة جيدة\n'
                      '• مساحة كافية للحركة\n'
                      '• استقرار الجهاز',
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
