import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
} from '@mui/material';
import {
  People,
  Event,
  Payment,
  TrendingUp,
  Warning,
  CheckCircle,
  Schedule,
  Visibility,
  Edit,
  Notifications,
  MonitorHeart,
  Assessment,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import { adminAPI, monitoringAPI } from '../../services/api';

const AdminDashboard = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading } = useQuery({
    queryKey: ['adminDashboard'],
    queryFn: adminAPI.getDashboard,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch system health
  const { data: healthData, isLoading: healthLoading } = useQuery({
    queryKey: ['systemHealth'],
    queryFn: adminAPI.getSystemHealth,
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const dashboard = dashboardData?.data;
  const health = healthData?.data;

  // Quick stats
  const stats = [
    {
      title: t('totalUsers', 'إجمالي المستخدمين'),
      value: dashboard?.stats?.totalUsers || 0,
      change: dashboard?.stats?.usersGrowth || 0,
      icon: <People />,
      color: 'primary',
      action: () => navigate('/admin/users'),
    },
    {
      title: t('activeSubscriptions', 'الاشتراكات النشطة'),
      value: dashboard?.stats?.activeSubscriptions || 0,
      change: dashboard?.stats?.subscriptionsGrowth || 0,
      icon: <CheckCircle />,
      color: 'success',
      action: () => navigate('/admin/subscriptions'),
    },
    {
      title: t('totalRevenue', 'إجمالي الإيرادات'),
      value: `${dashboard?.stats?.totalRevenue || 0} ${t('currency', 'ريال')}`,
      change: dashboard?.stats?.revenueGrowth || 0,
      icon: <Payment />,
      color: 'info',
      action: () => navigate('/admin/reports'),
    },
    {
      title: t('systemHealth', 'صحة النظام'),
      value: `${health?.score || 0}%`,
      change: 0,
      icon: <MonitorHeart />,
      color: health?.status === 'healthy' ? 'success' : health?.status === 'degraded' ? 'warning' : 'error',
      action: () => navigate('/admin/monitoring'),
    },
  ];

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'PENDING_REVIEW':
        return 'warning';
      case 'APPROVED_PENDING_PAYMENT':
        return 'info';
      case 'ACTIVE':
        return 'success';
      case 'REJECTED':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" gutterBottom>
          {t('adminDashboard', 'لوحة تحكم الإدارة')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('adminDashboardDesc', 'نظرة عامة على حالة النظام والإحصائيات')}
        </Typography>
      </Box>

      {/* System Health Alert */}
      {health && health.status !== 'healthy' && (
        <Alert 
          severity={health.status === 'degraded' ? 'warning' : 'error'}
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={() => navigate('/admin/monitoring')}>
              {t('viewDetails', 'عرض التفاصيل')}
            </Button>
          }
        >
          <Typography variant="body1">
            {t('systemHealthIssue', 'تم اكتشاف مشاكل في صحة النظام')} - {t('score', 'النتيجة')}: {health.score}%
          </Typography>
          {health.recommendations && health.recommendations.length > 0 && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              {health.recommendations[0]}
            </Typography>
          )}
        </Alert>
      )}

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': { 
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
              onClick={stat.action}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    {stat.change !== 0 && (
                      <Box display="flex" alignItems="center" mt={1}>
                        <TrendingUp 
                          fontSize="small" 
                          color={stat.change > 0 ? 'success' : 'error'}
                        />
                        <Typography 
                          variant="body2" 
                          color={stat.change > 0 ? 'success.main' : 'error.main'}
                          sx={{ ml: 0.5 }}
                        >
                          {stat.change > 0 ? '+' : ''}{stat.change}%
                        </Typography>
                      </Box>
                    )}
                  </Box>
                  <Avatar sx={{ bgcolor: `${stat.color}.main` }}>
                    {stat.icon}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Subscriptions */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" component="h2">
                  {t('recentSubscriptions', 'الاشتراكات الأخيرة')}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/admin/subscriptions')}
                  endIcon={<Visibility />}
                >
                  {t('viewAll', 'عرض الكل')}
                </Button>
              </Box>

              {dashboardLoading ? (
                <LinearProgress />
              ) : dashboard?.recentSubscriptions?.length > 0 ? (
                <List>
                  {dashboard.recentSubscriptions.slice(0, 5).map((subscription, index) => (
                    <ListItem key={subscription.id} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <Avatar sx={{ width: 32, height: 32 }}>
                          {subscription.user?.arabicName?.charAt(0) || 'U'}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={subscription.user?.arabicName || subscription.user?.englishName}
                        secondary={`${subscription.amount} ${subscription.currency} - ${t(subscription.qualification)}`}
                      />
                      <ListItemSecondaryAction>
                        <Chip
                          label={t(subscription.status)}
                          size="small"
                          color={getStatusColor(subscription.status)}
                        />
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noRecentSubscriptions', 'لا توجد اشتراكات حديثة')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* System Performance */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                <Typography variant="h6" component="h2">
                  {t('systemPerformance', 'أداء النظام')}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/admin/monitoring')}
                  endIcon={<Visibility />}
                >
                  {t('viewDetails', 'عرض التفاصيل')}
                </Button>
              </Box>

              {healthLoading ? (
                <LinearProgress />
              ) : health ? (
                <Box>
                  {/* CPU Usage */}
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">
                        {t('cpuUsage', 'استخدام المعالج')}
                      </Typography>
                      <Typography variant="body2">
                        {health.checks?.cpu?.value || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={health.checks?.cpu?.value || 0}
                      color={health.checks?.cpu?.status === 'healthy' ? 'success' : 'warning'}
                    />
                  </Box>

                  {/* Memory Usage */}
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">
                        {t('memoryUsage', 'استخدام الذاكرة')}
                      </Typography>
                      <Typography variant="body2">
                        {health.checks?.memory?.value || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={health.checks?.memory?.value || 0}
                      color={health.checks?.memory?.status === 'healthy' ? 'success' : 'warning'}
                    />
                  </Box>

                  {/* Error Rate */}
                  <Box mb={2}>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">
                        {t('errorRate', 'معدل الأخطاء')}
                      </Typography>
                      <Typography variant="body2">
                        {health.checks?.errorRate?.value || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={health.checks?.errorRate?.value || 0}
                      color={health.checks?.errorRate?.status === 'healthy' ? 'success' : 'error'}
                    />
                  </Box>

                  {/* Response Time */}
                  <Box>
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">
                        {t('responseTime', 'زمن الاستجابة')}
                      </Typography>
                      <Typography variant="body2">
                        {health.checks?.responseTime?.value || 0}ms
                      </Typography>
                    </Box>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.min((health.checks?.responseTime?.value || 0) / 10, 100)}
                      color={health.checks?.responseTime?.status === 'healthy' ? 'success' : 'warning'}
                    />
                  </Box>
                </Box>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noPerformanceData', 'لا توجد بيانات أداء')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="h2" gutterBottom>
                {t('recentActivities', 'الأنشطة الأخيرة')}
              </Typography>

              {dashboardLoading ? (
                <LinearProgress />
              ) : dashboard?.recentActivities?.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>{t('user', 'المستخدم')}</TableCell>
                        <TableCell>{t('action', 'الإجراء')}</TableCell>
                        <TableCell>{t('details', 'التفاصيل')}</TableCell>
                        <TableCell>{t('time', 'الوقت')}</TableCell>
                        <TableCell>{t('actions', 'الإجراءات')}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dashboard.recentActivities.slice(0, 10).map((activity, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Avatar sx={{ width: 24, height: 24 }}>
                                {activity.user?.arabicName?.charAt(0) || 'U'}
                              </Avatar>
                              {activity.user?.arabicName || activity.user?.englishName || t('system', 'النظام')}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip label={t(activity.action)} size="small" />
                          </TableCell>
                          <TableCell>{activity.details}</TableCell>
                          <TableCell>
                            {new Date(activity.timestamp).toLocaleString('ar-SA')}
                          </TableCell>
                          <TableCell>
                            <IconButton size="small" onClick={() => navigate(`/admin/users/${activity.userId}`)}>
                              <Visibility fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noRecentActivities', 'لا توجد أنشطة حديثة')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('quickActions', 'إجراءات سريعة')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<People />}
              onClick={() => navigate('/admin/users')}
            >
              {t('manageUsers', 'إدارة المستخدمين')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Event />}
              onClick={() => navigate('/admin/content')}
            >
              {t('manageContent', 'إدارة المحتوى')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Assessment />}
              onClick={() => navigate('/admin/reports')}
            >
              {t('viewReports', 'عرض التقارير')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Notifications />}
              onClick={() => navigate('/admin/notifications')}
            >
              {t('sendNotifications', 'إرسال إشعارات')}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default AdminDashboard;
