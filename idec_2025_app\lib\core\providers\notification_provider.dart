import 'package:flutter/foundation.dart';
import '../services/notification_service.dart';

class NotificationProvider extends ChangeNotifier {
  final NotificationService _notificationService = NotificationService();
  
  List<LocalNotification> _notifications = [];
  List<LocalNotification> _history = [];
  bool _isInitialized = false;
  bool _permissionsGranted = false;

  // Getters
  List<LocalNotification> get notifications => _notifications;
  List<LocalNotification> get history => _history;
  bool get isInitialized => _isInitialized;
  bool get permissionsGranted => _permissionsGranted;
  
  int get unreadCount => _notifications.where((n) => !n.isShown).length;

  // Initialize notification provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _notificationService.initialize();
      _permissionsGranted = await _notificationService.requestPermissions();
      _isInitialized = true;
      
      // Load existing notifications
      await _loadNotifications();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing notification provider: $e');
    }
  }

  // Load notifications from service
  Future<void> _loadNotifications() async {
    _notifications = _notificationService.getPendingNotifications();
    _history = _notificationService.getNotificationHistory();
    notifyListeners();
  }

  // Schedule session notifications
  Future<void> scheduleSessionNotifications({
    required String sessionId,
    required String sessionTitle,
    required DateTime sessionTime,
  }) async {
    if (!_isInitialized) await initialize();

    await NotificationHelper.scheduleSessionReminders(
      sessionId: sessionId,
      sessionTitle: sessionTitle,
      sessionTime: sessionTime,
    );

    await _loadNotifications();
  }

  // Schedule course reminder
  Future<void> scheduleCourseReminder({
    required String courseId,
    required String courseTitle,
    required DateTime courseTime,
    int minutesBefore = 30,
  }) async {
    if (!_isInitialized) await initialize();

    await _notificationService.scheduleCourseReminder(
      courseId: courseId,
      courseTitle: courseTitle,
      courseTime: courseTime,
      minutesBefore: minutesBefore,
    );

    await _loadNotifications();
  }

  // Send news notification
  Future<void> sendNewsNotification({
    required String newsId,
    required String title,
    required String summary,
  }) async {
    if (!_isInitialized) await initialize();

    await _notificationService.sendNewsNotification(
      newsId: newsId,
      title: title,
      summary: summary,
    );

    await _loadNotifications();
  }

  // Send announcement
  Future<void> sendAnnouncement({
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    if (!_isInitialized) await initialize();

    await _notificationService.sendAnnouncement(
      title: title,
      message: message,
      data: data,
    );

    await _loadNotifications();
  }

  // Cancel notification
  Future<void> cancelNotification(String id) async {
    await _notificationService.cancelNotification(id);
    await _loadNotifications();
  }

  // Cancel session notifications
  Future<void> cancelSessionNotifications(String sessionId) async {
    await _notificationService.cancelSessionNotifications(sessionId);
    await _loadNotifications();
  }

  // Mark notification as read
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index].isShown = true;
      notifyListeners();
    }
  }

  // Mark all as read
  void markAllAsRead() {
    for (final notification in _notifications) {
      notification.isShown = true;
    }
    notifyListeners();
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    await _notificationService.clearAllNotifications();
    _notifications.clear();
    _history.clear();
    notifyListeners();
  }

  // Get notifications by type
  List<LocalNotification> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get today's notifications
  List<LocalNotification> getTodaysNotifications() {
    final today = DateTime.now();
    return _notifications.where((n) {
      return n.scheduledTime.year == today.year &&
             n.scheduledTime.month == today.month &&
             n.scheduledTime.day == today.day;
    }).toList();
  }

  // Get upcoming notifications
  List<LocalNotification> getUpcomingNotifications({int limit = 5}) {
    final upcoming = _notifications
        .where((n) => n.scheduledTime.isAfter(DateTime.now()) && !n.isShown)
        .toList();
    
    upcoming.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    
    return upcoming.take(limit).toList();
  }

  // Request permissions
  Future<bool> requestPermissions() async {
    _permissionsGranted = await _notificationService.requestPermissions();
    notifyListeners();
    return _permissionsGranted;
  }

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _notificationService.areNotificationsEnabled();
  }

  // Refresh notifications
  Future<void> refresh() async {
    await _loadNotifications();
  }

  @override
  void dispose() {
    _notificationService.dispose();
    super.dispose();
  }
}

// Notification settings provider
class NotificationSettingsProvider extends ChangeNotifier {
  bool _sessionReminders = true;
  bool _courseReminders = true;
  bool _newsNotifications = true;
  bool _socialNotifications = true;
  bool _gamificationNotifications = true;
  
  int _reminderMinutes = 15;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // Getters
  bool get sessionReminders => _sessionReminders;
  bool get courseReminders => _courseReminders;
  bool get newsNotifications => _newsNotifications;
  bool get socialNotifications => _socialNotifications;
  bool get gamificationNotifications => _gamificationNotifications;
  
  int get reminderMinutes => _reminderMinutes;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;

  // Setters
  void setSessionReminders(bool value) {
    _sessionReminders = value;
    notifyListeners();
  }

  void setCourseReminders(bool value) {
    _courseReminders = value;
    notifyListeners();
  }

  void setNewsNotifications(bool value) {
    _newsNotifications = value;
    notifyListeners();
  }

  void setSocialNotifications(bool value) {
    _socialNotifications = value;
    notifyListeners();
  }

  void setGamificationNotifications(bool value) {
    _gamificationNotifications = value;
    notifyListeners();
  }

  void setReminderMinutes(int minutes) {
    _reminderMinutes = minutes;
    notifyListeners();
  }

  void setSoundEnabled(bool value) {
    _soundEnabled = value;
    notifyListeners();
  }

  void setVibrationEnabled(bool value) {
    _vibrationEnabled = value;
    notifyListeners();
  }

  // Load settings from storage
  Future<void> loadSettings() async {
    // In a real app, load from SharedPreferences
    // For now, use default values
    notifyListeners();
  }

  // Save settings to storage
  Future<void> saveSettings() async {
    // In a real app, save to SharedPreferences
    // For now, just notify listeners
    notifyListeners();
  }

  // Reset to defaults
  void resetToDefaults() {
    _sessionReminders = true;
    _courseReminders = true;
    _newsNotifications = true;
    _socialNotifications = true;
    _gamificationNotifications = true;
    _reminderMinutes = 15;
    _soundEnabled = true;
    _vibrationEnabled = true;
    notifyListeners();
  }
}
