# دليل البدء السريع - IDEC 2025

## 🚀 البدء السريع (5 دقائق)

### المتطلبات الأساسية
- Node.js >= 18.0.0
- PostgreSQL >= 13
- Redis >= 6.0
- Git

### 1. تحضير البيئة

#### تثبيت Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# أو باستخدام nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

#### تثبيت PostgreSQL
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# بدء الخدمة
sudo systemctl start postgresql
sudo systemctl enable postgresql

# إنشاء قاعدة البيانات
sudo -u postgres createdb idec2025
sudo -u postgres createuser idec_user
sudo -u postgres psql -c "ALTER USER idec_user WITH ENCRYPTED PASSWORD '123456';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE idec2025 TO idec_user;"
```

#### تثبيت Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# بدء الخدمة
sudo systemctl start redis-server
sudo systemctl enable redis-server

# اختبار Redis
redis-cli ping
# يجب أن يرجع: PONG
```

### 2. إعداد المشروع

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-repo/idec2025-backend.git
cd idec2025-backend

# 2. تثبيت التبعيات
npm install

# 3. إعداد متغيرات البيئة
cp .env.example .env

# 4. تعديل ملف .env (مهم!)
nano .env
```

#### إعدادات .env الأساسية
```env
# Database
DATABASE_URL="postgresql://idec_user:secure_password@localhost:5432/idec2025?schema=public"

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Email (للاختبار)
MOCK_EMAIL=true
MOCK_SMS=true
MOCK_PAYMENT=true
```

### 3. إعداد قاعدة البيانات

```bash
# إنشاء الجداول
npm run db:migrate

# إنشاء البيانات التجريبية
npm run db:seed
```

### 4. تشغيل التطبيق

```bash
# بيئة التطوير (مع hot reload)
npm run dev

# أو بيئة الإنتاج
npm start
```

### 5. اختبار التطبيق

```bash
# فحص صحة التطبيق
curl http://localhost:3000/health

# اختبار API
curl http://localhost:3000/api/v1/

# عرض المقاييس
curl http://localhost:3000/metrics
```

## 🧪 بيانات الاختبار

### حسابات المدير
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: Admin123!@#
الدور: Super Admin
```

### حسابات الاختبار
```
طبيب:
البريد الإلكتروني: <EMAIL>
كلمة المرور: Test123!@#

طالب:
البريد الإلكتروني: <EMAIL>
كلمة المرور: Test123!@#
```

## 📡 اختبار API

### تسجيل الدخول
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin123!@#"
  }'
```

### الحصول على الملف الشخصي
```bash
# استخدم الـ token من الاستجابة السابقة
curl -X GET http://localhost:3000/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### عرض الجلسات
```bash
curl -X GET http://localhost:3000/api/v1/conference/sessions
```

## 🔧 أوامر مفيدة

### إدارة قاعدة البيانات
```bash
# عرض قاعدة البيانات بصرياً
npm run db:studio

# إعادة تعيين قاعدة البيانات
npm run db:migrate -- --reset
npm run db:seed

# إنشاء migration جديد
npx prisma migrate dev --name your_migration_name
```

### إدارة PM2
```bash
# بدء التطبيق مع PM2
npm run pm2:start

# مراقبة العمليات
pm2 monit

# عرض السجلات
pm2 logs idec2025-api

# إعادة تشغيل
npm run pm2:restart

# إيقاف التطبيق
npm run pm2:stop
```

### الاختبارات
```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --grep "auth"

# تقرير التغطية
npm run test:coverage
```

## 🐛 حل المشاكل الشائعة

### خطأ اتصال قاعدة البيانات
```bash
# تحقق من حالة PostgreSQL
sudo systemctl status postgresql

# تحقق من المستخدم وكلمة المرور
sudo -u postgres psql -c "\du"

# اختبار الاتصال
psql -h localhost -U idec_user -d idec2025
```

### خطأ اتصال Redis
```bash
# تحقق من حالة Redis
sudo systemctl status redis-server

# اختبار الاتصال
redis-cli ping

# عرض السجلات
sudo journalctl -u redis-server
```

### خطأ في المنافذ
```bash
# تحقق من المنافذ المستخدمة
sudo netstat -tlnp | grep :3000

# قتل العملية إذا لزم الأمر
sudo kill -9 $(lsof -t -i:3000)
```

### مشاكل الصلاحيات
```bash
# إعطاء صلاحيات للمجلدات
sudo chown -R $USER:$USER ./logs
sudo chown -R $USER:$USER ./uploads
sudo chmod -R 755 ./logs
sudo chmod -R 755 ./uploads
```

## 📊 مراقبة التطبيق

### السجلات
```bash
# عرض السجلات المباشرة
tail -f logs/combined.log

# عرض سجلات الأخطاء
tail -f logs/error.log

# عرض مقاييس الأداء
tail -f logs/metrics.log
```

### مراقبة الموارد
```bash
# استخدام الذاكرة
ps aux | grep node

# استخدام المعالج
top -p $(pgrep -f "node.*server.js")

# مساحة القرص الصلب
df -h
```

## 🔄 التحديث والنشر

### تحديث التبعيات
```bash
# تحديث جميع الحزم
npm update

# تحديث حزمة محددة
npm install package-name@latest

# فحص الثغرات الأمنية
npm audit
npm audit fix
```

### النشر
```bash
# بناء التطبيق للإنتاج
npm run build

# نشر باستخدام PM2
pm2 deploy production

# أو نشر يدوي
git pull origin main
npm install --production
npm run db:migrate
pm2 restart idec2025-api
```

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. **تحقق من السجلات**: `tail -f logs/error.log`
2. **راجع التوثيق**: `README.md`
3. **تحقق من GitHub Issues**: [رابط المشروع]
4. **تواصل مع الفريق**: <EMAIL>

## ✅ قائمة التحقق

- [ ] Node.js مثبت ويعمل
- [ ] PostgreSQL مثبت وقاعدة البيانات منشأة
- [ ] Redis مثبت ويعمل
- [ ] ملف .env معدّل بالقيم الصحيحة
- [ ] التبعيات مثبتة (`npm install`)
- [ ] قاعدة البيانات مهيأة (`npm run db:migrate`)
- [ ] البيانات التجريبية منشأة (`npm run db:seed`)
- [ ] التطبيق يعمل (`npm run dev`)
- [ ] API يستجيب (`curl http://localhost:3000/health`)

🎉 **مبروك! التطبيق جاهز للاستخدام**
