const express = require('express');
const { authenticate, requireAdmin } = require('../middleware/auth');
const adminController = require('../controllers/adminController');
const subscriptionController = require('../controllers/subscriptionController');
const monitoringController = require('../controllers/monitoringController');

const router = express.Router();

// Admin Dashboard and Overview
router.get('/dashboard', authenticate, requireAdmin, adminController.getAdminDashboard);

// User Management
router.get('/users', authenticate, requireAdmin, adminController.getAllUsers);
router.get('/users/:id', authenticate, requireAdmin, adminController.getUserDetails);
router.put('/users/:id/status', authenticate, requireAdmin, adminController.updateUserStatus);

// Subscription Management (delegated to subscription controller)
router.get('/subscriptions', authenticate, requireAdmin, subscriptionController.getAllSubscriptions);
router.get('/subscriptions/stats', authenticate, requireAdmin, subscriptionController.getSubscriptionStats);
router.get('/subscriptions/:id', authenticate, requireAdmin, subscriptionController.getSubscriptionById);
router.put('/subscriptions/:id/status', authenticate, requireAdmin, subscriptionController.updateSubscriptionStatus);
router.put('/subscriptions/:id/approve', authenticate, requireAdmin, subscriptionController.approveSubscription);
router.put('/subscriptions/:id/reject', authenticate, requireAdmin, subscriptionController.rejectSubscription);

// Payment Management
router.post('/payments/manual', authenticate, requireAdmin, adminController.createManualPayment);

// Reports and Analytics
router.get('/reports', authenticate, requireAdmin, adminController.getReports);

// System Management
router.get('/system/health', authenticate, requireAdmin, monitoringController.getHealthReport);
router.get('/system/metrics', authenticate, requireAdmin, monitoringController.getAdvancedMetrics);
router.get('/system/performance', authenticate, requireAdmin, monitoringController.getPerformanceInsights);

// Communication
router.post('/notifications/send', authenticate, requireAdmin, adminController.sendNotification);



module.exports = router;
