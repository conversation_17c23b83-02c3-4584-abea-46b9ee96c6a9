const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gamificationService = require('./gamificationService');

const prisma = new PrismaClient();

class ExhibitionService {
  // Get all exhibitors with filters
  async getExhibitors(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 20 } = pagination;
      const { category, search, isPremium, isActive = true } = filters;

      const where = { isActive };

      if (category) {
        where.category = category;
      }

      if (isPremium !== undefined) {
        where.isPremium = isPremium;
      }

      if (search) {
        where.OR = [
          { companyName: { contains: search, mode: 'insensitive' } },
          { companyNameAr: { contains: search, mode: 'insensitive' } },
          { companyNameEn: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      const [exhibitors, total] = await Promise.all([
        prisma.exhibitor.findMany({
          where,
          include: {
            booths: true,
            products: {
              where: { isActive: true },
              take: 3
            },
            offers: {
              where: {
                isActive: true,
                validFrom: { lte: new Date() },
                validTo: { gte: new Date() }
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: [
            { isPremium: 'desc' },
            { companyName: 'asc' }
          ]
        }),
        prisma.exhibitor.count({ where })
      ]);

      return {
        exhibitors,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting exhibitors:', error);
      throw error;
    }
  }

  // Get exhibitor by ID
  async getExhibitorById(id) {
    try {
      const exhibitor = await prisma.exhibitor.findUnique({
        where: { id },
        include: {
          booths: true,
          products: {
            where: { isActive: true }
          },
          offers: {
            where: {
              isActive: true,
              validFrom: { lte: new Date() },
              validTo: { gte: new Date() }
            }
          },
          contacts: true
        }
      });

      if (!exhibitor) {
        throw new Error('Exhibitor not found');
      }

      return exhibitor;
    } catch (error) {
      logger.error('Error getting exhibitor by ID:', error);
      throw error;
    }
  }

  // Get all booths with exhibitor info
  async getBooths(filters = {}) {
    try {
      const { hallName, search } = filters;

      const where = {};

      if (hallName) {
        where.hallName = hallName;
      }

      if (search) {
        where.OR = [
          { boothNumber: { contains: search, mode: 'insensitive' } },
          { exhibitor: {
            OR: [
              { companyName: { contains: search, mode: 'insensitive' } },
              { companyNameAr: { contains: search, mode: 'insensitive' } },
              { companyNameEn: { contains: search, mode: 'insensitive' } }
            ]
          }}
        ];
      }

      const booths = await prisma.booth.findMany({
        where,
        include: {
          exhibitor: {
            select: {
              id: true,
              companyName: true,
              companyNameAr: true,
              companyNameEn: true,
              logo: true,
              category: true,
              isPremium: true
            }
          }
        },
        orderBy: { boothNumber: 'asc' }
      });

      return booths;
    } catch (error) {
      logger.error('Error getting booths:', error);
      throw error;
    }
  }

  // Visit booth (track user visit)
  async visitBooth(userId, boothId, notes = null) {
    try {
      // Check if booth exists
      const booth = await prisma.booth.findUnique({
        where: { id: boothId },
        include: {
          exhibitor: {
            select: {
              companyName: true,
              companyNameEn: true
            }
          }
        }
      });

      if (!booth) {
        throw new Error('Booth not found');
      }

      // Check if user already visited this booth
      const existingVisit = await prisma.boothVisit.findUnique({
        where: {
          userId_boothId: {
            userId,
            boothId
          }
        }
      });

      if (existingVisit) {
        // Update existing visit
        const updatedVisit = await prisma.boothVisit.update({
          where: { id: existingVisit.id },
          data: {
            visitTime: new Date(),
            notes
          },
          include: {
            booth: {
              include: {
                exhibitor: {
                  select: {
                    companyName: true,
                    companyNameEn: true
                  }
                }
              }
            }
          }
        });

        return updatedVisit;
      } else {
        // Create new visit
        const visit = await prisma.boothVisit.create({
          data: {
            userId,
            boothId,
            notes
          },
          include: {
            booth: {
              include: {
                exhibitor: {
                  select: {
                    companyName: true,
                    companyNameEn: true
                  }
                }
              }
            }
          }
        });

        // Award points for booth visit
        await gamificationService.awardPoints(
          userId,
          'SOCIAL_INTERACTION',
          5,
          `Visited booth: ${booth.exhibitor.companyNameEn || booth.exhibitor.companyName}`
        );

        logger.info(`User ${userId} visited booth ${boothId}`);
        return visit;
      }
    } catch (error) {
      logger.error('Error visiting booth:', error);
      throw error;
    }
  }

  // Get user's booth visits
  async getUserBoothVisits(userId) {
    try {
      const visits = await prisma.boothVisit.findMany({
        where: { userId },
        include: {
          booth: {
            include: {
              exhibitor: {
                select: {
                  id: true,
                  companyName: true,
                  companyNameAr: true,
                  companyNameEn: true,
                  logo: true,
                  category: true
                }
              }
            }
          }
        },
        orderBy: { visitTime: 'desc' }
      });

      return visits;
    } catch (error) {
      logger.error('Error getting user booth visits:', error);
      throw error;
    }
  }

  // Get products with filters
  async getProducts(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 20 } = pagination;
      const { exhibitorId, category, search, isFeatured } = filters;

      const where = { isActive: true };

      if (exhibitorId) {
        where.exhibitorId = exhibitorId;
      }

      if (category) {
        where.category = category;
      }

      if (isFeatured !== undefined) {
        where.isFeatured = isFeatured;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { nameAr: { contains: search, mode: 'insensitive' } },
          { nameEn: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          include: {
            exhibitor: {
              select: {
                id: true,
                companyName: true,
                companyNameAr: true,
                companyNameEn: true,
                logo: true
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: [
            { isFeatured: 'desc' },
            { createdAt: 'desc' }
          ]
        }),
        prisma.product.count({ where })
      ]);

      return {
        products,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting products:', error);
      throw error;
    }
  }

  // Get special offers
  async getSpecialOffers(filters = {}) {
    try {
      const { exhibitorId, isActive = true } = filters;

      const where = {
        isActive,
        validFrom: { lte: new Date() },
        validTo: { gte: new Date() }
      };

      if (exhibitorId) {
        where.exhibitorId = exhibitorId;
      }

      const offers = await prisma.specialOffer.findMany({
        where,
        include: {
          exhibitor: {
            select: {
              id: true,
              companyName: true,
              companyNameAr: true,
              companyNameEn: true,
              logo: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return offers;
    } catch (error) {
      logger.error('Error getting special offers:', error);
      throw error;
    }
  }

  // Get exhibition statistics
  async getExhibitionStats() {
    try {
      const [
        totalExhibitors,
        totalBooths,
        totalProducts,
        activeOffers,
        totalVisits,
        categoriesStats
      ] = await Promise.all([
        prisma.exhibitor.count({ where: { isActive: true } }),
        prisma.booth.count({ where: { isActive: true } }),
        prisma.product.count({ where: { isActive: true } }),
        prisma.specialOffer.count({
          where: {
            isActive: true,
            validFrom: { lte: new Date() },
            validTo: { gte: new Date() }
          }
        }),
        prisma.boothVisit.count(),
        prisma.exhibitor.groupBy({
          by: ['category'],
          where: { isActive: true },
          _count: { category: true }
        })
      ]);

      return {
        totalExhibitors,
        totalBooths,
        totalProducts,
        activeOffers,
        totalVisits,
        categoriesStats
      };
    } catch (error) {
      logger.error('Error getting exhibition stats:', error);
      throw error;
    }
  }

  // Search across exhibition (exhibitors, products, offers)
  async searchExhibition(query, filters = {}) {
    try {
      const { category } = filters;

      const searchConditions = {
        OR: [
          { companyName: { contains: query, mode: 'insensitive' } },
          { companyNameAr: { contains: query, mode: 'insensitive' } },
          { companyNameEn: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } }
        ]
      };

      if (category) {
        searchConditions.category = category;
      }

      const [exhibitors, products] = await Promise.all([
        prisma.exhibitor.findMany({
          where: {
            isActive: true,
            ...searchConditions
          },
          include: {
            booths: true
          },
          take: 10
        }),
        prisma.product.findMany({
          where: {
            isActive: true,
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { nameAr: { contains: query, mode: 'insensitive' } },
              { nameEn: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } }
            ]
          },
          include: {
            exhibitor: {
              select: {
                companyName: true,
                companyNameEn: true,
                logo: true
              }
            }
          },
          take: 10
        })
      ]);

      return {
        exhibitors,
        products
      };
    } catch (error) {
      logger.error('Error searching exhibition:', error);
      throw error;
    }
  }
}

module.exports = new ExhibitionService();
