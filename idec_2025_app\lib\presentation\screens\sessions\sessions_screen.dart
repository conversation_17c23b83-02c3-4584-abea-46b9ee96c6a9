import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class SessionsScreen extends StatefulWidget {
  const SessionsScreen({super.key});

  @override
  State<SessionsScreen> createState() => _SessionsScreenState();
}

class _SessionsScreenState extends State<SessionsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _selectedFilter = 'الكل';
  String _selectedDay = 'الكل';
  String _selectedLevel = 'الكل';
  String _selectedTrack = 'الكل';

  final List<String> _filters = ['الكل', 'مسجل', 'متاح', 'ممتلئ'];
  final List<String> _days = ['الكل', 'اليوم الأول', 'اليوم الثاني', 'اليوم الثالث'];
  final List<String> _levels = ['الكل', 'مبتدئ', 'متوسط', 'متقدم'];
  final List<String> _tracks = ['الكل', 'جراحة', 'تقويم', 'علاج جذور', 'أطفال', 'تجميل'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 320,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'جلسات المؤتمر',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                30 * _animationController.value,
                                -20 * _animationController.value,
                              ),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/sessions_pattern.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                      // Content
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60),

                            // Sessions Icon with Animation
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: SlideTransition(
                                position: _slideAnimation,
                                child: Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.event_note,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: AppSizes.paddingM),

                            // Stats Row
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('120+', 'جلسة علمية'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('50+', 'متحدث خبير'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('24', 'ساعة تعليم'),
                                ],
                              ),
                            ),

                            const SizedBox(height: AppSizes.paddingM),

                            // Quick Filter Chips
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: _filters.map((filter) {
                                    final isSelected = filter == _selectedFilter;
                                    return Container(
                                      margin: const EdgeInsets.only(right: AppSizes.paddingS),
                                      child: FilterChip(
                                        label: Text(filter),
                                        selected: isSelected,
                                        onSelected: (selected) {
                                          setState(() {
                                            _selectedFilter = filter;
                                          });
                                        },
                                        backgroundColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                        selectedColor: const Color(AppColors.primaryWhite),
                                        labelStyle: TextStyle(
                                          color: isSelected
                                              ? const Color(AppColors.primaryRed)
                                              : const Color(AppColors.primaryWhite),
                                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                tabs: const [
                  Tab(text: 'الكل'),
                  Tab(text: 'اليوم الأول'),
                  Tab(text: 'اليوم الثاني'),
                  Tab(text: 'اليوم الثالث'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Advanced Filters
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Row(
                children: [
                  Expanded(
                    child: _buildFilterDropdown(
                      'المستوى',
                      _selectedLevel,
                      _levels,
                      (value) => setState(() => _selectedLevel = value!),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildFilterDropdown(
                      'التخصص',
                      _selectedTrack,
                      _tracks,
                      (value) => setState(() => _selectedTrack = value!),
                    ),
                  ),
                  IconButton(
                    onPressed: _showAdvancedFilters,
                    icon: const Icon(
                      Icons.tune,
                      color: Color(AppColors.primaryRed),
                    ),
                    tooltip: 'فلترة متقدمة',
                  ),
                ],
              ),
            ),

            // Sessions Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildSessionsList(0),
                    _buildSessionsList(1),
                    _buildSessionsList(2),
                    _buildSessionsList(3),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showMySchedule,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.schedule),
        label: const Text('جدولي'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Text(label),
          isExpanded: true,
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildSessionsList(int dayIndex) {
    final sessions = _getSessionsForDay(dayIndex);

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: sessions.length,
      itemBuilder: (context, index) {
        final session = sessions[index];
        return _buildSessionCard(session, index);
      },
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session, int index) {
    final isRegistered = session['isRegistered'] ?? false;
    final isFull = session['isFull'] ?? false;
    final isLive = session['isLive'] ?? false;
    final level = session['level'] ?? 'متوسط';
    final track = session['track'] ?? 'عام';

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
            border: Border.all(
              color: isLive
                  ? const Color(AppColors.success)
                  : isFull
                      ? const Color(AppColors.error)
                      : const Color(AppColors.primaryRed).withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              // Session Header
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isLive
                        ? [const Color(AppColors.success), const Color(AppColors.success).withValues(alpha: 0.8)]
                        : isFull
                            ? [const Color(AppColors.error), const Color(AppColors.error).withValues(alpha: 0.8)]
                            : [const Color(AppColors.primaryRed), const Color(AppColors.darkRed)],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppSizes.radiusL),
                    topRight: Radius.circular(AppSizes.radiusL),
                  ),
                ),
                child: Row(
                  children: [
                    // Time and Room
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.access_time,
                                color: Color(AppColors.primaryWhite),
                                size: AppSizes.iconS,
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                session['time'],
                                style: const TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSizes.paddingXS),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on,
                                color: Color(AppColors.primaryWhite),
                                size: AppSizes.iconS,
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                session['room'],
                                style: TextStyle(
                                  color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Status Badges
                    Column(
                      children: [
                        if (isLive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.primaryWhite),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: const BoxDecoration(
                                    color: Color(AppColors.success),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: AppSizes.paddingXS),
                                const Text(
                                  'مباشر',
                                  style: TextStyle(
                                    color: Color(AppColors.success),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                        if (isFull && !isLive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.primaryWhite),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: const Text(
                              'ممتلئ',
                              style: TextStyle(
                                color: Color(AppColors.error),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                        const SizedBox(height: AppSizes.paddingXS),

                        // Capacity
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            '${session['registered']}/${session['capacity']}',
                            style: const TextStyle(
                              color: Color(AppColors.primaryWhite),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Session Content
              Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and Level
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            session['title'],
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(AppColors.primaryBlack),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: _getLevelColor(level).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            level,
                            style: TextStyle(
                              color: _getLevelColor(level),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    // Track
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: AppSizes.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                      child: Text(
                        track,
                        style: const TextStyle(
                          color: Color(AppColors.primaryBlack),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    // Description
                    Text(
                      session['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(AppColors.grey),
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    // Speaker
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                          child: const Icon(
                            Icons.person,
                            size: 20,
                            color: Color(AppColors.primaryRed),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                session['speaker'],
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: const Color(AppColors.primaryBlack),
                                ),
                              ),
                              Text(
                                session['speakerTitle'],
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: const Color(AppColors.grey),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.warning).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.school,
                                size: 12,
                                color: Color(AppColors.warning),
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                '${session['credits']} ساعة',
                                style: const TextStyle(
                                  color: Color(AppColors.warning),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: ElevatedButton.icon(
                            onPressed: isFull ? null : () => _registerForSession(session),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isRegistered
                                  ? const Color(AppColors.success)
                                  : isFull
                                      ? const Color(AppColors.grey)
                                      : const Color(AppColors.primaryRed),
                              foregroundColor: const Color(AppColors.primaryWhite),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            icon: Icon(
                              isRegistered ? Icons.check : isFull ? Icons.block : Icons.add,
                              size: AppSizes.iconS,
                            ),
                            label: Text(
                              isRegistered ? 'مسجل' : isFull ? 'ممتلئ' : 'تسجيل',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ),

                        const SizedBox(width: AppSizes.paddingS),

                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => _showSessionDetails(session),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: const Color(AppColors.primaryRed),
                              side: const BorderSide(color: Color(AppColors.primaryRed)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            icon: const Icon(Icons.info_outline, size: AppSizes.iconS),
                            label: const Text('تفاصيل', style: TextStyle(fontSize: 14)),
                          ),
                        ),

                        const SizedBox(width: AppSizes.paddingS),

                        IconButton(
                          onPressed: () => _addToFavorites(session),
                          icon: Icon(
                            session['isFavorite'] ? Icons.favorite : Icons.favorite_border,
                            color: session['isFavorite']
                                ? const Color(AppColors.primaryRed)
                                : const Color(AppColors.grey),
                          ),
                        ),

                        IconButton(
                          onPressed: () => _shareSession(session),
                          icon: const Icon(
                            Icons.share,
                            color: Color(AppColors.primaryRed),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case 'مبتدئ':
        return const Color(AppColors.success);
      case 'متوسط':
        return const Color(AppColors.warning);
      case 'متقدم':
        return const Color(AppColors.error);
      default:
        return const Color(AppColors.primaryBlack);
    }
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Text(
                'فلترة متقدمة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Filters content would go here
            const Expanded(
              child: Center(
                child: Text('فلاتر متقدمة قريباً...'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMySchedule() {
    Navigator.of(context).pushNamed('/agenda');
  }

  void _registerForSession(Map<String, dynamic> session) {
    setState(() {
      session['isRegistered'] = true;
      session['registered'] = (session['registered'] ?? 0) + 1;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم التسجيل في جلسة: ${session['title']}'),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _showSessionDetails(Map<String, dynamic> session) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      session['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      'وصف الجلسة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    Text(
                      session['fullDescription'] ?? session['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addToFavorites(Map<String, dynamic> session) {
    setState(() {
      session['isFavorite'] = !(session['isFavorite'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          session['isFavorite']
              ? 'تم إضافة الجلسة للمفضلة'
              : 'تم إزالة الجلسة من المفضلة',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _shareSession(Map<String, dynamic> session) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة جلسة: ${session['title']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  List<Map<String, dynamic>> _getSessionsForDay(int dayIndex) {
    // Mock data for sessions
    final allSessions = [
      // All sessions
      [
        {
          'title': 'أحدث تقنيات زراعة الأسنان',
          'description': 'استعراض شامل لأحدث التقنيات في مجال زراعة الأسنان والتطورات الحديثة',
          'speaker': 'د. أحمد محمد علي',
          'speakerTitle': 'استشاري جراحة الفم والوجه والفكين',
          'time': '09:00 - 10:30',
          'room': 'القاعة الرئيسية',
          'level': 'متقدم',
          'track': 'جراحة',
          'credits': 1.5,
          'capacity': 200,
          'registered': 180,
          'isRegistered': true,
          'isFull': false,
          'isLive': false,
          'isFavorite': true,
        },
        {
          'title': 'التقويم الشفاف والتقنيات الحديثة',
          'description': 'مقدمة شاملة عن التقويم الشفاف وأحدث التقنيات المستخدمة',
          'speaker': 'د. محمد عبدالله الزهراني',
          'speakerTitle': 'استشاري تقويم الأسنان',
          'time': '11:00 - 12:30',
          'room': 'قاعة المحاضرات A',
          'level': 'متوسط',
          'track': 'تقويم',
          'credits': 1.5,
          'capacity': 150,
          'registered': 150,
          'isRegistered': false,
          'isFull': true,
          'isLive': false,
          'isFavorite': false,
        },
        {
          'title': 'علاج الجذور بالمجهر',
          'description': 'تقنيات متقدمة في علاج الجذور باستخدام المجهر الجراحي',
          'speaker': 'د. عبدالرحمن علي محمد',
          'speakerTitle': 'استشاري علاج الجذور',
          'time': '14:00 - 15:30',
          'room': 'قاعة المحاضرات B',
          'level': 'متقدم',
          'track': 'علاج جذور',
          'credits': 1.5,
          'capacity': 100,
          'registered': 85,
          'isRegistered': true,
          'isFull': false,
          'isLive': true,
          'isFavorite': false,
        },
      ],
      // Day 1 sessions
      [
        {
          'title': 'طب أسنان الأطفال الحديث',
          'description': 'أساليب حديثة في علاج أسنان الأطفال والتعامل مع القلق',
          'speaker': 'د. فاطمة أحمد حسن',
          'speakerTitle': 'أستاذة طب أسنان الأطفال',
          'time': '09:00 - 10:30',
          'room': 'قاعة المحاضرات A',
          'level': 'مبتدئ',
          'track': 'أطفال',
          'credits': 1.5,
          'capacity': 120,
          'registered': 95,
          'isRegistered': false,
          'isFull': false,
          'isLive': false,
          'isFavorite': true,
        },
      ],
      // Day 2 sessions
      [
        {
          'title': 'جراحة اللثة التجميلية',
          'description': 'تقنيات متقدمة في جراحة اللثة التجميلية والنتائج المتوقعة',
          'speaker': 'د. نورا أحمد سالم',
          'speakerTitle': 'استشارية طب اللثة',
          'time': '09:00 - 10:30',
          'room': 'قاعة المحاضرات B',
          'level': 'متقدم',
          'track': 'تجميل',
          'credits': 1.5,
          'capacity': 80,
          'registered': 70,
          'isRegistered': true,
          'isFull': false,
          'isLive': false,
          'isFavorite': false,
        },
      ],
      // Day 3 sessions
      [
        {
          'title': 'المواد السنية المتقدمة',
          'description': 'استعراض أحدث المواد السنية وتطبيقاتها العملية',
          'speaker': 'د. سارة محمد قاسم',
          'speakerTitle': 'باحثة في علوم المواد السنية',
          'time': '09:00 - 10:30',
          'room': 'قاعة المحاضرات A',
          'level': 'متوسط',
          'track': 'مواد',
          'credits': 1.5,
          'capacity': 100,
          'registered': 60,
          'isRegistered': false,
          'isFull': false,
          'isLive': false,
          'isFavorite': false,
        },
      ],
    ];

    return allSessions[dayIndex];
  }
}