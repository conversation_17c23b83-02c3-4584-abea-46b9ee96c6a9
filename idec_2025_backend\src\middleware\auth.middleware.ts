import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { getPrisma } from '@/config/database';
import { config } from '@/config/config';
import { logger, logSecurityEvent } from '@/utils/logger';
import { ApiError } from '@/utils/ApiError';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role?: string;
        status: string;
      };
    }
  }
}

// JWT payload interface
interface JwtPayload {
  userId: string;
  email: string;
  role?: string;
  iat: number;
  exp: number;
}

// Authentication middleware
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logSecurityEvent(
        'Authentication failed - No token provided',
        'medium',
        { ip: req.ip, userAgent: req.get('User-Agent') }
      );
      throw new ApiError(401, 'Access token is required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;

    // Get user from database
    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        status: true,
        emailVerified: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      logSecurityEvent(
        'Authentication failed - User not found',
        'high',
        { userId: decoded.userId, ip: req.ip }
      );
      throw new ApiError(401, 'Invalid token - user not found');
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      logSecurityEvent(
        'Authentication failed - User not active',
        'medium',
        { userId: user.id, status: user.status, ip: req.ip }
      );
      throw new ApiError(401, 'Account is not active');
    }

    // Check if email is verified
    if (!user.emailVerified) {
      throw new ApiError(401, 'Email verification required');
    }

    // Add user to request object
    req.user = {
      id: user.id,
      email: user.email,
      status: user.status,
    };

    // Update last login time (optional, can be done in login endpoint)
    // await prisma.user.update({
    //   where: { id: user.id },
    //   data: { lastLoginAt: new Date() },
    // });

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logSecurityEvent(
        'Authentication failed - Invalid token',
        'medium',
        { error: error.message, ip: req.ip }
      );
      next(new ApiError(401, 'Invalid token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      logSecurityEvent(
        'Authentication failed - Token expired',
        'low',
        { ip: req.ip }
      );
      next(new ApiError(401, 'Token expired'));
    } else {
      next(error);
    }
  }
};

// Optional authentication middleware (doesn't throw error if no token)
export const optionalAuthenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;

    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        status: true,
        emailVerified: true,
      },
    });

    if (user && user.status === 'ACTIVE' && user.emailVerified) {
      req.user = {
        id: user.id,
        email: user.email,
        status: user.status,
      };
    }

    next();
  } catch (error) {
    // Ignore authentication errors in optional middleware
    next();
  }
};

// Authorization middleware for specific roles
export const authorize = (...allowedRoles: string[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'Authentication required');
      }

      // Get user with role information
      const prisma = getPrisma();
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
          id: true,
          email: true,
          status: true,
          // Add role field when implemented
        },
      });

      if (!user) {
        throw new ApiError(401, 'User not found');
      }

      // For now, we'll implement basic role checking
      // This can be extended when role system is implemented
      const userRole = 'USER'; // Default role
      
      if (!allowedRoles.includes(userRole)) {
        logSecurityEvent(
          'Authorization failed - Insufficient permissions',
          'medium',
          { 
            userId: req.user.id,
            requiredRoles: allowedRoles,
            userRole,
            ip: req.ip 
          }
        );
        throw new ApiError(403, 'Insufficient permissions');
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Admin authorization middleware
export const requireAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'Authentication required');
    }

    // Check if user is admin (implement your admin logic here)
    const isAdmin = req.user.email === config.admin.email;
    
    if (!isAdmin) {
      logSecurityEvent(
        'Admin authorization failed',
        'high',
        { userId: req.user.id, ip: req.ip }
      );
      throw new ApiError(403, 'Admin access required');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Rate limiting by user
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const now = Date.now();
    const userRequests = requests.get(userId);

    if (!userRequests || now > userRequests.resetTime) {
      requests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    if (userRequests.count >= maxRequests) {
      logSecurityEvent(
        'Rate limit exceeded',
        'medium',
        { userId, maxRequests, windowMs, ip: req.ip }
      );
      throw new ApiError(429, 'Too many requests');
    }

    userRequests.count++;
    next();
  };
};

// Subscription status check middleware
export const requireActiveSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'Authentication required');
    }

    const prisma = getPrisma();
    const activeSubscription = await prisma.subscription.findFirst({
      where: {
        userId: req.user.id,
        status: 'PAID',
      },
    });

    if (!activeSubscription) {
      throw new ApiError(403, 'Active subscription required');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Email verification check middleware
export const requireEmailVerification = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new ApiError(401, 'Authentication required');
    }

    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { emailVerified: true },
    });

    if (!user?.emailVerified) {
      throw new ApiError(403, 'Email verification required');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Generate JWT token
export const generateToken = (payload: {
  userId: string;
  email: string;
  role?: string;
}): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

// Generate refresh token
export const generateRefreshToken = (payload: {
  userId: string;
  email: string;
}): string => {
  return jwt.sign(payload, config.jwt.refreshSecret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });
};

// Verify refresh token
export const verifyRefreshToken = (token: string): JwtPayload => {
  return jwt.verify(token, config.jwt.refreshSecret) as JwtPayload;
};
