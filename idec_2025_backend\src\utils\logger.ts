import winston from 'winston';
import path from 'path';
import { config } from '@/config/config';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`,
  ),
);

// Define which transports the logger must use
const transports = [
  // Console transport
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
    ),
  }),
];

// Add file transport if not in test environment
if (config.nodeEnv !== 'test') {
  // Ensure logs directory exists
  const fs = require('fs');
  const logsDir = path.dirname(config.logging.file);
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  transports.push(
    // File transport for all logs
    new winston.transports.File({
      filename: config.logging.file,
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.json(),
      ),
    }),
    
    // Separate file for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: winston.format.combine(
        winston.format.uncolorize(),
        winston.format.json(),
      ),
    }),
  );
}

// Create the logger
const logger = winston.createLogger({
  level: config.logging.level,
  levels,
  format,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
export const logError = (message: string, error?: any, metadata?: any) => {
  logger.error(message, {
    error: error?.message || error,
    stack: error?.stack,
    ...metadata,
  });
};

export const logInfo = (message: string, metadata?: any) => {
  logger.info(message, metadata);
};

export const logWarn = (message: string, metadata?: any) => {
  logger.warn(message, metadata);
};

export const logDebug = (message: string, metadata?: any) => {
  logger.debug(message, metadata);
};

export const logHttp = (message: string, metadata?: any) => {
  logger.http(message, metadata);
};

// Performance logging helper
export const logPerformance = (operation: string, startTime: number, metadata?: any) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation} completed in ${duration}ms`, {
    operation,
    duration,
    ...metadata,
  });
};

// Database query logging helper
export const logDatabaseQuery = (query: string, duration: number, metadata?: any) => {
  if (config.development.debug) {
    logger.debug(`Database Query: ${query} (${duration}ms)`, {
      query,
      duration,
      ...metadata,
    });
  }
};

// API request logging helper
export const logApiRequest = (
  method: string,
  url: string,
  statusCode: number,
  duration: number,
  userId?: string,
  metadata?: any
) => {
  logger.http(`${method} ${url} ${statusCode} - ${duration}ms`, {
    method,
    url,
    statusCode,
    duration,
    userId,
    ...metadata,
  });
};

// Security event logging helper
export const logSecurityEvent = (
  event: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  details: any
) => {
  const logLevel = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
  logger[logLevel](`Security Event: ${event}`, {
    event,
    severity,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

// Business logic logging helper
export const logBusinessEvent = (
  event: string,
  entityType: string,
  entityId: string,
  userId?: string,
  metadata?: any
) => {
  logger.info(`Business Event: ${event}`, {
    event,
    entityType,
    entityId,
    userId,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Payment logging helper
export const logPaymentEvent = (
  event: string,
  paymentId: string,
  amount: number,
  currency: string,
  userId: string,
  metadata?: any
) => {
  logger.info(`Payment Event: ${event}`, {
    event,
    paymentId,
    amount,
    currency,
    userId,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Email logging helper
export const logEmailEvent = (
  event: string,
  to: string,
  subject: string,
  success: boolean,
  metadata?: any
) => {
  const logLevel = success ? 'info' : 'error';
  logger[logLevel](`Email Event: ${event}`, {
    event,
    to,
    subject,
    success,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// SMS logging helper
export const logSmsEvent = (
  event: string,
  to: string,
  success: boolean,
  metadata?: any
) => {
  const logLevel = success ? 'info' : 'error';
  logger[logLevel](`SMS Event: ${event}`, {
    event,
    to,
    success,
    timestamp: new Date().toISOString(),
    ...metadata,
  });
};

// Export the logger
export { logger };
export default logger;
