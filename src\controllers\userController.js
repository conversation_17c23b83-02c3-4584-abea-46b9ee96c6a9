const { body, param } = require('express-validator');
const { asyncHand<PERSON> } = require('../middleware/errorHandler');
const { handleValidationErrors, createPaginationResult } = require('../middleware/validation');
const userService = require('../services/userService');
const logger = require('../utils/logger');

// Validation rules
const updateProfileValidation = [
  body('arabicName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .matches(/^[\u0600-\u06FF\s]+$/)
    .withMessage('الاسم العربي يجب أن يحتوي على أحرف عربية فقط'),
  
  body('englishName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('الاسم الإنجليزي يجب أن يحتوي على أحرف إنجليزية فقط'),
  
  body('phone')
    .optional()
    .matches(/^\+966[0-9]{9}$/)
    .withMessage('رقم الهاتف يجب أن يكون بالصيغة: +966xxxxxxxxx'),
  
  body('specialization')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('التخصص يجب أن يكون بين 2 و 100 حرف'),
  
  body('university')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('الجامعة يجب أن تكون بين 2 و 200 حرف'),
  
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('تاريخ الميلاد غير صحيح'),
  
  handleValidationErrors
];

const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),
  
  body('newPassword')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور الجديدة يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة ورقم ورمز خاص'),
  
  handleValidationErrors
];

// Get user profile
const getProfile = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  const user = await userService.findUserById(userId);
  
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'المستخدم غير موجود'
    });
  }

  logger.logUserAction(userId, 'PROFILE_VIEWED');

  res.json({
    success: true,
    data: {
      user
    }
  });
});

// Update user profile
const updateProfile = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const updateData = req.body;

  // Remove fields that shouldn't be updated directly
  delete updateData.email;
  delete updateData.password;
  delete updateData.passwordHash;
  delete updateData.status;
  delete updateData.emailVerified;
  delete updateData.phoneVerified;

  const updatedUser = await userService.updateUser(userId, updateData);

  logger.logUserAction(userId, 'PROFILE_UPDATED', updateData);

  res.json({
    success: true,
    message: 'تم تحديث الملف الشخصي بنجاح',
    data: {
      user: updatedUser
    }
  });
});

// Change password
const changePassword = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { currentPassword, newPassword } = req.body;

  const user = await userService.findUserById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'المستخدم غير موجود'
    });
  }

  // Verify current password
  const bcrypt = require('bcryptjs');
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
  
  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      success: false,
      message: 'كلمة المرور الحالية غير صحيحة'
    });
  }

  // Hash new password
  const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
  const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

  // Update password
  await userService.updateUser(userId, { passwordHash: newPasswordHash });

  logger.logUserAction(userId, 'PASSWORD_CHANGED');

  res.json({
    success: true,
    message: 'تم تغيير كلمة المرور بنجاح'
  });
});

// Get subscription status
const getSubscriptionStatus = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement subscription status logic
  logger.logUserAction(userId, 'SUBSCRIPTION_STATUS_VIEWED');

  res.json({
    success: true,
    data: {
      status: 'PENDING_REVIEW',
      message: 'Subscription status endpoint - Coming soon'
    }
  });
});

// Submit conference subscription
const submitSubscription = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement subscription submission logic
  logger.logUserAction(userId, 'SUBSCRIPTION_SUBMITTED');

  res.json({
    success: true,
    message: 'تم إرسال طلب الاشتراك بنجاح',
    data: {
      message: 'Subscription submission endpoint - Coming soon'
    }
  });
});

// Upload document
const uploadDocument = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { documentType } = req.body;

  // TODO: Implement document upload logic
  logger.logUserAction(userId, 'DOCUMENT_UPLOADED', { documentType });

  res.json({
    success: true,
    message: 'تم رفع الوثيقة بنجاح',
    data: {
      message: 'Document upload endpoint - Coming soon'
    }
  });
});

// Get payment history
const getPaymentHistory = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement payment history logic
  logger.logUserAction(userId, 'PAYMENT_HISTORY_VIEWED');

  res.json({
    success: true,
    data: {
      payments: [],
      message: 'Payment history endpoint - Coming soon'
    }
  });
});

// Get certificates
const getCertificates = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement certificates logic
  logger.logUserAction(userId, 'CERTIFICATES_VIEWED');

  res.json({
    success: true,
    data: {
      certificates: [],
      message: 'Certificates endpoint - Coming soon'
    }
  });
});

// Get user notifications
const getNotifications = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 10 } = req.query;

  // TODO: Implement notifications logic
  logger.logUserAction(userId, 'NOTIFICATIONS_VIEWED');

  const paginationResult = createPaginationResult(parseInt(page), parseInt(limit), 0);

  res.json({
    success: true,
    data: {
      notifications: [],
      pagination: paginationResult,
      message: 'Notifications endpoint - Coming soon'
    }
  });
});

// Mark notification as read
const markNotificationRead = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { notificationId } = req.params;

  // TODO: Implement mark notification as read logic
  logger.logUserAction(userId, 'NOTIFICATION_READ', { notificationId });

  res.json({
    success: true,
    message: 'تم تحديد الإشعار كمقروء',
    data: {
      message: 'Mark notification read endpoint - Coming soon'
    }
  });
});

// Delete account (soft delete)
const deleteAccount = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement account deletion logic (soft delete)
  logger.logUserAction(userId, 'ACCOUNT_DELETION_REQUESTED');

  res.json({
    success: true,
    message: 'تم إرسال طلب حذف الحساب',
    data: {
      message: 'Account deletion endpoint - Coming soon'
    }
  });
});

module.exports = {
  getProfile,
  updateProfile: [updateProfileValidation, updateProfile],
  changePassword: [changePasswordValidation, changePassword],
  getSubscriptionStatus,
  submitSubscription,
  uploadDocument,
  getPaymentHistory,
  getCertificates,
  getNotifications,
  markNotificationRead,
  deleteAccount
};
