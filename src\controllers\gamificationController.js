const gamificationService = require('../services/gamificationService');
const logger = require('../utils/logger');

class GamificationController {
  // Get user's gamification summary
  async getUserSummary(req, res) {
    try {
      const userId = req.user.id;
      const summary = await gamificationService.getUserGamificationSummary(userId);

      res.json({
        success: true,
        data: summary
      });
    } catch (error) {
      logger.error('Error getting user gamification summary:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get gamification summary',
          code: 'GAMIFICATION_SUMMARY_ERROR'
        }
      });
    }
  }

  // Get user's points history
  async getPointsHistory(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 50 } = req.query;

      const history = await gamificationService.getUserPointHistory(userId, parseInt(limit));

      res.json({
        success: true,
        data: history
      });
    } catch (error) {
      logger.error('Error getting points history:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get points history',
          code: 'POINTS_HISTORY_ERROR'
        }
      });
    }
  }

  // Get user's badges
  async getUserBadges(req, res) {
    try {
      const userId = req.user.id;
      const badges = await gamificationService.getUserBadges(userId);

      res.json({
        success: true,
        data: badges
      });
    } catch (error) {
      logger.error('Error getting user badges:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get user badges',
          code: 'USER_BADGES_ERROR'
        }
      });
    }
  }

  // Get user's achievements
  async getUserAchievements(req, res) {
    try {
      const userId = req.user.id;
      const achievements = await gamificationService.getUserAchievements(userId);

      res.json({
        success: true,
        data: achievements
      });
    } catch (error) {
      logger.error('Error getting user achievements:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get user achievements',
          code: 'USER_ACHIEVEMENTS_ERROR'
        }
      });
    }
  }

  // Get leaderboard
  async getLeaderboard(req, res) {
    try {
      const { period = 'ALL_TIME', limit = 100 } = req.query;
      const leaderboard = await gamificationService.getLeaderboard(period, parseInt(limit));

      res.json({
        success: true,
        data: leaderboard
      });
    } catch (error) {
      logger.error('Error getting leaderboard:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get leaderboard',
          code: 'LEADERBOARD_ERROR'
        }
      });
    }
  }

  // Award points (Admin only)
  async awardPoints(req, res) {
    try {
      const { userId, pointType, points, reason, sessionId, courseId } = req.body;

      const userPoint = await gamificationService.awardPoints(
        userId,
        pointType,
        points,
        reason,
        sessionId,
        courseId
      );

      res.json({
        success: true,
        message: 'Points awarded successfully',
        data: userPoint
      });
    } catch (error) {
      logger.error('Error awarding points:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to award points',
          code: 'AWARD_POINTS_ERROR'
        }
      });
    }
  }

  // Create badge (Admin only)
  async createBadge(req, res) {
    try {
      const { name, nameAr, nameEn, description, icon, color, criteria } = req.body;

      const badge = await prisma.badge.create({
        data: {
          name,
          nameAr,
          nameEn,
          description,
          icon,
          color,
          criteria
        }
      });

      res.status(201).json({
        success: true,
        message: 'Badge created successfully',
        data: badge
      });
    } catch (error) {
      logger.error('Error creating badge:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to create badge',
          code: 'CREATE_BADGE_ERROR'
        }
      });
    }
  }

  // Get all badges (Admin only)
  async getAllBadges(req, res) {
    try {
      const badges = await prisma.badge.findMany({
        orderBy: { createdAt: 'desc' }
      });

      res.json({
        success: true,
        data: badges
      });
    } catch (error) {
      logger.error('Error getting all badges:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get badges',
          code: 'GET_BADGES_ERROR'
        }
      });
    }
  }

  // Update badge (Admin only)
  async updateBadge(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const badge = await prisma.badge.update({
        where: { id },
        data: updateData
      });

      res.json({
        success: true,
        message: 'Badge updated successfully',
        data: badge
      });
    } catch (error) {
      logger.error('Error updating badge:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to update badge',
          code: 'UPDATE_BADGE_ERROR'
        }
      });
    }
  }

  // Delete badge (Admin only)
  async deleteBadge(req, res) {
    try {
      const { id } = req.params;

      await prisma.badge.delete({
        where: { id }
      });

      res.json({
        success: true,
        message: 'Badge deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting badge:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to delete badge',
          code: 'DELETE_BADGE_ERROR'
        }
      });
    }
  }

  // Get gamification statistics (Admin only)
  async getGamificationStats(req, res) {
    try {
      const [
        totalPoints,
        totalBadgesAwarded,
        totalAchievements,
        activeUsers,
        topUsers
      ] = await Promise.all([
        prisma.userPoint.aggregate({
          _sum: { points: true }
        }),
        prisma.userBadge.count(),
        prisma.achievement.count(),
        prisma.leaderboard.count({
          where: { totalPoints: { gt: 0 } }
        }),
        prisma.leaderboard.findMany({
          where: { period: 'ALL_TIME' },
          orderBy: { rank: 'asc' },
          take: 10,
          include: {
            user: {
              select: {
                arabicName: true,
                englishName: true,
                qualification: true
              }
            }
          }
        })
      ]);

      res.json({
        success: true,
        data: {
          totalPoints: totalPoints._sum.points || 0,
          totalBadgesAwarded,
          totalAchievements,
          activeUsers,
          topUsers
        }
      });
    } catch (error) {
      logger.error('Error getting gamification stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get gamification statistics',
          code: 'GAMIFICATION_STATS_ERROR'
        }
      });
    }
  }

  // Manually trigger badge check for user (Admin only)
  async triggerBadgeCheck(req, res) {
    try {
      const { userId } = req.params;

      await gamificationService.checkBadgeAchievements(userId);

      res.json({
        success: true,
        message: 'Badge check triggered successfully'
      });
    } catch (error) {
      logger.error('Error triggering badge check:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to trigger badge check',
          code: 'BADGE_CHECK_ERROR'
        }
      });
    }
  }

  // Update leaderboard rankings (Admin only)
  async updateLeaderboard(req, res) {
    try {
      const { period = 'ALL_TIME' } = req.body;

      await gamificationService.updateRanks(period);

      res.json({
        success: true,
        message: 'Leaderboard updated successfully'
      });
    } catch (error) {
      logger.error('Error updating leaderboard:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to update leaderboard',
          code: 'UPDATE_LEADERBOARD_ERROR'
        }
      });
    }
  }
}

module.exports = new GamificationController();
