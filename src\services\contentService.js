const { PrismaClient } = require('@prisma/client');
const { NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { createPaginationResult } = require('../middleware/validation');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
} catch (error) {
  logger.error('Failed to initialize Prisma client:', error);
  prisma = null;
}

class ContentService {
  // Create or update session
  async createSession(sessionData) {
    try {
      const session = {
        titleAr: sessionData.titleAr,
        titleEn: sessionData.titleEn,
        descriptionAr: sessionData.descriptionAr,
        descriptionEn: sessionData.descriptionEn,
        startTime: new Date(sessionData.startTime),
        endTime: new Date(sessionData.endTime),
        type: sessionData.type,
        cmeHours: sessionData.cmeHours || 0,
        maxAttendees: sessionData.maxAttendees || 500,
        speakerId: sessionData.speakerId,
        location: sessionData.location,
        isActive: sessionData.isActive !== false
      };

      let createdSession;
      if (prisma) {
        createdSession = await prisma.session.create({
          data: session,
          include: {
            speaker: true
          }
        });
      } else {
        // Mock session creation
        createdSession = {
          id: 'session_' + Date.now(),
          ...session,
          createdAt: new Date(),
          updatedAt: new Date(),
          speaker: {
            id: sessionData.speakerId,
            nameAr: 'متحدث تجريبي',
            nameEn: 'Test Speaker'
          }
        };
      }

      logger.info('Session created', { sessionId: createdSession.id, title: session.titleEn });

      return createdSession;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw error;
    }
  }

  // Update session
  async updateSession(sessionId, updateData) {
    try {
      let updatedSession;
      if (prisma) {
        updatedSession = await prisma.session.update({
          where: { id: sessionId },
          data: {
            ...updateData,
            updatedAt: new Date()
          },
          include: {
            speaker: true
          }
        });
      } else {
        // Mock session update
        updatedSession = {
          id: sessionId,
          ...updateData,
          updatedAt: new Date()
        };
      }

      logger.info('Session updated', { sessionId, updates: Object.keys(updateData) });

      return updatedSession;
    } catch (error) {
      logger.error('Error updating session:', error);
      throw error;
    }
  }

  // Delete session
  async deleteSession(sessionId) {
    try {
      if (prisma) {
        await prisma.session.delete({
          where: { id: sessionId }
        });
      }

      logger.info('Session deleted', { sessionId });

      return { success: true };
    } catch (error) {
      logger.error('Error deleting session:', error);
      throw error;
    }
  }

  // Create or update speaker
  async createSpeaker(speakerData) {
    try {
      const speaker = {
        nameAr: speakerData.nameAr,
        nameEn: speakerData.nameEn,
        bio: speakerData.bio,
        bioEn: speakerData.bioEn,
        title: speakerData.title,
        organization: speakerData.organization,
        email: speakerData.email,
        linkedin: speakerData.linkedin,
        twitter: speakerData.twitter,
        website: speakerData.website,
        profileImage: speakerData.profileImage,
        isActive: speakerData.isActive !== false
      };

      let createdSpeaker;
      if (prisma) {
        createdSpeaker = await prisma.speaker.create({
          data: speaker
        });
      } else {
        // Mock speaker creation
        createdSpeaker = {
          id: 'speaker_' + Date.now(),
          ...speaker,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }

      logger.info('Speaker created', { speakerId: createdSpeaker.id, name: speaker.nameEn });

      return createdSpeaker;
    } catch (error) {
      logger.error('Error creating speaker:', error);
      throw error;
    }
  }

  // Update speaker
  async updateSpeaker(speakerId, updateData) {
    try {
      let updatedSpeaker;
      if (prisma) {
        updatedSpeaker = await prisma.speaker.update({
          where: { id: speakerId },
          data: {
            ...updateData,
            updatedAt: new Date()
          }
        });
      } else {
        // Mock speaker update
        updatedSpeaker = {
          id: speakerId,
          ...updateData,
          updatedAt: new Date()
        };
      }

      logger.info('Speaker updated', { speakerId, updates: Object.keys(updateData) });

      return updatedSpeaker;
    } catch (error) {
      logger.error('Error updating speaker:', error);
      throw error;
    }
  }

  // Delete speaker
  async deleteSpeaker(speakerId) {
    try {
      if (prisma) {
        // Check if speaker has sessions
        const sessionsCount = await prisma.session.count({
          where: { speakerId }
        });

        if (sessionsCount > 0) {
          throw new ValidationError('Cannot delete speaker with existing sessions');
        }

        await prisma.speaker.delete({
          where: { id: speakerId }
        });
      }

      logger.info('Speaker deleted', { speakerId });

      return { success: true };
    } catch (error) {
      logger.error('Error deleting speaker:', error);
      throw error;
    }
  }

  // Get all announcements
  async getAnnouncements(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      if (prisma) {
        const where = {};
        
        if (filters.type) {
          where.type = filters.type;
        }
        
        if (filters.isActive !== undefined) {
          where.isActive = filters.isActive;
        }

        const [announcements, total] = await Promise.all([
          prisma.announcement.findMany({
            where,
            skip: offset,
            take: limit,
            orderBy: { createdAt: 'desc' }
          }),
          prisma.announcement.count({ where })
        ]);

        return {
          announcements,
          pagination: createPaginationResult(page, limit, total)
        };
      } else {
        // Mock announcements
        const mockAnnouncements = [
          {
            id: 'ann1',
            titleAr: 'إعلان مهم حول المؤتمر',
            titleEn: 'Important Conference Announcement',
            contentAr: 'نود إعلامكم بآخر التحديثات حول المؤتمر',
            contentEn: 'We would like to inform you about the latest conference updates',
            type: 'GENERAL',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        return {
          announcements: mockAnnouncements,
          pagination: createPaginationResult(1, 10, mockAnnouncements.length)
        };
      }
    } catch (error) {
      logger.error('Error getting announcements:', error);
      throw error;
    }
  }

  // Create announcement
  async createAnnouncement(announcementData) {
    try {
      const announcement = {
        titleAr: announcementData.titleAr,
        titleEn: announcementData.titleEn,
        contentAr: announcementData.contentAr,
        contentEn: announcementData.contentEn,
        type: announcementData.type || 'GENERAL',
        priority: announcementData.priority || 'MEDIUM',
        isActive: announcementData.isActive !== false,
        publishAt: announcementData.publishAt ? new Date(announcementData.publishAt) : new Date(),
        expiresAt: announcementData.expiresAt ? new Date(announcementData.expiresAt) : null
      };

      let createdAnnouncement;
      if (prisma) {
        createdAnnouncement = await prisma.announcement.create({
          data: announcement
        });
      } else {
        // Mock announcement creation
        createdAnnouncement = {
          id: 'announcement_' + Date.now(),
          ...announcement,
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }

      logger.info('Announcement created', { 
        announcementId: createdAnnouncement.id, 
        title: announcement.titleEn 
      });

      return createdAnnouncement;
    } catch (error) {
      logger.error('Error creating announcement:', error);
      throw error;
    }
  }

  // Update announcement
  async updateAnnouncement(announcementId, updateData) {
    try {
      let updatedAnnouncement;
      if (prisma) {
        updatedAnnouncement = await prisma.announcement.update({
          where: { id: announcementId },
          data: {
            ...updateData,
            updatedAt: new Date()
          }
        });
      } else {
        // Mock announcement update
        updatedAnnouncement = {
          id: announcementId,
          ...updateData,
          updatedAt: new Date()
        };
      }

      logger.info('Announcement updated', { 
        announcementId, 
        updates: Object.keys(updateData) 
      });

      return updatedAnnouncement;
    } catch (error) {
      logger.error('Error updating announcement:', error);
      throw error;
    }
  }

  // Delete announcement
  async deleteAnnouncement(announcementId) {
    try {
      if (prisma) {
        await prisma.announcement.delete({
          where: { id: announcementId }
        });
      }

      logger.info('Announcement deleted', { announcementId });

      return { success: true };
    } catch (error) {
      logger.error('Error deleting announcement:', error);
      throw error;
    }
  }

  // Get content statistics
  async getContentStats() {
    try {
      if (prisma) {
        const [
          totalSessions,
          activeSessions,
          totalSpeakers,
          activeSpeakers,
          totalAnnouncements,
          activeAnnouncements
        ] = await Promise.all([
          prisma.session.count(),
          prisma.session.count({ where: { isActive: true } }),
          prisma.speaker.count(),
          prisma.speaker.count({ where: { isActive: true } }),
          prisma.announcement.count(),
          prisma.announcement.count({ where: { isActive: true } })
        ]);

        return {
          sessions: {
            total: totalSessions,
            active: activeSessions,
            inactive: totalSessions - activeSessions
          },
          speakers: {
            total: totalSpeakers,
            active: activeSpeakers,
            inactive: totalSpeakers - activeSpeakers
          },
          announcements: {
            total: totalAnnouncements,
            active: activeAnnouncements,
            inactive: totalAnnouncements - activeAnnouncements
          }
        };
      } else {
        // Mock stats
        return {
          sessions: {
            total: 25,
            active: 20,
            inactive: 5
          },
          speakers: {
            total: 15,
            active: 12,
            inactive: 3
          },
          announcements: {
            total: 8,
            active: 6,
            inactive: 2
          }
        };
      }
    } catch (error) {
      logger.error('Error getting content stats:', error);
      throw error;
    }
  }
}

module.exports = new ContentService();
