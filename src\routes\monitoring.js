const express = require('express');
const { authenticate, requireAdmin } = require('../middleware/auth');
const monitoringController = require('../controllers/monitoringController');

const router = express.Router();

// Public health check (no authentication required)
router.get('/health', monitoringController.getHealthReport);

// Basic metrics (admin only)
router.get('/metrics', authenticate, requireAdmin, monitoringController.getSystemMetrics);
router.get('/metrics/advanced', authenticate, requireAdmin, monitoringController.getAdvancedMetrics);
router.get('/metrics/realtime', authenticate, requireAdmin, monitoringController.getRealTimeStats);

// Performance and insights
router.get('/performance', authenticate, requireAdmin, monitoringController.getPerformanceInsights);
router.get('/dashboard', authenticate, requireAdmin, monitoringController.getDashboardData);

// API usage and statistics
router.get('/api-usage', authenticate, requireAdmin, monitoringController.getApiUsageStats);
router.get('/errors', authenticate, requireAdmin, monitoringController.getErrorLogs);
router.get('/alerts', authenticate, requireAdmin, monitoringController.getSystemAlerts);

// Administrative actions
router.post('/reset', authenticate, requireAdmin, monitoringController.resetMetrics);
router.get('/export', authenticate, requireAdmin, monitoringController.exportMetrics);

module.exports = router;
