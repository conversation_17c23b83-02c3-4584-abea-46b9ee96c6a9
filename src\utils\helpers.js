const crypto = require('crypto');
const path = require('path');

// Generate random string
const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

// Generate UUID-like string
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Validate email format
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate Saudi phone number
const isValidSaudiPhone = (phone) => {
  const phoneRegex = /^\+966[0-9]{9}$/;
  return phoneRegex.test(phone);
};

// Format phone number to Saudi format
const formatSaudiPhone = (phone) => {
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '');
  
  // If starts with 966, add +
  if (digits.startsWith('966')) {
    return '+' + digits;
  }
  
  // If starts with 0, replace with +966
  if (digits.startsWith('0')) {
    return '+966' + digits.substring(1);
  }
  
  // If 9 digits, add +966
  if (digits.length === 9) {
    return '+966' + digits;
  }
  
  return phone; // Return original if can't format
};

// Sanitize filename for safe storage
const sanitizeFilename = (filename) => {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .toLowerCase();
};

// Get file extension
const getFileExtension = (filename) => {
  return path.extname(filename).toLowerCase().substring(1);
};

// Check if file type is allowed
const isAllowedFileType = (filename, allowedTypes = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']) => {
  const extension = getFileExtension(filename);
  return allowedTypes.includes(extension);
};

// Format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Sleep function for delays
const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Deep clone object
const deepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

// Remove sensitive fields from object
const removeSensitiveFields = (obj, sensitiveFields = ['password', 'passwordHash', 'token', 'secret']) => {
  const cloned = deepClone(obj);
  
  const removeSensitive = (item) => {
    if (Array.isArray(item)) {
      return item.map(removeSensitive);
    }
    
    if (item && typeof item === 'object') {
      const cleaned = {};
      for (const [key, value] of Object.entries(item)) {
        if (!sensitiveFields.includes(key)) {
          cleaned[key] = removeSensitive(value);
        }
      }
      return cleaned;
    }
    
    return item;
  };
  
  return removeSensitive(cloned);
};

// Format date to Arabic
const formatDateArabic = (date) => {
  const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  
  const d = new Date(date);
  const day = d.getDate();
  const month = arabicMonths[d.getMonth()];
  const year = d.getFullYear();
  
  return `${day} ${month} ${year}`;
};

// Convert English numbers to Arabic
const toArabicNumbers = (str) => {
  const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
  return str.replace(/[0-9]/g, (match) => arabicNumbers[parseInt(match)]);
};

// Convert Arabic numbers to English
const toEnglishNumbers = (str) => {
  const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
  return str.replace(/[٠-٩]/g, (match) => arabicNumbers.indexOf(match).toString());
};

// Validate Arabic text
const isArabicText = (text) => {
  const arabicRegex = /^[\u0600-\u06FF\s]+$/;
  return arabicRegex.test(text);
};

// Validate English text
const isEnglishText = (text) => {
  const englishRegex = /^[a-zA-Z\s]+$/;
  return englishRegex.test(text);
};

// Generate QR code data
const generateQRData = (type, data) => {
  return JSON.stringify({
    type,
    data,
    timestamp: Date.now(),
    version: '1.0'
  });
};

// Parse QR code data
const parseQRData = (qrString) => {
  try {
    const parsed = JSON.parse(qrString);
    return {
      isValid: true,
      ...parsed
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid QR code format'
    };
  }
};

// Calculate age from birth date
const calculateAge = (birthDate) => {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

// Mask sensitive data
const maskData = (data, type = 'email') => {
  if (!data) return '';
  
  switch (type) {
    case 'email':
      const [username, domain] = data.split('@');
      const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
      return `${maskedUsername}@${domain}`;
    
    case 'phone':
      return data.substring(0, 4) + '*'.repeat(data.length - 7) + data.substring(data.length - 3);
    
    case 'name':
      return data.charAt(0) + '*'.repeat(data.length - 1);
    
    default:
      return '*'.repeat(data.length);
  }
};

// Retry function with exponential backoff
const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      const delay = baseDelay * Math.pow(2, i);
      await sleep(delay);
    }
  }
};

module.exports = {
  generateRandomString,
  generateId,
  isValidEmail,
  isValidSaudiPhone,
  formatSaudiPhone,
  sanitizeFilename,
  getFileExtension,
  isAllowedFileType,
  formatFileSize,
  sleep,
  deepClone,
  removeSensitiveFields,
  formatDateArabic,
  toArabicNumbers,
  toEnglishNumbers,
  isArabicText,
  isEnglishText,
  generateQRData,
  parseQRData,
  calculateAge,
  maskData,
  retryWithBackoff
};
