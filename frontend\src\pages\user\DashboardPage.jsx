import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  Paper,
  Divider,
} from '@mui/material';
import {
  Person,
  Event,
  Payment,
  Certificate,
  Notifications,
  Edit,
  Visibility,
  CheckCircle,
  Schedule,
  Warning,
  Info,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { userAPI, conferenceAPI } from '../../services/api';

const DashboardPage = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Fetch user subscription status
  const { data: subscriptionData, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['userSubscription'],
    queryFn: userAPI.getSubscriptionStatus,
  });

  // Fetch user's personal agenda
  const { data: agendaData, isLoading: agendaLoading } = useQuery({
    queryKey: ['personalAgenda'],
    queryFn: conferenceAPI.getPersonalAgenda,
  });

  // Fetch payment history
  const { data: paymentsData, isLoading: paymentsLoading } = useQuery({
    queryKey: ['paymentHistory'],
    queryFn: () => userAPI.getPaymentHistory({ page: 1, limit: 5 }),
  });

  // Fetch notifications
  const { data: notificationsData, isLoading: notificationsLoading } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => userAPI.getNotifications({ page: 1, limit: 5 }),
  });

  const subscription = subscriptionData?.data;
  const agenda = agendaData?.data?.agenda || [];
  const payments = paymentsData?.data?.payments || [];
  const notifications = notificationsData?.data?.notifications || [];

  // Get status color and icon
  const getStatusInfo = (status) => {
    switch (status) {
      case 'PENDING_REVIEW':
        return { color: 'warning', icon: <Schedule />, text: t('PENDING_REVIEW') };
      case 'APPROVED_PENDING_PAYMENT':
        return { color: 'info', icon: <Payment />, text: t('APPROVED_PENDING_PAYMENT') };
      case 'ACTIVE':
        return { color: 'success', icon: <CheckCircle />, text: t('ACTIVE') };
      case 'REJECTED':
        return { color: 'error', icon: <Warning />, text: t('REJECTED') };
      default:
        return { color: 'default', icon: <Info />, text: status };
    }
  };

  // Quick stats
  const stats = [
    {
      title: t('registeredSessions', 'الجلسات المسجلة'),
      value: agenda.length,
      icon: <Event />,
      color: 'primary',
      action: () => navigate('/conference'),
    },
    {
      title: t('totalPayments', 'إجمالي المدفوعات'),
      value: payments.reduce((sum, payment) => sum + (payment.amount || 0), 0),
      suffix: t('currency', 'ريال'),
      icon: <Payment />,
      color: 'success',
      action: () => navigate('/dashboard/payment'),
    },
    {
      title: t('cmeHours', 'ساعات التعليم المستمر'),
      value: agenda.reduce((sum, session) => sum + (session.cmeHours || 0), 0),
      icon: <Certificate />,
      color: 'secondary',
      action: () => navigate('/dashboard/certificates'),
    },
    {
      title: t('unreadNotifications', 'الإشعارات غير المقروءة'),
      value: notifications.filter(n => !n.isRead).length,
      icon: <Notifications />,
      color: 'warning',
      action: () => navigate('/dashboard/notifications'),
    },
  ];

  return (
    <Box>
      {/* Welcome Section */}
      <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)', color: 'white' }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar sx={{ width: 80, height: 80, bgcolor: 'rgba(255,255,255,0.2)' }}>
              {user?.arabicName?.charAt(0) || user?.englishName?.charAt(0) || 'U'}
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" gutterBottom>
              {t('welcome', 'مرحباً')}, {user?.arabicName || user?.englishName}
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              {t('qualification')}: {t(user?.qualification || 'DOCTOR')}
            </Typography>
            {user?.specialization && (
              <Typography variant="body1" sx={{ opacity: 0.8 }}>
                {t('specialization')}: {user.specialization}
              </Typography>
            )}
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              sx={{ bgcolor: 'rgba(255,255,255,0.2)', '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' } }}
              startIcon={<Edit />}
              onClick={() => navigate('/dashboard/profile')}
            >
              {t('editProfile', 'تعديل الملف الشخصي')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Subscription Status */}
      {subscription && (
        <Alert 
          severity={getStatusInfo(subscription.status).color}
          icon={getStatusInfo(subscription.status).icon}
          sx={{ mb: 3 }}
          action={
            subscription.status === 'APPROVED_PENDING_PAYMENT' && (
              <Button color="inherit" size="small" onClick={() => navigate('/dashboard/payment')}>
                {t('payNow', 'ادفع الآن')}
              </Button>
            )
          }
        >
          <Typography variant="body1">
            {t('subscriptionStatus')}: <strong>{getStatusInfo(subscription.status).text}</strong>
          </Typography>
          {subscription.reviewNotes && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              {subscription.reviewNotes}
            </Typography>
          )}
        </Alert>
      )}

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': { transform: 'translateY(-4px)' }
              }}
              onClick={stat.action}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" component="div">
                      {stat.value} {stat.suffix}
                    </Typography>
                  </Box>
                  <Avatar sx={{ bgcolor: `${stat.color}.main` }}>
                    {stat.icon}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Personal Agenda */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" component="h2">
                  {t('personalAgenda', 'جدولي الشخصي')}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/conference')}
                  endIcon={<Visibility />}
                >
                  {t('viewAll', 'عرض الكل')}
                </Button>
              </Box>

              {agendaLoading ? (
                <LinearProgress />
              ) : agenda.length > 0 ? (
                <List>
                  {agenda.slice(0, 3).map((session, index) => (
                    <React.Fragment key={session.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Event color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={session.titleAr || session.titleEn}
                          secondary={new Date(session.startTime).toLocaleDateString('ar-SA')}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={t(session.type)}
                            size="small"
                            variant="outlined"
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < agenda.slice(0, 3).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noSessionsRegistered', 'لم تسجل في أي جلسات بعد')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Payments */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" component="h2">
                  {t('recentPayments', 'المدفوعات الأخيرة')}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/dashboard/payment')}
                  endIcon={<Visibility />}
                >
                  {t('viewAll', 'عرض الكل')}
                </Button>
              </Box>

              {paymentsLoading ? (
                <LinearProgress />
              ) : payments.length > 0 ? (
                <List>
                  {payments.slice(0, 3).map((payment, index) => (
                    <React.Fragment key={payment.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Payment color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary={`${payment.amount} ${t('currency', 'ريال')}`}
                          secondary={new Date(payment.createdAt).toLocaleDateString('ar-SA')}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={t(payment.status)}
                            size="small"
                            color={payment.status === 'COMPLETED' ? 'success' : 'default'}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < payments.slice(0, 3).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noPayments', 'لا توجد مدفوعات')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Notifications */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" component="h2">
                  {t('recentNotifications', 'الإشعارات الأخيرة')}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigate('/dashboard/notifications')}
                  endIcon={<Visibility />}
                >
                  {t('viewAll', 'عرض الكل')}
                </Button>
              </Box>

              {notificationsLoading ? (
                <LinearProgress />
              ) : notifications.length > 0 ? (
                <List>
                  {notifications.slice(0, 3).map((notification, index) => (
                    <React.Fragment key={notification.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Notifications color={notification.isRead ? 'disabled' : 'primary'} />
                        </ListItemIcon>
                        <ListItemText
                          primary={notification.title}
                          secondary={notification.message}
                          sx={{ 
                            '& .MuiListItemText-primary': {
                              fontWeight: notification.isRead ? 'normal' : 'bold'
                            }
                          }}
                        />
                        <ListItemSecondaryAction>
                          <Typography variant="caption" color="text.secondary">
                            {new Date(notification.createdAt).toLocaleDateString('ar-SA')}
                          </Typography>
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < notifications.slice(0, 3).length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography color="text.secondary" textAlign="center" py={3}>
                  {t('noNotifications', 'لا توجد إشعارات')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          {t('quickActions', 'إجراءات سريعة')}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Event />}
              onClick={() => navigate('/conference')}
            >
              {t('browseSessions', 'تصفح الجلسات')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Person />}
              onClick={() => navigate('/dashboard/profile')}
            >
              {t('updateProfile', 'تحديث الملف الشخصي')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Payment />}
              onClick={() => navigate('/dashboard/subscription')}
            >
              {t('manageSubscription', 'إدارة الاشتراك')}
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Certificate />}
              onClick={() => navigate('/dashboard/certificates')}
            >
              {t('viewCertificates', 'عرض الشهادات')}
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default DashboardPage;
