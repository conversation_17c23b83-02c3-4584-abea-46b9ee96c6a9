const express = require('express');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

console.log('Testing middleware imports...');

try {
  console.log('1. Testing logger...');
  const logger = require('./src/utils/logger');
  console.log('✅ Logger imported successfully');

  console.log('2. Testing errorHandler...');
  const { errorHandler } = require('./src/middleware/errorHandler');
  console.log('✅ ErrorHandler imported successfully');

  console.log('3. Testing validation middleware...');
  const { addRequestId, requestTiming, sanitizeInput, formatResponse } = require('./src/middleware/validation');
  console.log('✅ Validation middleware imported successfully');

  console.log('4. Testing routes...');
  const routes = require('./src/routes');
  console.log('✅ Routes imported successfully');

  console.log('5. Testing monitoring...');
  const { systemMonitor } = require('./src/utils/monitoring');
  console.log('✅ Monitoring imported successfully');

  // Basic middleware setup
  app.use(express.json());
  app.use(addRequestId);
  app.use(requestTiming);
  app.use(sanitizeInput);
  app.use(formatResponse);

  // Health check
  app.get('/health', (req, res) => {
    res.json({
      success: true,
      message: 'Server is running',
      timestamp: new Date().toISOString()
    });
  });

  // Routes
  app.use('/api/v1', routes);

  // Error handler
  app.use(errorHandler);

  app.listen(PORT, () => {
    console.log(`✅ Test server running on port ${PORT}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  });

} catch (error) {
  console.error('❌ Error importing modules:', error);
  process.exit(1);
}
