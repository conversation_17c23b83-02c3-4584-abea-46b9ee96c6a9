const { query, param, body } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const { handleValidationErrors, pagination } = require('../middleware/validation');
const userService = require('../services/userService');
const subscriptionService = require('../services/subscriptionService');
const paymentService = require('../services/paymentService');
const conferenceService = require('../services/conferenceService');
const advancedMonitor = require('../utils/advancedMonitoring');
const logger = require('../utils/logger');

// Validation rules
const getUsersValidation = [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION']),
  query('qualification').optional().isString(),
  query('search').optional().isString(),
  handleValidationErrors
];

const updateUserStatusValidation = [
  param('id').notEmpty().withMessage('معرف المستخدم مطلوب'),
  body('status').isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING_VERIFICATION']).withMessage('حالة غير صحيحة'),
  body('reason').optional().isString().withMessage('السبب يجب أن يكون نص'),
  handleValidationErrors
];

const createManualPaymentValidation = [
  body('userId').notEmpty().withMessage('معرف المستخدم مطلوب'),
  body('amount').isFloat({ min: 0 }).withMessage('المبلغ يجب أن يكون رقم موجب'),
  body('purpose').isIn(['CONFERENCE_SUBSCRIPTION', 'COURSE_ENROLLMENT', 'MANUAL_ADJUSTMENT']).withMessage('غرض الدفع غير صحيح'),
  body('notes').optional().isString(),
  handleValidationErrors
];

const sendNotificationValidation = [
  body('title').notEmpty().withMessage('عنوان الإشعار مطلوب'),
  body('message').notEmpty().withMessage('نص الإشعار مطلوب'),
  body('type').isIn(['GENERAL_ANNOUNCEMENT', 'SYSTEM_ALERT', 'SUBSCRIPTION_UPDATE', 'PAYMENT_REMINDER']).withMessage('نوع الإشعار غير صحيح'),
  body('recipients').optional().isArray().withMessage('قائمة المستقبلين يجب أن تكون مصفوفة'),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).withMessage('أولوية غير صحيحة'),
  handleValidationErrors
];

// Get comprehensive admin dashboard
const getAdminDashboard = asyncHandler(async (req, res) => {
  try {
    // Get all statistics in parallel
    const [
      userStats,
      subscriptionStats,
      paymentStats,
      systemHealth,
      recentActivity
    ] = await Promise.all([
      userService.getAllUsers({}, { page: 1, limit: 1 }), // Just for count
      subscriptionService.getSubscriptionStats(),
      paymentService.getPaymentStats(),
      advancedMonitor.getHealthReport(),
      getRecentActivity()
    ]);

    const dashboardData = {
      overview: {
        totalUsers: userStats.total,
        activeSubscriptions: subscriptionStats.byStatus.active,
        pendingSubscriptions: subscriptionStats.byStatus.pending,
        totalRevenue: paymentStats.totalRevenue,
        systemHealth: systemHealth.status,
        uptime: Math.round(process.uptime() / 3600 * 100) / 100 // hours
      },
      statistics: {
        users: {
          total: userStats.total,
          byQualification: subscriptionStats.byQualification,
          recentRegistrations: 0 // TODO: Implement recent registrations count
        },
        subscriptions: subscriptionStats,
        payments: paymentStats,
        system: {
          health: systemHealth,
          alerts: advancedMonitor.getMetrics().alerts?.length || 0
        }
      },
      recentActivity,
      quickActions: [
        { id: 'approve_subscriptions', name: 'موافقة الاشتراكات', count: subscriptionStats.byStatus.pending },
        { id: 'review_payments', name: 'مراجعة المدفوعات', count: paymentStats.byStatus.pending },
        { id: 'system_alerts', name: 'تنبيهات النظام', count: systemHealth.checks ? Object.values(systemHealth.checks).filter(c => c.status !== 'healthy').length : 0 }
      ]
    };

    logger.logAdminAction(req.user.id, 'ADMIN_DASHBOARD_VIEWED');

    res.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting admin dashboard:', error);
    throw error;
  }
});

// Get all users with advanced filtering
const getAllUsers = [
  getUsersValidation,
  pagination,
  asyncHandler(async (req, res) => {
    const filters = {
      status: req.query.status,
      qualification: req.query.qualification,
      search: req.query.search
    };

    const result = await userService.getAllUsers(filters, req.pagination);

    logger.logAdminAction(req.user.id, 'USERS_LIST_VIEWED', { filters, pagination: req.pagination });

    req.paginationResult = result.pagination;

    res.json({
      success: true,
      data: {
        users: result.users,
        filters,
        summary: {
          total: result.total,
          filtered: result.users.length
        }
      }
    });
  })
];

// Update user status
const updateUserStatus = [
  updateUserStatusValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { status, reason } = req.body;
    const updatedBy = req.user.id;

    const updatedUser = await userService.updateUserStatus(id, status, updatedBy);

    // Log the action with reason
    logger.logAudit(updatedBy, 'USER_STATUS_UPDATED', 'user', id, {
      newStatus: status,
      reason
    });

    res.json({
      success: true,
      message: 'تم تحديث حالة المستخدم بنجاح',
      data: {
        user: {
          id: updatedUser.id,
          status: updatedUser.status,
          updatedAt: updatedUser.updatedAt
        }
      }
    });
  })
];

// Get user details with full information
const getUserDetails = [
  param('id').notEmpty().withMessage('معرف المستخدم مطلوب'),
  handleValidationErrors,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const [user, subscription, payments] = await Promise.all([
      userService.findUserById(id),
      subscriptionService.getUserSubscription(id),
      paymentService.getUserPayments(id, { page: 1, limit: 10 })
    ]);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    logger.logAdminAction(req.user.id, 'USER_DETAILS_VIEWED', { userId: id });

    res.json({
      success: true,
      data: {
        user,
        subscription,
        payments: payments.payments,
        summary: {
          hasSubscription: !!subscription,
          totalPayments: payments.pagination.total,
          lastActivity: user.updatedAt
        }
      }
    });
  })
];

// Create manual payment
const createManualPayment = [
  createManualPaymentValidation,
  asyncHandler(async (req, res) => {
    const { userId, amount, purpose, notes } = req.body;
    const createdBy = req.user.id;

    // Verify user exists
    const user = await userService.findUserById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Create payment record
    const paymentData = {
      userId,
      amount,
      currency: 'SAR',
      purpose,
      method: 'MANUAL',
      reference: `MANUAL-${Date.now()}`,
      notes
    };

    const payment = await paymentService.createPayment(paymentData);

    // Mark payment as completed for manual payments
    await paymentService.updatePaymentStatus(payment.id, 'COMPLETED', {
      processedBy: createdBy,
      processedAt: new Date()
    });

    logger.logAudit(createdBy, 'MANUAL_PAYMENT_CREATED', 'payment', payment.id, {
      userId,
      amount,
      purpose,
      notes
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء الدفعة اليدوية بنجاح',
      data: {
        payment: {
          id: payment.id,
          amount: payment.amount,
          currency: payment.currency,
          purpose: payment.purpose,
          status: 'COMPLETED',
          createdAt: payment.createdAt
        }
      }
    });
  })
];

// Get comprehensive reports
const getReports = asyncHandler(async (req, res) => {
  const { type = 'overview', period = '30d' } = req.query;

  try {
    let reportData = {};

    switch (type) {
      case 'subscriptions':
        reportData = await generateSubscriptionReport(period);
        break;
      case 'financial':
        reportData = await generateFinancialReport(period);
        break;
      case 'attendance':
        reportData = await generateAttendanceReport(period);
        break;
      case 'system':
        reportData = await generateSystemReport(period);
        break;
      default:
        reportData = await generateOverviewReport(period);
    }

    logger.logAdminAction(req.user.id, 'REPORT_GENERATED', { type, period });

    res.json({
      success: true,
      data: {
        report: reportData,
        type,
        period,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error generating report:', error);
    throw error;
  }
});

// Send notification to users
const sendNotification = [
  sendNotificationValidation,
  asyncHandler(async (req, res) => {
    const { title, message, type, recipients, priority = 'MEDIUM' } = req.body;
    const sentBy = req.user.id;

    // TODO: Implement notification sending logic
    const notificationData = {
      id: 'notification_' + Date.now(),
      title,
      message,
      type,
      priority,
      sentBy,
      sentAt: new Date(),
      recipients: recipients || 'all_users',
      status: 'SENT'
    };

    logger.logAudit(sentBy, 'NOTIFICATION_SENT', 'notification', notificationData.id, {
      title,
      type,
      recipientCount: recipients?.length || 'all'
    });

    res.json({
      success: true,
      message: 'تم إرسال الإشعار بنجاح',
      data: {
        notification: notificationData
      }
    });
  })
];

// Helper functions
async function getRecentActivity() {
  // TODO: Implement recent activity tracking
  return [
    {
      id: 1,
      type: 'USER_REGISTRATION',
      description: 'مستخدم جديد سجل في النظام',
      timestamp: new Date(),
      user: 'د. أحمد محمد'
    },
    {
      id: 2,
      type: 'SUBSCRIPTION_APPROVED',
      description: 'تم قبول اشتراك جديد',
      timestamp: new Date(Date.now() - 3600000),
      user: 'د. فاطمة علي'
    },
    {
      id: 3,
      type: 'PAYMENT_COMPLETED',
      description: 'تم إكمال دفعة بقيمة 500 ريال',
      timestamp: new Date(Date.now() - 7200000),
      user: 'محمد سعد'
    }
  ];
}

async function generateOverviewReport(period) {
  const [subscriptionStats, paymentStats] = await Promise.all([
    subscriptionService.getSubscriptionStats(),
    paymentService.getPaymentStats()
  ]);

  return {
    summary: {
      totalUsers: subscriptionStats.total,
      totalRevenue: paymentStats.totalRevenue,
      activeSubscriptions: subscriptionStats.byStatus.active,
      period
    },
    subscriptions: subscriptionStats,
    payments: paymentStats
  };
}

async function generateSubscriptionReport(period) {
  const stats = await subscriptionService.getSubscriptionStats();
  return {
    period,
    ...stats,
    trends: {
      // TODO: Implement trend analysis
      growth: '+15%',
      conversionRate: '85%'
    }
  };
}

async function generateFinancialReport(period) {
  const stats = await paymentService.getPaymentStats();
  return {
    period,
    ...stats,
    trends: {
      // TODO: Implement financial trends
      revenue_growth: '+25%',
      average_transaction: stats.totalRevenue / stats.totalPayments
    }
  };
}

async function generateAttendanceReport(period) {
  // TODO: Implement attendance reporting
  return {
    period,
    totalAttendees: 1500,
    sessionsAttended: 4500,
    averageAttendance: '75%',
    topSessions: []
  };
}

async function generateSystemReport(period) {
  const health = advancedMonitor.getHealthReport();
  const metrics = advancedMonitor.getMetrics();
  
  return {
    period,
    health,
    performance: {
      uptime: process.uptime(),
      totalRequests: metrics.requests.total,
      errorRate: metrics.requests.errorRate,
      averageResponseTime: metrics.requests.averageResponseTime
    },
    alerts: metrics.alerts?.length || 0
  };
}

// Simple dashboard for new admin panel
const getDashboardStats = async (req, res) => {
  try {
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    const [
      totalUsers,
      totalSubscriptions,
      totalPayments,
      totalSessions,
      pendingApplications,
      activeUsers,
      totalRevenue,
      recentActivities
    ] = await Promise.all([
      prisma.user.count(),
      prisma.subscription.count(),
      prisma.payment.count(),
      prisma.session.count(),
      prisma.subscription.count({
        where: { status: 'PENDING_REVIEW' }
      }),
      prisma.user.count({
        where: { status: 'ACTIVE' }
      }),
      prisma.payment.aggregate({
        _sum: { amount: true },
        where: { status: 'COMPLETED' }
      }),
      prisma.user.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          arabicName: true,
          englishName: true,
          email: true,
          createdAt: true,
          status: true
        }
      })
    ]);

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers,
        pending: totalUsers - activeUsers
      },
      subscriptions: {
        total: totalSubscriptions,
        pending: pendingApplications
      },
      payments: {
        total: totalPayments,
        revenue: totalRevenue._sum.amount || 0
      },
      sessions: {
        total: totalSessions
      },
      recentActivities
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to fetch dashboard statistics' }
    });
  }
};

module.exports = {
  getAdminDashboard,
  getAllUsers,
  updateUserStatus,
  getUserDetails,
  createManualPayment,
  getReports,
  sendNotification,
  getDashboardStats
};
