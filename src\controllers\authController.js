const { body } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const { handleValidationErrors } = require('../middleware/validation');
const authService = require('../services/authService');
const logger = require('../utils/logger');

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  
  body('phone')
    .matches(/^\+966[0-9]{9}$/)
    .withMessage('رقم الهاتف يجب أن يكون بالصيغة: +966xxxxxxxxx'),
  
  body('arabicName')
    .isLength({ min: 2, max: 100 })
    .matches(/^[\u0600-\u06FF\s]+$/)
    .withMessage('الاسم العربي يجب أن يحتوي على أحرف عربية فقط'),
  
  body('englishName')
    .isLength({ min: 2, max: 100 })
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('الاسم الإنجليزي يجب أن يحتوي على أحرف إنجليزية فقط'),
  
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة ورقم ورمز خاص'),
  
  body('qualification')
    .isIn(['DOCTOR', 'STUDENT_YEAR_1', 'STUDENT_YEAR_2', 'STUDENT_YEAR_3', 'STUDENT_YEAR_4', 'STUDENT_YEAR_5', 'STUDENT_YEAR_6'])
    .withMessage('المؤهل العلمي غير صحيح'),
  
  handleValidationErrors
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('البريد الإلكتروني غير صحيح'),
  
  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة'),
  
  handleValidationErrors
];

const otpValidation = [
  body('phone')
    .matches(/^\+966[0-9]{9}$/)
    .withMessage('رقم الهاتف غير صحيح'),
  
  body('otp')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('رمز التحقق يجب أن يكون 6 أرقام'),
  
  handleValidationErrors
];

// Register new user
const register = asyncHandler(async (req, res) => {
  const {
    email,
    phone,
    password,
    arabicName,
    englishName,
    qualification,
    specialization,
    university
  } = req.body;

  logger.info('Registration attempt', { email, phone, qualification });

  const result = await authService.register({
    email,
    phone,
    password,
    arabicName,
    englishName,
    qualification,
    specialization,
    university
  });

  // Send OTP for phone verification
  const otp = authService.generateOTP();
  await authService.sendOTP(phone, otp);

  res.status(201).json({
    success: true,
    message: 'تم إنشاء الحساب بنجاح. يرجى التحقق من رقم الهاتف',
    data: {
      user: {
        id: result.user.id,
        email: result.user.email,
        arabicName: result.user.arabicName,
        englishName: result.user.englishName,
        status: result.user.status
      },
      requiresPhoneVerification: true
    }
  });
});

// Login user
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  logger.info('Login attempt', { email });

  const result = await authService.login(email, password);

  res.json({
    success: true,
    message: 'تم تسجيل الدخول بنجاح',
    data: {
      user: result.user,
      tokens: result.tokens
    }
  });
});

// Verify OTP
const verifyOTP = asyncHandler(async (req, res) => {
  const { phone, otp } = req.body;

  logger.info('OTP verification attempt', { phone });

  await authService.verifyOTP(phone, otp);

  res.json({
    success: true,
    message: 'تم التحقق من رقم الهاتف بنجاح',
    data: {
      phoneVerified: true
    }
  });
});

// Refresh token
const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken: token } = req.body;

  if (!token) {
    return res.status(400).json({
      success: false,
      message: 'Refresh token is required'
    });
  }

  const result = await authService.refreshToken(token);

  res.json({
    success: true,
    message: 'تم تجديد الرمز المميز بنجاح',
    data: {
      tokens: result
    }
  });
});

// Logout user
const logout = asyncHandler(async (req, res) => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.substring(7); // Remove 'Bearer ' prefix

  if (token) {
    await authService.logout(token);
  }

  res.json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  });
});

// Send OTP for phone verification
const sendOTP = asyncHandler(async (req, res) => {
  const { phone } = req.body;

  if (!phone) {
    return res.status(400).json({
      success: false,
      message: 'رقم الهاتف مطلوب'
    });
  }

  const otp = authService.generateOTP();
  await authService.sendOTP(phone, otp);

  res.json({
    success: true,
    message: 'تم إرسال رمز التحقق إلى رقم الهاتف',
    data: {
      phone,
      // In development, include OTP for testing
      ...(process.env.NODE_ENV === 'development' && { otp })
    }
  });
});

// Forgot password
const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  // TODO: Implement forgot password logic
  logger.info('Forgot password request', { email });

  res.json({
    success: true,
    message: 'إذا كان البريد الإلكتروني موجود، سيتم إرسال رابط إعادة تعيين كلمة المرور',
    data: {
      message: 'Forgot password endpoint - Coming soon'
    }
  });
});

// Reset password
const resetPassword = asyncHandler(async (req, res) => {
  const { token, newPassword } = req.body;

  // TODO: Implement reset password logic
  logger.info('Password reset attempt', { token: token?.substring(0, 10) + '...' });

  res.json({
    success: true,
    message: 'تم إعادة تعيين كلمة المرور بنجاح',
    data: {
      message: 'Password reset endpoint - Coming soon'
    }
  });
});

module.exports = {
  register: [registerValidation, register],
  login: [loginValidation, login],
  verifyOTP: [otpValidation, verifyOTP],
  refreshToken,
  logout,
  sendOTP,
  forgotPassword,
  resetPassword
};
