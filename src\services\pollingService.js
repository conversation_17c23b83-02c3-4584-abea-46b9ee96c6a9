const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gamificationService = require('./gamificationService');

const prisma = new PrismaClient();

class PollingService {
  // Create a new poll
  async createPoll(pollData, createdBy) {
    try {
      const { options, ...pollInfo } = pollData;

      const poll = await prisma.poll.create({
        data: {
          ...pollInfo,
          createdBy,
          options: {
            create: options.map((option, index) => ({
              text: option.text,
              textAr: option.textAr,
              textEn: option.textEn,
              order: index,
              isCorrect: option.isCorrect
            }))
          }
        },
        include: {
          options: {
            orderBy: { order: 'asc' }
          },
          session: {
            select: {
              titleAr: true,
              titleEn: true
            }
          }
        }
      });

      logger.info(`Poll created: ${poll.id} by user ${createdBy}`);
      return poll;
    } catch (error) {
      logger.error('Error creating poll:', error);
      throw error;
    }
  }

  // Get poll by ID with results
  async getPollById(pollId, userId = null) {
    try {
      const poll = await prisma.poll.findUnique({
        where: { id: pollId },
        include: {
          options: {
            orderBy: { order: 'asc' },
            include: {
              _count: {
                select: { responses: true }
              }
            }
          },
          session: {
            select: {
              titleAr: true,
              titleEn: true
            }
          },
          creator: {
            select: {
              arabicName: true,
              englishName: true
            }
          },
          _count: {
            select: { responses: true }
          }
        }
      });

      if (!poll) {
        throw new Error('Poll not found');
      }

      // Check if user has already responded
      let userResponse = null;
      if (userId) {
        userResponse = await prisma.pollResponse.findFirst({
          where: {
            pollId,
            userId
          },
          include: {
            option: true
          }
        });
      }

      // Calculate percentages for each option
      const totalResponses = poll._count.responses;
      const optionsWithStats = poll.options.map(option => ({
        ...option,
        responseCount: option._count.responses,
        percentage: totalResponses > 0 ? (option._count.responses / totalResponses * 100).toFixed(1) : 0
      }));

      return {
        ...poll,
        options: optionsWithStats,
        totalResponses,
        userResponse: userResponse ? {
          optionId: userResponse.optionId,
          textResponse: userResponse.textResponse,
          ratingValue: userResponse.ratingValue
        } : null
      };
    } catch (error) {
      logger.error('Error getting poll by ID:', error);
      throw error;
    }
  }

  // Submit poll response
  async submitPollResponse(pollId, userId, responseData, ipAddress = null, userAgent = null) {
    try {
      const poll = await prisma.poll.findUnique({
        where: { id: pollId },
        include: { options: true }
      });

      if (!poll) {
        throw new Error('Poll not found');
      }

      if (!poll.isActive || !poll.isLive) {
        throw new Error('Poll is not active or live');
      }

      // Check if poll has ended
      if (poll.endTime && new Date() > poll.endTime) {
        throw new Error('Poll has ended');
      }

      // Check if user has already responded (for non-anonymous polls)
      if (!poll.isAnonymous && userId) {
        const existingResponse = await prisma.pollResponse.findFirst({
          where: {
            pollId,
            userId
          }
        });

        if (existingResponse && !poll.allowMultiple) {
          throw new Error('User has already responded to this poll');
        }
      }

      // Validate response based on poll type
      const { optionId, textResponse, ratingValue } = responseData;

      if (poll.type === 'TEXT' && !textResponse) {
        throw new Error('Text response is required for text polls');
      }

      if (poll.type === 'RATING' && (!ratingValue || ratingValue < 1 || ratingValue > 5)) {
        throw new Error('Rating value must be between 1 and 5');
      }

      if (['MULTIPLE_CHOICE', 'SINGLE_CHOICE', 'YES_NO'].includes(poll.type) && !optionId) {
        throw new Error('Option selection is required');
      }

      // Create response
      const response = await prisma.pollResponse.create({
        data: {
          pollId,
          userId: poll.isAnonymous ? null : userId,
          optionId,
          textResponse,
          ratingValue,
          ipAddress,
          userAgent
        }
      });

      // Award points for participation
      if (userId) {
        await gamificationService.awardPoints(
          userId,
          'SOCIAL_INTERACTION',
          3,
          'Participated in live poll'
        );
      }

      logger.info(`Poll response submitted for poll ${pollId}`);
      return response;
    } catch (error) {
      logger.error('Error submitting poll response:', error);
      throw error;
    }
  }

  // Get live polls for a session
  async getLivePolls(sessionId) {
    try {
      const polls = await prisma.poll.findMany({
        where: {
          sessionId,
          isActive: true,
          isLive: true
        },
        include: {
          options: {
            orderBy: { order: 'asc' }
          },
          _count: {
            select: { responses: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return polls;
    } catch (error) {
      logger.error('Error getting live polls:', error);
      throw error;
    }
  }

  // Start/Stop poll
  async togglePollLive(pollId, isLive, userId) {
    try {
      const poll = await prisma.poll.findUnique({
        where: { id: pollId }
      });

      if (!poll) {
        throw new Error('Poll not found');
      }

      if (poll.createdBy !== userId) {
        throw new Error('Unauthorized to modify this poll');
      }

      const updatedPoll = await prisma.poll.update({
        where: { id: pollId },
        data: {
          isLive,
          startTime: isLive ? new Date() : poll.startTime,
          endTime: !isLive ? new Date() : null
        }
      });

      logger.info(`Poll ${pollId} ${isLive ? 'started' : 'stopped'} by user ${userId}`);
      return updatedPoll;
    } catch (error) {
      logger.error('Error toggling poll live status:', error);
      throw error;
    }
  }

  // Get poll results
  async getPollResults(pollId) {
    try {
      const poll = await prisma.poll.findUnique({
        where: { id: pollId },
        include: {
          options: {
            orderBy: { order: 'asc' },
            include: {
              responses: {
                include: {
                  user: {
                    select: {
                      arabicName: true,
                      englishName: true
                    }
                  }
                }
              }
            }
          },
          responses: {
            include: {
              option: true,
              user: {
                select: {
                  arabicName: true,
                  englishName: true
                }
              }
            }
          }
        }
      });

      if (!poll) {
        throw new Error('Poll not found');
      }

      const totalResponses = poll.responses.length;
      
      // Calculate detailed statistics
      const optionStats = poll.options.map(option => ({
        id: option.id,
        text: option.text,
        textAr: option.textAr,
        textEn: option.textEn,
        responseCount: option.responses.length,
        percentage: totalResponses > 0 ? (option.responses.length / totalResponses * 100).toFixed(1) : 0,
        isCorrect: option.isCorrect
      }));

      // For rating polls, calculate average
      let averageRating = null;
      if (poll.type === 'RATING') {
        const ratingResponses = poll.responses.filter(r => r.ratingValue);
        if (ratingResponses.length > 0) {
          averageRating = ratingResponses.reduce((sum, r) => sum + r.ratingValue, 0) / ratingResponses.length;
        }
      }

      // Text responses for text polls
      const textResponses = poll.type === 'TEXT' 
        ? poll.responses.filter(r => r.textResponse).map(r => ({
            text: r.textResponse,
            user: r.user ? `${r.user.englishName || r.user.arabicName}` : 'Anonymous',
            createdAt: r.createdAt
          }))
        : [];

      return {
        poll: {
          id: poll.id,
          title: poll.title,
          titleAr: poll.titleAr,
          titleEn: poll.titleEn,
          type: poll.type,
          isAnonymous: poll.isAnonymous
        },
        totalResponses,
        optionStats,
        averageRating,
        textResponses
      };
    } catch (error) {
      logger.error('Error getting poll results:', error);
      throw error;
    }
  }

  // Get user's polls
  async getUserPolls(userId, filters = {}) {
    try {
      const { sessionId, isActive, isLive } = filters;

      const where = { createdBy: userId };

      if (sessionId) where.sessionId = sessionId;
      if (isActive !== undefined) where.isActive = isActive;
      if (isLive !== undefined) where.isLive = isLive;

      const polls = await prisma.poll.findMany({
        where,
        include: {
          session: {
            select: {
              titleAr: true,
              titleEn: true
            }
          },
          _count: {
            select: { responses: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return polls;
    } catch (error) {
      logger.error('Error getting user polls:', error);
      throw error;
    }
  }

  // Delete poll
  async deletePoll(pollId, userId) {
    try {
      const poll = await prisma.poll.findUnique({
        where: { id: pollId }
      });

      if (!poll) {
        throw new Error('Poll not found');
      }

      if (poll.createdBy !== userId) {
        throw new Error('Unauthorized to delete this poll');
      }

      if (poll.isLive) {
        throw new Error('Cannot delete a live poll');
      }

      await prisma.poll.delete({
        where: { id: pollId }
      });

      logger.info(`Poll ${pollId} deleted by user ${userId}`);
    } catch (error) {
      logger.error('Error deleting poll:', error);
      throw error;
    }
  }

  // Get polling statistics
  async getPollingStats() {
    try {
      const [
        totalPolls,
        activePolls,
        livePolls,
        totalResponses,
        pollsByType
      ] = await Promise.all([
        prisma.poll.count(),
        prisma.poll.count({ where: { isActive: true } }),
        prisma.poll.count({ where: { isLive: true } }),
        prisma.pollResponse.count(),
        prisma.poll.groupBy({
          by: ['type'],
          _count: { type: true }
        })
      ]);

      return {
        totalPolls,
        activePolls,
        livePolls,
        totalResponses,
        pollsByType
      };
    } catch (error) {
      logger.error('Error getting polling stats:', error);
      throw error;
    }
  }
}

module.exports = new PollingService();
