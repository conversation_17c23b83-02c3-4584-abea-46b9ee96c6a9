const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gamificationService = require('./gamificationService');

const prisma = new PrismaClient();

class SocialService {
  // Send connection request
  async sendConnectionRequest(requesterId, receiverId, message = null) {
    try {
      // Check if users exist
      const [requester, receiver] = await Promise.all([
        prisma.user.findUnique({ where: { id: requesterId } }),
        prisma.user.findUnique({ where: { id: receiverId } })
      ]);

      if (!requester || !receiver) {
        throw new Error('User not found');
      }

      if (requesterId === receiverId) {
        throw new Error('Cannot send connection request to yourself');
      }

      // Check if connection already exists
      const existingConnection = await prisma.connection.findFirst({
        where: {
          OR: [
            { requesterId, receiverId },
            { requesterId: receiverId, receiverId: requesterId }
          ]
        }
      });

      if (existingConnection) {
        throw new Error('Connection already exists or pending');
      }

      // Create connection request
      const connection = await prisma.connection.create({
        data: {
          requesterId,
          receiverId,
          message,
          status: 'PENDING'
        },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true
            }
          },
          receiver: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true
            }
          }
        }
      });

      // Award points for social interaction
      await gamificationService.awardPoints(
        requesterId,
        'SOCIAL_INTERACTION',
        5,
        'Sent connection request'
      );

      logger.info(`Connection request sent from ${requesterId} to ${receiverId}`);
      return connection;
    } catch (error) {
      logger.error('Error sending connection request:', error);
      throw error;
    }
  }

  // Respond to connection request
  async respondToConnectionRequest(connectionId, userId, response) {
    try {
      const connection = await prisma.connection.findUnique({
        where: { id: connectionId },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true
            }
          }
        }
      });

      if (!connection) {
        throw new Error('Connection request not found');
      }

      if (connection.receiverId !== userId) {
        throw new Error('Unauthorized to respond to this request');
      }

      if (connection.status !== 'PENDING') {
        throw new Error('Connection request already processed');
      }

      const updatedConnection = await prisma.connection.update({
        where: { id: connectionId },
        data: { status: response },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true
            }
          },
          receiver: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true
            }
          }
        }
      });

      // Award points for accepting connection
      if (response === 'ACCEPTED') {
        await Promise.all([
          gamificationService.awardPoints(
            connection.requesterId,
            'SOCIAL_INTERACTION',
            10,
            'Connection accepted'
          ),
          gamificationService.awardPoints(
            userId,
            'SOCIAL_INTERACTION',
            10,
            'Accepted connection request'
          )
        ]);
      }

      logger.info(`Connection request ${response.toLowerCase()} by ${userId}`);
      return updatedConnection;
    } catch (error) {
      logger.error('Error responding to connection request:', error);
      throw error;
    }
  }

  // Get user's connections
  async getUserConnections(userId, status = 'ACCEPTED') {
    try {
      const connections = await prisma.connection.findMany({
        where: {
          OR: [
            { requesterId: userId, status },
            { receiverId: userId, status }
          ]
        },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true,
              university: true
            }
          },
          receiver: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true,
              university: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      // Format connections to show the other user
      const formattedConnections = connections.map(connection => {
        const otherUser = connection.requesterId === userId 
          ? connection.receiver 
          : connection.requester;
        
        return {
          id: connection.id,
          user: otherUser,
          connectedAt: connection.updatedAt,
          status: connection.status
        };
      });

      return formattedConnections;
    } catch (error) {
      logger.error('Error getting user connections:', error);
      throw error;
    }
  }

  // Get pending connection requests
  async getPendingRequests(userId) {
    try {
      const requests = await prisma.connection.findMany({
        where: {
          receiverId: userId,
          status: 'PENDING'
        },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true,
              qualification: true,
              specialization: true,
              university: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      return requests;
    } catch (error) {
      logger.error('Error getting pending requests:', error);
      throw error;
    }
  }

  // Search users for connections
  async searchUsers(searchQuery, currentUserId, filters = {}) {
    try {
      const where = {
        AND: [
          { id: { not: currentUserId } },
          {
            OR: [
              { arabicName: { contains: searchQuery, mode: 'insensitive' } },
              { englishName: { contains: searchQuery, mode: 'insensitive' } },
              { specialization: { contains: searchQuery, mode: 'insensitive' } },
              { university: { contains: searchQuery, mode: 'insensitive' } }
            ]
          }
        ]
      };

      if (filters.qualification) {
        where.AND.push({ qualification: filters.qualification });
      }

      if (filters.specialization) {
        where.AND.push({ specialization: filters.specialization });
      }

      const users = await prisma.user.findMany({
        where,
        select: {
          id: true,
          arabicName: true,
          englishName: true,
          profileImage: true,
          qualification: true,
          specialization: true,
          university: true
        },
        take: 50
      });

      // Check connection status for each user
      const userIds = users.map(user => user.id);
      const connections = await prisma.connection.findMany({
        where: {
          OR: [
            { requesterId: currentUserId, receiverId: { in: userIds } },
            { requesterId: { in: userIds }, receiverId: currentUserId }
          ]
        }
      });

      const connectionMap = {};
      connections.forEach(conn => {
        const otherUserId = conn.requesterId === currentUserId ? conn.receiverId : conn.requesterId;
        connectionMap[otherUserId] = conn.status;
      });

      const usersWithConnectionStatus = users.map(user => ({
        ...user,
        connectionStatus: connectionMap[user.id] || 'NONE'
      }));

      return usersWithConnectionStatus;
    } catch (error) {
      logger.error('Error searching users:', error);
      throw error;
    }
  }

  // Get connection suggestions
  async getConnectionSuggestions(userId, limit = 10) {
    try {
      // Get user's current connections
      const userConnections = await this.getUserConnections(userId);
      const connectedUserIds = userConnections.map(conn => conn.user.id);
      connectedUserIds.push(userId); // Exclude self

      // Get user's profile for matching
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          qualification: true,
          specialization: true,
          university: true
        }
      });

      // Find users with similar profiles
      const suggestions = await prisma.user.findMany({
        where: {
          AND: [
            { id: { notIn: connectedUserIds } },
            {
              OR: [
                { qualification: user.qualification },
                { specialization: user.specialization },
                { university: user.university }
              ]
            }
          ]
        },
        select: {
          id: true,
          arabicName: true,
          englishName: true,
          profileImage: true,
          qualification: true,
          specialization: true,
          university: true
        },
        take: limit
      });

      return suggestions;
    } catch (error) {
      logger.error('Error getting connection suggestions:', error);
      throw error;
    }
  }

  // Remove connection
  async removeConnection(connectionId, userId) {
    try {
      const connection = await prisma.connection.findUnique({
        where: { id: connectionId }
      });

      if (!connection) {
        throw new Error('Connection not found');
      }

      if (connection.requesterId !== userId && connection.receiverId !== userId) {
        throw new Error('Unauthorized to remove this connection');
      }

      await prisma.connection.delete({
        where: { id: connectionId }
      });

      logger.info(`Connection ${connectionId} removed by user ${userId}`);
      return { success: true };
    } catch (error) {
      logger.error('Error removing connection:', error);
      throw error;
    }
  }

  // Get user's social stats
  async getUserSocialStats(userId) {
    try {
      const [connectionsCount, pendingRequestsCount, sentRequestsCount] = await Promise.all([
        prisma.connection.count({
          where: {
            OR: [
              { requesterId: userId, status: 'ACCEPTED' },
              { receiverId: userId, status: 'ACCEPTED' }
            ]
          }
        }),
        prisma.connection.count({
          where: {
            receiverId: userId,
            status: 'PENDING'
          }
        }),
        prisma.connection.count({
          where: {
            requesterId: userId,
            status: 'PENDING'
          }
        })
      ]);

      return {
        connectionsCount,
        pendingRequestsCount,
        sentRequestsCount
      };
    } catch (error) {
      logger.error('Error getting user social stats:', error);
      throw error;
    }
  }
}

module.exports = new SocialService();
