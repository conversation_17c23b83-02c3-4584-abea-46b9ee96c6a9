import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  bool _isInitialized = false;
  List<LocalNotification> _notifications = [];
  Timer? _notificationTimer;

  // Initialize notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // In a real app, you would initialize flutter_local_notifications here
      // For now, we'll simulate the initialization
      await Future.delayed(const Duration(milliseconds: 500));
      
      _isInitialized = true;
      _startNotificationTimer();
      
      debugPrint('Notification service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing notification service: $e');
    }
  }

  // Start periodic notification check
  void _startNotificationTimer() {
    _notificationTimer = Timer.periodic(
      const Duration(minutes: 1),
      (timer) => _checkScheduledNotifications(),
    );
  }

  // Check for scheduled notifications
  void _checkScheduledNotifications() {
    final now = DateTime.now();
    final dueNotifications = _notifications.where((notification) {
      return notification.scheduledTime.isBefore(now) && !notification.isShown;
    }).toList();

    for (final notification in dueNotifications) {
      _showNotification(notification);
      notification.isShown = true;
    }
  }

  // Show notification
  void _showNotification(LocalNotification notification) {
    debugPrint('Showing notification: ${notification.title}');
    // In a real app, this would trigger the actual notification
    // For now, we'll just log it
  }

  // Schedule session reminder
  Future<void> scheduleSessionReminder({
    required String sessionId,
    required String sessionTitle,
    required DateTime sessionTime,
    int minutesBefore = 15,
  }) async {
    if (!_isInitialized) await initialize();

    final reminderTime = sessionTime.subtract(Duration(minutes: minutesBefore));
    
    // Don't schedule if the time has already passed
    if (reminderTime.isBefore(DateTime.now())) return;

    final notification = LocalNotification(
      id: 'session_$sessionId',
      title: 'تذكير بالجلسة',
      body: 'ستبدأ جلسة "$sessionTitle" خلال $minutesBefore دقيقة',
      scheduledTime: reminderTime,
      type: NotificationType.sessionReminder,
      data: {'sessionId': sessionId},
    );

    _notifications.add(notification);
    debugPrint('Scheduled session reminder for: $sessionTitle at $reminderTime');
  }

  // Schedule session start notification
  Future<void> scheduleSessionStart({
    required String sessionId,
    required String sessionTitle,
    required DateTime sessionTime,
  }) async {
    if (!_isInitialized) await initialize();

    final notification = LocalNotification(
      id: 'session_start_$sessionId',
      title: 'بدأت الجلسة الآن',
      body: 'بدأت جلسة "$sessionTitle" الآن. انضم إلينا!',
      scheduledTime: sessionTime,
      type: NotificationType.sessionStart,
      data: {'sessionId': sessionId},
    );

    _notifications.add(notification);
    debugPrint('Scheduled session start notification for: $sessionTitle');
  }

  // Schedule course reminder
  Future<void> scheduleCourseReminder({
    required String courseId,
    required String courseTitle,
    required DateTime courseTime,
    int minutesBefore = 30,
  }) async {
    if (!_isInitialized) await initialize();

    final reminderTime = courseTime.subtract(Duration(minutes: minutesBefore));
    
    if (reminderTime.isBefore(DateTime.now())) return;

    final notification = LocalNotification(
      id: 'course_$courseId',
      title: 'تذكير بالدورة التدريبية',
      body: 'ستبدأ دورة "$courseTitle" خلال $minutesBefore دقيقة',
      scheduledTime: reminderTime,
      type: NotificationType.courseReminder,
      data: {'courseId': courseId},
    );

    _notifications.add(notification);
    debugPrint('Scheduled course reminder for: $courseTitle');
  }

  // Send news notification
  Future<void> sendNewsNotification({
    required String newsId,
    required String title,
    required String summary,
  }) async {
    if (!_isInitialized) await initialize();

    final notification = LocalNotification(
      id: 'news_$newsId',
      title: 'خبر جديد',
      body: title,
      scheduledTime: DateTime.now(),
      type: NotificationType.news,
      data: {'newsId': newsId},
    );

    _showNotification(notification);
    debugPrint('Sent news notification: $title');
  }

  // Send general announcement
  Future<void> sendAnnouncement({
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    if (!_isInitialized) await initialize();

    final notification = LocalNotification(
      id: 'announcement_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      body: message,
      scheduledTime: DateTime.now(),
      type: NotificationType.announcement,
      data: data ?? {},
    );

    _showNotification(notification);
    debugPrint('Sent announcement: $title');
  }

  // Cancel notification
  Future<void> cancelNotification(String id) async {
    _notifications.removeWhere((notification) => notification.id == id);
    debugPrint('Cancelled notification: $id');
  }

  // Cancel all session notifications
  Future<void> cancelSessionNotifications(String sessionId) async {
    _notifications.removeWhere((notification) => 
        notification.id == 'session_$sessionId' || 
        notification.id == 'session_start_$sessionId');
    debugPrint('Cancelled all notifications for session: $sessionId');
  }

  // Get pending notifications
  List<LocalNotification> getPendingNotifications() {
    return _notifications.where((notification) => !notification.isShown).toList();
  }

  // Get notification history
  List<LocalNotification> getNotificationHistory() {
    return _notifications.where((notification) => notification.isShown).toList();
  }

  // Clear all notifications
  Future<void> clearAllNotifications() async {
    _notifications.clear();
    debugPrint('Cleared all notifications');
  }

  // Request notification permissions
  Future<bool> requestPermissions() async {
    // In a real app, this would request actual permissions
    // For now, we'll simulate permission granted
    await Future.delayed(const Duration(milliseconds: 300));
    debugPrint('Notification permissions granted');
    return true;
  }

  // Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    // In a real app, this would check actual permission status
    return true;
  }

  // Dispose resources
  void dispose() {
    _notificationTimer?.cancel();
    _notifications.clear();
    _isInitialized = false;
  }
}

// Local notification model
class LocalNotification {
  final String id;
  final String title;
  final String body;
  final DateTime scheduledTime;
  final NotificationType type;
  final Map<String, dynamic> data;
  bool isShown;

  LocalNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledTime,
    required this.type,
    this.data = const {},
    this.isShown = false,
  });

  @override
  String toString() {
    return 'LocalNotification(id: $id, title: $title, scheduledTime: $scheduledTime)';
  }
}

// Notification types
enum NotificationType {
  sessionReminder,
  sessionStart,
  courseReminder,
  news,
  announcement,
  social,
  gamification,
}

// Notification helper functions
class NotificationHelper {
  // Schedule multiple session reminders
  static Future<void> scheduleSessionReminders({
    required String sessionId,
    required String sessionTitle,
    required DateTime sessionTime,
  }) async {
    final service = NotificationService();
    
    // Schedule 30 minutes before
    await service.scheduleSessionReminder(
      sessionId: sessionId,
      sessionTitle: sessionTitle,
      sessionTime: sessionTime,
      minutesBefore: 30,
    );
    
    // Schedule 15 minutes before
    await service.scheduleSessionReminder(
      sessionId: sessionId,
      sessionTitle: sessionTitle,
      sessionTime: sessionTime,
      minutesBefore: 15,
    );
    
    // Schedule session start
    await service.scheduleSessionStart(
      sessionId: sessionId,
      sessionTitle: sessionTitle,
      sessionTime: sessionTime,
    );
  }

  // Format notification time
  static String formatNotificationTime(DateTime time) {
    final now = DateTime.now();
    final difference = time.difference(now);
    
    if (difference.inDays > 0) {
      return 'خلال ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'خلال ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'خلال ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  // Get notification icon based on type
  static IconData getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.sessionReminder:
      case NotificationType.sessionStart:
        return Icons.event;
      case NotificationType.courseReminder:
        return Icons.school;
      case NotificationType.news:
        return Icons.newspaper;
      case NotificationType.announcement:
        return Icons.campaign;
      case NotificationType.social:
        return Icons.people;
      case NotificationType.gamification:
        return Icons.emoji_events;
    }
  }
}
