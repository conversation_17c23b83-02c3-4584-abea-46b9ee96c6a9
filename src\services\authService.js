const bcrypt = require('bcryptjs');
const { generateToken, generateRefreshToken } = require('../middleware/auth');
const { ValidationError, AuthenticationError } = require('../middleware/errorHandler');
const userService = require('./userService');
const logger = require('../utils/logger');

class AuthService {
  constructor() {
    this.saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
  }

  // Hash password
  async hashPassword(password) {
    if (!password) {
      throw new ValidationError('Password cannot be empty');
    }
    
    return await bcrypt.hash(password, this.saltRounds);
  }

  // Compare password
  async comparePassword(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  }

  // Validate user data
  validateUserData(userData) {
    const errors = [];
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      errors.push('Invalid email format');
    }
    
    // Phone validation (Saudi format)
    const phoneRegex = /^\+966[0-9]{9}$/;
    if (!phoneRegex.test(userData.phone)) {
      errors.push('Invalid phone number format');
    }
    
    // Arabic name validation
    const arabicRegex = /^[\u0600-\u06FF\s]+$/;
    if (!arabicRegex.test(userData.arabicName)) {
      errors.push('Arabic name must contain only Arabic characters');
    }
    
    // English name validation
    const englishRegex = /^[a-zA-Z\s]+$/;
    if (!englishRegex.test(userData.englishName)) {
      errors.push('English name must contain only English characters');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Register new user
  async register(userData) {
    try {
      // Validate user data
      const validation = this.validateUserData(userData);
      if (!validation.isValid) {
        throw new ValidationError('Invalid user data', validation.errors);
      }

      // Create user using userService
      const newUser = await userService.createUser(userData);

      // Get user roles
      const roles = await userService.getUserRoles(newUser.id);
      const userWithRoles = { ...newUser, roles };

      logger.logUserAction(newUser.id, 'USER_REGISTERED', {
        email: userData.email,
        qualification: userData.qualification
      });

      // Generate tokens
      const accessToken = generateToken(userWithRoles);
      const refreshToken = generateRefreshToken(userWithRoles);

      return {
        user: newUser,
        tokens: {
          accessToken,
          refreshToken
        }
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  // Login user
  async login(email, password) {
    try {
      // Get user from database
      const user = await userService.findUserByEmail(email);

      // Check if user exists
      if (!user) {
        throw new AuthenticationError('Invalid email or password');
      }

      // Check password
      const isPasswordValid = await this.comparePassword(password, user.passwordHash);
      if (!isPasswordValid) {
        throw new AuthenticationError('Invalid email or password');
      }

      // Check user status
      if (user.status !== 'ACTIVE') {
        throw new AuthenticationError('Account is not active');
      }

      // Get user roles
      const roles = await userService.getUserRoles(user.id);
      const userWithRoles = { ...user, roles };

      logger.logUserAction(user.id, 'USER_LOGIN', {
        email: user.email
      });

      // Generate tokens
      const accessToken = generateToken(userWithRoles);
      const refreshToken = generateRefreshToken(userWithRoles);

      // Remove sensitive data
      const { passwordHash, ...userWithoutPassword } = user;

      return {
        user: { ...userWithoutPassword, roles },
        tokens: {
          accessToken,
          refreshToken
        }
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  // Refresh token
  async refreshToken(refreshToken) {
    try {
      // TODO: Verify refresh token and get user
      // Mock implementation
      const user = {
        id: 'user_123',
        email: '<EMAIL>',
        roles: ['participant']
      };

      const newAccessToken = generateToken(user);
      const newRefreshToken = generateRefreshToken(user);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw new AuthenticationError('Invalid refresh token');
    }
  }

  // Logout user
  async logout(token) {
    try {
      // TODO: Add token to blacklist
      const { session } = require('../config/redis');
      
      // Calculate TTL based on token expiration
      const ttl = 24 * 60 * 60; // 24 hours in seconds
      await session.blacklistToken(token, ttl);

      logger.info('User logged out', { token: token.substring(0, 10) + '...' });
      
      return { success: true };
    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  // Generate OTP
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Send OTP (mock implementation)
  async sendOTP(phone, otp) {
    try {
      // TODO: Integrate with SMS service
      logger.info('OTP sent', { phone, otp: process.env.NODE_ENV === 'development' ? otp : '******' });
      
      // Store OTP in cache for verification
      const { cache } = require('../config/redis');
      await cache.set(`otp:${phone}`, otp, 300); // 5 minutes expiry
      
      return { success: true };
    } catch (error) {
      logger.error('Failed to send OTP:', error);
      throw error;
    }
  }

  // Verify OTP
  async verifyOTP(phone, otp) {
    try {
      const { cache } = require('../config/redis');
      const storedOTP = await cache.get(`otp:${phone}`);
      
      if (!storedOTP || storedOTP !== otp) {
        throw new ValidationError('Invalid or expired OTP');
      }
      
      // Remove OTP after successful verification
      await cache.del(`otp:${phone}`);
      
      logger.logUserAction(null, 'OTP_VERIFIED', { phone });
      
      return { success: true };
    } catch (error) {
      logger.error('OTP verification failed:', error);
      throw error;
    }
  }
}

module.exports = new AuthService();
