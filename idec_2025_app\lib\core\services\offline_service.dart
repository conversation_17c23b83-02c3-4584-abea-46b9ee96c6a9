import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/session_model.dart';
import '../models/user_model.dart';

class OfflineService {
  static final OfflineService _instance = OfflineService._internal();
  factory OfflineService() => _instance;
  OfflineService._internal();

  static const String _sessionsKey = 'offline_sessions';
  static const String _userKey = 'offline_user';
  static const String _speakersKey = 'offline_speakers';
  static const String _newsKey = 'offline_news';
  static const String _exhibitorsKey = 'offline_exhibitors';
  static const String _libraryKey = 'offline_library';
  static const String _lastSyncKey = 'last_sync_time';

  bool _isOfflineMode = false;
  DateTime? _lastSyncTime;

  // Getters
  bool get isOfflineMode => _isOfflineMode;
  DateTime? get lastSyncTime => _lastSyncTime;

  // Initialize offline service
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSyncString = prefs.getString(_lastSyncKey);
      if (lastSyncString != null) {
        _lastSyncTime = DateTime.parse(lastSyncString);
      }
      
      // Check if we have offline data
      final hasOfflineData = await _hasOfflineData();
      debugPrint('Offline service initialized. Has offline data: $hasOfflineData');
    } catch (e) {
      debugPrint('Error initializing offline service: $e');
    }
  }

  // Check if we have offline data
  Future<bool> _hasOfflineData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_sessionsKey) && 
             prefs.containsKey(_userKey);
    } catch (e) {
      return false;
    }
  }

  // Enable offline mode
  void enableOfflineMode() {
    _isOfflineMode = true;
    debugPrint('Offline mode enabled');
  }

  // Disable offline mode
  void disableOfflineMode() {
    _isOfflineMode = false;
    debugPrint('Offline mode disabled');
  }

  // Cache sessions data
  Future<void> cacheSessions(List<Session> sessions) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsJson = sessions.map((session) => session.toJson()).toList();
      await prefs.setString(_sessionsKey, jsonEncode(sessionsJson));
      debugPrint('Cached ${sessions.length} sessions offline');
    } catch (e) {
      debugPrint('Error caching sessions: $e');
    }
  }

  // Get cached sessions
  Future<List<Session>> getCachedSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsString = prefs.getString(_sessionsKey);
      
      if (sessionsString != null) {
        final List<dynamic> sessionsJson = jsonDecode(sessionsString);
        return sessionsJson.map((json) => Session.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached sessions: $e');
      return [];
    }
  }

  // Cache user data
  Future<void> cacheUser(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, jsonEncode(user.toJson()));
      debugPrint('Cached user data offline');
    } catch (e) {
      debugPrint('Error caching user: $e');
    }
  }

  // Get cached user
  Future<User?> getCachedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userString = prefs.getString(_userKey);
      
      if (userString != null) {
        final userJson = jsonDecode(userString);
        return User.fromJson(userJson);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting cached user: $e');
      return null;
    }
  }

  // Cache speakers data
  Future<void> cacheSpeakers(List<Speaker> speakers) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final speakersJson = speakers.map((speaker) => speaker.toJson()).toList();
      await prefs.setString(_speakersKey, jsonEncode(speakersJson));
      debugPrint('Cached ${speakers.length} speakers offline');
    } catch (e) {
      debugPrint('Error caching speakers: $e');
    }
  }

  // Get cached speakers
  Future<List<Speaker>> getCachedSpeakers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final speakersString = prefs.getString(_speakersKey);
      
      if (speakersString != null) {
        final List<dynamic> speakersJson = jsonDecode(speakersString);
        return speakersJson.map((json) => Speaker.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached speakers: $e');
      return [];
    }
  }

  // Cache news data
  Future<void> cacheNews(List<Map<String, dynamic>> news) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_newsKey, jsonEncode(news));
      debugPrint('Cached ${news.length} news items offline');
    } catch (e) {
      debugPrint('Error caching news: $e');
    }
  }

  // Get cached news
  Future<List<Map<String, dynamic>>> getCachedNews() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newsString = prefs.getString(_newsKey);
      
      if (newsString != null) {
        final List<dynamic> newsJson = jsonDecode(newsString);
        return newsJson.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached news: $e');
      return [];
    }
  }

  // Cache exhibitors data
  Future<void> cacheExhibitors(List<Map<String, dynamic>> exhibitors) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_exhibitorsKey, jsonEncode(exhibitors));
      debugPrint('Cached ${exhibitors.length} exhibitors offline');
    } catch (e) {
      debugPrint('Error caching exhibitors: $e');
    }
  }

  // Get cached exhibitors
  Future<List<Map<String, dynamic>>> getCachedExhibitors() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final exhibitorsString = prefs.getString(_exhibitorsKey);
      
      if (exhibitorsString != null) {
        final List<dynamic> exhibitorsJson = jsonDecode(exhibitorsString);
        return exhibitorsJson.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached exhibitors: $e');
      return [];
    }
  }

  // Cache library data
  Future<void> cacheLibrary(List<Map<String, dynamic>> library) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_libraryKey, jsonEncode(library));
      debugPrint('Cached ${library.length} library items offline');
    } catch (e) {
      debugPrint('Error caching library: $e');
    }
  }

  // Get cached library
  Future<List<Map<String, dynamic>>> getCachedLibrary() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final libraryString = prefs.getString(_libraryKey);
      
      if (libraryString != null) {
        final List<dynamic> libraryJson = jsonDecode(libraryString);
        return libraryJson.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      debugPrint('Error getting cached library: $e');
      return [];
    }
  }

  // Update last sync time
  Future<void> updateLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lastSyncTime = DateTime.now();
      await prefs.setString(_lastSyncKey, _lastSyncTime!.toIso8601String());
      debugPrint('Updated last sync time: $_lastSyncTime');
    } catch (e) {
      debugPrint('Error updating last sync time: $e');
    }
  }

  // Check if data is stale (older than 24 hours)
  bool isDataStale() {
    if (_lastSyncTime == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(_lastSyncTime!);
    return difference.inHours > 24;
  }

  // Clear all cached data
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionsKey);
      await prefs.remove(_userKey);
      await prefs.remove(_speakersKey);
      await prefs.remove(_newsKey);
      await prefs.remove(_exhibitorsKey);
      await prefs.remove(_libraryKey);
      await prefs.remove(_lastSyncKey);
      
      _lastSyncTime = null;
      debugPrint('Cleared all offline cache');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  // Get cache size info
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final sessions = await getCachedSessions();
      final speakers = await getCachedSpeakers();
      final news = await getCachedNews();
      final exhibitors = await getCachedExhibitors();
      final library = await getCachedLibrary();
      
      return {
        'sessions': sessions.length,
        'speakers': speakers.length,
        'news': news.length,
        'exhibitors': exhibitors.length,
        'library': library.length,
        'lastSync': _lastSyncTime?.toIso8601String(),
        'isStale': isDataStale(),
      };
    } catch (e) {
      debugPrint('Error getting cache info: $e');
      return {};
    }
  }

  // Sync all data for offline use
  Future<void> syncAllData({
    required List<Session> sessions,
    required User user,
    required List<Speaker> speakers,
    required List<Map<String, dynamic>> news,
    required List<Map<String, dynamic>> exhibitors,
    required List<Map<String, dynamic>> library,
  }) async {
    try {
      await Future.wait([
        cacheSessions(sessions),
        cacheUser(user),
        cacheSpeakers(speakers),
        cacheNews(news),
        cacheExhibitors(exhibitors),
        cacheLibrary(library),
      ]);
      
      await updateLastSyncTime();
      debugPrint('Successfully synced all data for offline use');
    } catch (e) {
      debugPrint('Error syncing data: $e');
      throw e;
    }
  }
}

// Offline data model
class OfflineData {
  final List<Session> sessions;
  final User? user;
  final List<Speaker> speakers;
  final List<Map<String, dynamic>> news;
  final List<Map<String, dynamic>> exhibitors;
  final List<Map<String, dynamic>> library;
  final DateTime? lastSync;

  OfflineData({
    required this.sessions,
    this.user,
    required this.speakers,
    required this.news,
    required this.exhibitors,
    required this.library,
    this.lastSync,
  });

  bool get isEmpty => sessions.isEmpty && speakers.isEmpty && news.isEmpty;
  bool get isComplete => user != null && sessions.isNotEmpty;
}
