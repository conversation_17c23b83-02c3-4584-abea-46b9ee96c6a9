// Dashboard Configuration
const API_BASE_URL = 'http://localhost:3000/api/v1';
let currentUser = null;
let authToken = localStorage.getItem('adminToken');

// Initialize Dashboard
document.addEventListener('DOMContentLoaded', function() {
    if (!authToken) {
        window.location.href = 'login.html';
        return;
    }
    
    initializeDashboard();
    setupEventListeners();
    loadDashboardData();
});

// Setup Event Listeners
function setupEventListeners() {
    // Sidebar navigation
    document.querySelectorAll('.nav-link[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
            
            // Update active state
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Search functionality
    const userSearch = document.getElementById('userSearch');
    if (userSearch) {
        userSearch.addEventListener('input', debounce(searchUsers, 300));
    }
}

// Initialize Dashboard
async function initializeDashboard() {
    try {
        // Get current user info
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            currentUser = await response.json();
            updateUserInfo();
        } else {
            throw new Error('Failed to get user info');
        }
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        Swal.fire({
            icon: 'error',
            title: 'خطأ في التحميل',
            text: 'حدث خطأ أثناء تحميل بيانات المستخدم',
            confirmButtonText: 'موافق'
        });
    }
}

// Update User Info
function updateUserInfo() {
    if (currentUser && currentUser.data) {
        const user = currentUser.data;
        document.getElementById('userName').textContent = user.arabicName || user.englishName || 'المدير';
        
        // Update role display
        const roleElement = document.getElementById('userRole');
        if (user.userRoles && user.userRoles.length > 0) {
            const role = user.userRoles[0].role.name;
            roleElement.textContent = getRoleDisplayName(role);
        }
    }
}

// Get Role Display Name
function getRoleDisplayName(role) {
    const roleNames = {
        'super_admin': 'مدير النظام',
        'admin': 'مشرف عام',
        'admission_committee': 'لجنة القبول',
        'accountant': 'محاسب',
        'scientific_committee': 'اللجنة العلمية',
        'media_committee': 'اللجنة الإعلامية',
        'exhibition_manager': 'مدير المعرض',
        'preparatory_committee': 'اللجنة التحضيرية'
    };
    return roleNames[role] || role;
}

// Show Section
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        
        // Load section data
        switch(sectionName) {
            case 'dashboard':
                loadDashboardData();
                break;
            case 'applications':
                loadApplications();
                break;
            case 'users':
                loadUsers();
                break;
            case 'accounting':
                loadAccountingData();
                break;
        }
    }
}

// Load Dashboard Data
async function loadDashboardData() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin/dashboard/stats`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            updateDashboardStats(data.data);
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Update Dashboard Stats
function updateDashboardStats(stats) {
    // Update main stats
    document.getElementById('totalUsers').textContent = stats.users?.total || 0;
    document.getElementById('activeSubscriptions').textContent = stats.subscriptions?.total || 0;
    document.getElementById('pendingApplications').textContent = stats.subscriptions?.pending || 0;
    document.getElementById('totalRevenue').textContent = formatCurrency(stats.payments?.revenue || 0);
    
    // Update notification badge
    const pendingCount = stats.subscriptions?.pending || 0;
    document.getElementById('pendingCount').textContent = pendingCount;
    
    // Update quick stats
    const total = stats.subscriptions?.total || 1;
    const approved = (stats.subscriptions?.total || 0) - (stats.subscriptions?.pending || 0);
    const approvalRate = Math.round((approved / total) * 100);
    
    document.getElementById('approvalRate').textContent = `${approvalRate}%`;
    document.querySelector('#approvalRate').parentElement.nextElementSibling.firstElementChild.style.width = `${approvalRate}%`;
    
    // Update recent activities
    updateRecentActivities(stats.recentActivities || []);
    
    // Update last update time
    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString('ar-SA');
}

// Update Recent Activities
function updateRecentActivities(activities) {
    const container = document.getElementById('recentActivities');
    container.classList.remove('loading');
    
    if (activities.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد أنشطة حديثة</p>';
        return;
    }
    
    const activitiesHtml = activities.map(activity => `
        <div class="d-flex align-items-center mb-3 p-2 border-bottom">
            <div class="me-3">
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <div class="flex-grow-1">
                <div class="fw-bold">${activity.arabicName || activity.englishName}</div>
                <small class="text-muted">${activity.email}</small>
                <div class="small text-muted">${formatDate(activity.createdAt)}</div>
            </div>
            <div>
                <span class="badge ${getStatusBadgeClass(activity.status)}">${getStatusDisplayName(activity.status)}</span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = activitiesHtml;
}

// Load Applications
async function loadApplications() {
    try {
        const response = await fetch(`${API_BASE_URL}/admission/applications/pending`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updateApplicationsTable(data.data.applications);
        }
    } catch (error) {
        console.error('Error loading applications:', error);
        document.getElementById('applicationsTable').innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    حدث خطأ أثناء تحميل البيانات
                </td>
            </tr>
        `;
    }
}

// Update Applications Table
function updateApplicationsTable(applications) {
    const tbody = document.getElementById('applicationsTable');
    
    if (applications.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-muted">
                    <i class="fas fa-inbox"></i>
                    لا توجد طلبات في الانتظار
                </td>
            </tr>
        `;
        return;
    }
    
    const applicationsHtml = applications.map(app => `
        <tr>
            <td>
                <div class="fw-bold">${app.user.arabicName}</div>
                <small class="text-muted">${app.user.englishName}</small>
            </td>
            <td>${app.user.email}</td>
            <td>
                <span class="badge bg-info">${app.user.qualification}</span>
                ${app.user.specialization ? `<br><small class="text-muted">${app.user.specialization}</small>` : ''}
            </td>
            <td>${formatDate(app.createdAt)}</td>
            <td>
                <span class="badge ${getStatusBadgeClass(app.status)}">${getStatusDisplayName(app.status)}</span>
            </td>
            <td>
                <button class="btn btn-success btn-action" onclick="reviewApplication('${app.id}', 'APPROVED')">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
                <button class="btn btn-danger btn-action" onclick="reviewApplication('${app.id}', 'REJECTED')">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
                <button class="btn btn-info btn-action" onclick="viewApplicationDetails('${app.id}')">
                    <i class="fas fa-eye"></i>
                    عرض
                </button>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = applicationsHtml;
}

// Review Application
async function reviewApplication(subscriptionId, status) {
    const result = await Swal.fire({
        title: status === 'APPROVED' ? 'موافقة على الطلب' : 'رفض الطلب',
        text: `هل أنت متأكد من ${status === 'APPROVED' ? 'الموافقة على' : 'رفض'} هذا الطلب؟`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء',
        input: status === 'REJECTED' ? 'textarea' : null,
        inputPlaceholder: status === 'REJECTED' ? 'سبب الرفض (اختياري)' : null
    });
    
    if (result.isConfirmed) {
        try {
            const response = await fetch(`${API_BASE_URL}/admission/applications/${subscriptionId}/review`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    status,
                    comments: result.value || ''
                })
            });
            
            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'تم بنجاح',
                    text: `تم ${status === 'APPROVED' ? 'الموافقة على' : 'رفض'} الطلب بنجاح`,
                    confirmButtonText: 'موافق'
                });
                loadApplications(); // Reload applications
                loadDashboardData(); // Update stats
            } else {
                throw new Error('Failed to review application');
            }
        } catch (error) {
            console.error('Error reviewing application:', error);
            Swal.fire({
                icon: 'error',
                title: 'خطأ',
                text: 'حدث خطأ أثناء معالجة الطلب',
                confirmButtonText: 'موافق'
            });
        }
    }
}

// Utility Functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getStatusBadgeClass(status) {
    const classes = {
        'PENDING_REVIEW': 'bg-warning',
        'DOCUMENT_VERIFICATION': 'bg-info',
        'APPROVED_PENDING_PAYMENT': 'bg-primary',
        'ACTIVE': 'bg-success',
        'REJECTED': 'bg-danger',
        'SUSPENDED': 'bg-secondary'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusDisplayName(status) {
    const names = {
        'PENDING_REVIEW': 'في انتظار المراجعة',
        'DOCUMENT_VERIFICATION': 'مراجعة الوثائق',
        'APPROVED_PENDING_PAYMENT': 'موافق عليه - في انتظار الدفع',
        'ACTIVE': 'نشط',
        'REJECTED': 'مرفوض',
        'SUSPENDED': 'معلق'
    };
    return names[status] || status;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Refresh Data
function refreshData() {
    const currentSection = document.querySelector('.content-section.active').id;
    showSection(currentSection);
    
    // Show refresh animation
    const refreshBtn = event.target.closest('button');
    const icon = refreshBtn.querySelector('i');
    icon.classList.add('fa-spin');
    setTimeout(() => icon.classList.remove('fa-spin'), 1000);
}

// Logout
function logout() {
    Swal.fire({
        title: 'تسجيل الخروج',
        text: 'هل أنت متأكد من تسجيل الخروج؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            localStorage.removeItem('adminToken');
            window.location.href = 'login.html';
        }
    });
}

// Placeholder functions for other features
function loadUsers() {
    console.log('Loading users...');
}

function loadAccountingData() {
    console.log('Loading accounting data...');
}

function searchUsers() {
    console.log('Searching users...');
}

function filterApplications(filter) {
    console.log('Filtering applications:', filter);
}

function viewApplicationDetails(id) {
    console.log('Viewing application details:', id);
}

function showAddUserModal() {
    console.log('Show add user modal');
}

function showManualReceiptModal() {
    console.log('Show manual receipt modal');
}

function exportFinancialData() {
    console.log('Export financial data');
}
