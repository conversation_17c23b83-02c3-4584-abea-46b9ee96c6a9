import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { getPrisma } from '@/config/database';
import { cacheService } from '@/config/redis';
import { config } from '@/config/config';
import { ApiError } from '@/utils/ApiError';
import { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken 
} from '@/middleware/auth.middleware';
import { 
  logger, 
  logBusinessEvent, 
  logSecurityEvent 
} from '@/utils/logger';
import { EmailService } from '@/services/email.service';
import { SmsService } from '@/services/sms.service';

export class AuthController {
  private emailService: EmailService;
  private smsService: SmsService;

  constructor() {
    this.emailService = new EmailService();
    this.smsService = new SmsService();
  }

  // Register new user
  public register = async (req: Request, res: Response): Promise<void> => {
    const {
      email,
      password,
      arabicName,
      englishName,
      phone,
      qualification,
      specialization,
      university,
      graduationYear,
      licenseNumber,
      nationality,
      gender,
      birthDate,
    } = req.body;

    const prisma = getPrisma();

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          ...(phone ? [{ phone }] : []),
        ],
      },
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ApiError(409, 'Email already registered', 'EMAIL_EXISTS');
      }
      if (existingUser.phone === phone) {
        throw new ApiError(409, 'Phone number already registered', 'PHONE_EXISTS');
      }
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, config.encryption.bcryptRounds);

    // Create user
    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        arabicName,
        englishName,
        phone,
        qualification,
        specialization,
        university,
        graduationYear,
        licenseNumber,
        nationality,
        gender,
        birthDate: birthDate ? new Date(birthDate) : null,
        status: 'PENDING',
      },
      select: {
        id: true,
        email: true,
        arabicName: true,
        englishName: true,
        phone: true,
        qualification: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
      },
    });

    // Generate email verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    await cacheService.set(
      `email_verification:${user.id}`,
      verificationToken,
      24 * 60 * 60 // 24 hours
    );

    // Send verification email
    try {
      await this.emailService.sendVerificationEmail(user.email, user.arabicName, verificationToken);
    } catch (error) {
      logger.error('Failed to send verification email:', error);
      // Don't fail registration if email fails
    }

    // Log business event
    logBusinessEvent('USER_REGISTERED', 'User', user.id, user.id, {
      email: user.email,
      qualification: user.qualification,
    });

    // Generate tokens
    const accessToken = generateToken({
      userId: user.id,
      email: user.email,
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      email: user.email,
    });

    // Store refresh token
    await cacheService.set(
      `refresh_token:${user.id}`,
      refreshToken,
      7 * 24 * 60 * 60 // 7 days
    );

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please verify your email.',
      data: {
        user,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  };

  // Login user
  public login = async (req: Request, res: Response): Promise<void> => {
    const { email, password } = req.body;
    const prisma = getPrisma();

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        passwordHash: true,
        arabicName: true,
        englishName: true,
        phone: true,
        qualification: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      logSecurityEvent('LOGIN_FAILED', 'medium', {
        email,
        reason: 'User not found',
        ip: req.ip,
      });
      throw new ApiError(401, 'Invalid credentials', 'INVALID_CREDENTIALS');
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      logSecurityEvent('LOGIN_FAILED', 'medium', {
        userId: user.id,
        email,
        reason: 'Invalid password',
        ip: req.ip,
      });
      throw new ApiError(401, 'Invalid credentials', 'INVALID_CREDENTIALS');
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      logSecurityEvent('LOGIN_FAILED', 'medium', {
        userId: user.id,
        email,
        reason: 'Account not active',
        status: user.status,
        ip: req.ip,
      });
      throw new ApiError(401, 'Account is not active', 'ACCOUNT_NOT_ACTIVE');
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // Generate tokens
    const accessToken = generateToken({
      userId: user.id,
      email: user.email,
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      email: user.email,
    });

    // Store refresh token
    await cacheService.set(
      `refresh_token:${user.id}`,
      refreshToken,
      7 * 24 * 60 * 60 // 7 days
    );

    // Log successful login
    logBusinessEvent('USER_LOGIN', 'User', user.id, user.id, {
      email: user.email,
      ip: req.ip,
    });

    // Remove password hash from response
    const { passwordHash, ...userResponse } = user;

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  };

  // Logout user
  public logout = async (req: Request, res: Response): Promise<void> => {
    const userId = req.user!.id;

    // Remove refresh token from cache
    await cacheService.del(`refresh_token:${userId}`);

    // Log logout event
    logBusinessEvent('USER_LOGOUT', 'User', userId, userId, {
      ip: req.ip,
    });

    res.json({
      success: true,
      message: 'Logout successful',
    });
  };

  // Refresh access token
  public refreshToken = async (req: Request, res: Response): Promise<void> => {
    const { refreshToken } = req.body;

    try {
      // Verify refresh token
      const decoded = verifyRefreshToken(refreshToken);
      
      // Check if refresh token exists in cache
      const storedToken = await cacheService.get(`refresh_token:${decoded.userId}`);
      if (!storedToken || storedToken !== refreshToken) {
        throw new ApiError(401, 'Invalid refresh token', 'INVALID_REFRESH_TOKEN');
      }

      // Get user
      const prisma = getPrisma();
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          status: true,
          emailVerified: true,
        },
      });

      if (!user || user.status !== 'ACTIVE') {
        throw new ApiError(401, 'User not found or inactive', 'USER_INACTIVE');
      }

      // Generate new tokens
      const newAccessToken = generateToken({
        userId: user.id,
        email: user.email,
      });

      const newRefreshToken = generateRefreshToken({
        userId: user.id,
        email: user.email,
      });

      // Update refresh token in cache
      await cacheService.set(
        `refresh_token:${user.id}`,
        newRefreshToken,
        7 * 24 * 60 * 60 // 7 days
      );

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          tokens: {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
          },
        },
      });
    } catch (error) {
      logSecurityEvent('TOKEN_REFRESH_FAILED', 'medium', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: req.ip,
      });
      throw new ApiError(401, 'Invalid refresh token', 'INVALID_REFRESH_TOKEN');
    }
  };

  // Forgot password
  public forgotPassword = async (req: Request, res: Response): Promise<void> => {
    const { email } = req.body;
    const prisma = getPrisma();

    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        arabicName: true,
        status: true,
      },
    });

    // Always return success to prevent email enumeration
    if (!user) {
      res.json({
        success: true,
        message: 'If the email exists, a reset link has been sent.',
      });
      return;
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    await cacheService.set(
      `password_reset:${user.id}`,
      resetToken,
      60 * 60 // 1 hour
    );

    // Send reset email
    try {
      await this.emailService.sendPasswordResetEmail(user.email, user.arabicName, resetToken);
      
      logBusinessEvent('PASSWORD_RESET_REQUESTED', 'User', user.id, user.id, {
        email: user.email,
        ip: req.ip,
      });
    } catch (error) {
      logger.error('Failed to send password reset email:', error);
    }

    res.json({
      success: true,
      message: 'If the email exists, a reset link has been sent.',
    });
  };

  // Reset password
  public resetPassword = async (req: Request, res: Response): Promise<void> => {
    const { token, password } = req.body;

    // Find user by token
    const keys = await cacheService.keys('password_reset:*');
    let userId: string | null = null;

    for (const key of keys) {
      const storedToken = await cacheService.get(key);
      if (storedToken === token) {
        userId = key.split(':')[1];
        break;
      }
    }

    if (!userId) {
      throw new ApiError(400, 'Invalid or expired reset token', 'INVALID_RESET_TOKEN');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(password, config.encryption.bcryptRounds);

    // Update password
    const prisma = getPrisma();
    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash },
    });

    // Remove reset token
    await cacheService.del(`password_reset:${userId}`);

    // Remove all refresh tokens for this user
    await cacheService.del(`refresh_token:${userId}`);

    logBusinessEvent('PASSWORD_RESET_COMPLETED', 'User', userId, userId, {
      ip: req.ip,
    });

    res.json({
      success: true,
      message: 'Password reset successfully',
    });
  };

  // Change password
  public changePassword = async (req: Request, res: Response): Promise<void> => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.id;
    const prisma = getPrisma();

    // Get user with current password
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        passwordHash: true,
      },
    });

    if (!user) {
      throw new ApiError(404, 'User not found', 'USER_NOT_FOUND');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw new ApiError(400, 'Current password is incorrect', 'INVALID_CURRENT_PASSWORD');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(newPassword, config.encryption.bcryptRounds);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash },
    });

    // Remove all refresh tokens for this user
    await cacheService.del(`refresh_token:${userId}`);

    logBusinessEvent('PASSWORD_CHANGED', 'User', userId, userId, {
      ip: req.ip,
    });

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  };

  // Verify email
  public verifyEmail = async (req: Request, res: Response): Promise<void> => {
    const { token } = req.body;

    // Find user by token
    const keys = await cacheService.keys('email_verification:*');
    let userId: string | null = null;

    for (const key of keys) {
      const storedToken = await cacheService.get(key);
      if (storedToken === token) {
        userId = key.split(':')[1];
        break;
      }
    }

    if (!userId) {
      throw new ApiError(400, 'Invalid or expired verification token', 'INVALID_VERIFICATION_TOKEN');
    }

    // Update user email verification status
    const prisma = getPrisma();
    await prisma.user.update({
      where: { id: userId },
      data: { emailVerified: true },
    });

    // Remove verification token
    await cacheService.del(`email_verification:${userId}`);

    logBusinessEvent('EMAIL_VERIFIED', 'User', userId, userId);

    res.json({
      success: true,
      message: 'Email verified successfully',
    });
  };

  // Resend email verification
  public resendVerification = async (req: Request, res: Response): Promise<void> => {
    const userId = req.user!.id;
    const prisma = getPrisma();

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        arabicName: true,
        emailVerified: true,
      },
    });

    if (!user) {
      throw new ApiError(404, 'User not found', 'USER_NOT_FOUND');
    }

    if (user.emailVerified) {
      throw new ApiError(400, 'Email already verified', 'EMAIL_ALREADY_VERIFIED');
    }

    // Generate new verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    await cacheService.set(
      `email_verification:${user.id}`,
      verificationToken,
      24 * 60 * 60 // 24 hours
    );

    // Send verification email
    try {
      await this.emailService.sendVerificationEmail(user.email, user.arabicName, verificationToken);
    } catch (error) {
      logger.error('Failed to send verification email:', error);
      throw new ApiError(500, 'Failed to send verification email', 'EMAIL_SEND_FAILED');
    }

    res.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  };

  // Send phone verification
  public sendPhoneVerification = async (req: Request, res: Response): Promise<void> => {
    const userId = req.user!.id;
    const prisma = getPrisma();

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        phone: true,
        phoneVerified: true,
      },
    });

    if (!user || !user.phone) {
      throw new ApiError(400, 'Phone number not found', 'PHONE_NOT_FOUND');
    }

    if (user.phoneVerified) {
      throw new ApiError(400, 'Phone already verified', 'PHONE_ALREADY_VERIFIED');
    }

    // Generate verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    await cacheService.set(
      `phone_verification:${user.id}`,
      verificationCode,
      10 * 60 // 10 minutes
    );

    // Send SMS
    try {
      await this.smsService.sendVerificationCode(user.phone, verificationCode);
    } catch (error) {
      logger.error('Failed to send verification SMS:', error);
      throw new ApiError(500, 'Failed to send verification SMS', 'SMS_SEND_FAILED');
    }

    res.json({
      success: true,
      message: 'Verification code sent successfully',
    });
  };

  // Verify phone
  public verifyPhone = async (req: Request, res: Response): Promise<void> => {
    const { code } = req.body;
    const userId = req.user!.id;

    // Get stored verification code
    const storedCode = await cacheService.get(`phone_verification:${userId}`);
    if (!storedCode || storedCode !== code) {
      throw new ApiError(400, 'Invalid or expired verification code', 'INVALID_VERIFICATION_CODE');
    }

    // Update user phone verification status
    const prisma = getPrisma();
    await prisma.user.update({
      where: { id: userId },
      data: { phoneVerified: true },
    });

    // Remove verification code
    await cacheService.del(`phone_verification:${userId}`);

    logBusinessEvent('PHONE_VERIFIED', 'User', userId, userId);

    res.json({
      success: true,
      message: 'Phone verified successfully',
    });
  };

  // Get user profile
  public getProfile = async (req: Request, res: Response): Promise<void> => {
    const userId = req.user!.id;
    const prisma = getPrisma();

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        phone: true,
        arabicName: true,
        englishName: true,
        birthDate: true,
        gender: true,
        nationality: true,
        qualification: true,
        specialization: true,
        university: true,
        graduationYear: true,
        licenseNumber: true,
        profileImage: true,
        bio: true,
        status: true,
        emailVerified: true,
        phoneVerified: true,
        createdAt: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw new ApiError(404, 'User not found', 'USER_NOT_FOUND');
    }

    res.json({
      success: true,
      data: { user },
    });
  };

  // Check token validity
  public checkToken = async (req: Request, res: Response): Promise<void> => {
    res.json({
      success: true,
      message: 'Token is valid',
      data: {
        user: req.user,
      },
    });
  };

  // Social login placeholders
  public googleLogin = async (req: Request, res: Response): Promise<void> => {
    throw new ApiError(501, 'Google login not implemented yet', 'NOT_IMPLEMENTED');
  };

  public facebookLogin = async (req: Request, res: Response): Promise<void> => {
    throw new ApiError(501, 'Facebook login not implemented yet', 'NOT_IMPLEMENTED');
  };

  public appleLogin = async (req: Request, res: Response): Promise<void> => {
    throw new ApiError(501, 'Apple login not implemented yet', 'NOT_IMPLEMENTED');
  };
}
