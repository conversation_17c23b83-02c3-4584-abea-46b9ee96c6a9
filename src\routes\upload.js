const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Middleware for authentication (placeholder)
const authenticate = (req, res, next) => {
  // TODO: Implement JWT authentication middleware
  req.user = { id: 'user123', email: '<EMAIL>' }; // Mock user for now
  next();
};

// Create uploads directory if it doesn't exist
const createUploadDir = async (dir) => {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (error) {
    logger.error('Error creating upload directory:', error);
  }
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../../uploads', req.body.type || 'general');
    await createUploadDir(uploadPath);
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);
  
  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('نوع الملف غير مدعوم. الأنواع المسموحة: JPEG, JPG, PNG, PDF, DOC, DOCX'));
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB default
  },
  fileFilter: fileFilter
});

// Upload single file
router.post('/single', authenticate, upload.single('file'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'لم يتم رفع أي ملف'
    });
  }

  // TODO: Save file info to database
  const fileInfo = {
    id: 'file_' + Date.now(),
    originalName: req.file.originalname,
    filename: req.file.filename,
    path: req.file.path,
    size: req.file.size,
    mimetype: req.file.mimetype,
    uploadedBy: req.user.id,
    uploadedAt: new Date().toISOString()
  };

  logger.info('File uploaded', {
    userId: req.user.id,
    filename: req.file.filename,
    size: req.file.size,
    type: req.file.mimetype
  });

  res.json({
    success: true,
    message: 'تم رفع الملف بنجاح',
    data: {
      file: fileInfo,
      url: `/uploads/${req.body.type || 'general'}/${req.file.filename}`
    }
  });
}));

// Upload multiple files
router.post('/multiple', authenticate, upload.array('files', 5), asyncHandler(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'لم يتم رفع أي ملفات'
    });
  }

  // TODO: Save files info to database
  const filesInfo = req.files.map(file => ({
    id: 'file_' + Date.now() + '_' + Math.random(),
    originalName: file.originalname,
    filename: file.filename,
    path: file.path,
    size: file.size,
    mimetype: file.mimetype,
    uploadedBy: req.user.id,
    uploadedAt: new Date().toISOString(),
    url: `/uploads/${req.body.type || 'general'}/${file.filename}`
  }));

  logger.info('Multiple files uploaded', {
    userId: req.user.id,
    fileCount: req.files.length,
    totalSize: req.files.reduce((sum, file) => sum + file.size, 0)
  });

  res.json({
    success: true,
    message: `تم رفع ${req.files.length} ملف بنجاح`,
    data: {
      files: filesInfo
    }
  });
}));

// Upload profile image
router.post('/profile-image', authenticate, upload.single('profileImage'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'لم يتم رفع صورة الملف الشخصي'
    });
  }

  // Check if it's an image
  if (!req.file.mimetype.startsWith('image/')) {
    return res.status(400).json({
      success: false,
      message: 'يجب أن يكون الملف صورة'
    });
  }

  // TODO: Update user profile image in database
  const imageInfo = {
    filename: req.file.filename,
    url: `/uploads/profiles/${req.file.filename}`,
    size: req.file.size
  };

  logger.info('Profile image uploaded', {
    userId: req.user.id,
    filename: req.file.filename,
    size: req.file.size
  });

  res.json({
    success: true,
    message: 'تم رفع صورة الملف الشخصي بنجاح',
    data: {
      image: imageInfo
    }
  });
}));

// Upload document
router.post('/document', authenticate, upload.single('document'), asyncHandler(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'لم يتم رفع الوثيقة'
    });
  }

  const { documentType } = req.body;
  
  if (!documentType) {
    return res.status(400).json({
      success: false,
      message: 'نوع الوثيقة مطلوب'
    });
  }

  // TODO: Save document info to database
  const documentInfo = {
    id: 'doc_' + Date.now(),
    type: documentType,
    originalName: req.file.originalname,
    filename: req.file.filename,
    path: req.file.path,
    size: req.file.size,
    mimetype: req.file.mimetype,
    status: 'PENDING',
    uploadedBy: req.user.id,
    uploadedAt: new Date().toISOString()
  };

  logger.info('Document uploaded', {
    userId: req.user.id,
    documentType,
    filename: req.file.filename,
    size: req.file.size
  });

  res.json({
    success: true,
    message: 'تم رفع الوثيقة بنجاح',
    data: {
      document: documentInfo,
      url: `/uploads/documents/${req.file.filename}`
    }
  });
}));

// Get file info
router.get('/file/:filename', authenticate, asyncHandler(async (req, res) => {
  const { filename } = req.params;
  
  // TODO: Get file info from database
  // For now, return mock data
  res.json({
    success: true,
    data: {
      file: {
        filename,
        originalName: 'document.pdf',
        size: 1024000,
        mimetype: 'application/pdf',
        uploadedAt: new Date().toISOString(),
        status: 'ACTIVE'
      },
      message: 'File info endpoint - Coming soon'
    }
  });
}));

// Delete file
router.delete('/file/:filename', authenticate, asyncHandler(async (req, res) => {
  const { filename } = req.params;
  
  try {
    // TODO: Check if user owns the file
    // TODO: Remove file info from database
    
    // Find and delete the physical file
    const uploadDirs = ['general', 'profiles', 'documents'];
    let fileDeleted = false;
    
    for (const dir of uploadDirs) {
      const filePath = path.join(__dirname, '../../uploads', dir, filename);
      try {
        await fs.unlink(filePath);
        fileDeleted = true;
        break;
      } catch (error) {
        // File not found in this directory, continue
      }
    }
    
    if (!fileDeleted) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }

    logger.info('File deleted', {
      userId: req.user.id,
      filename
    });

    res.json({
      success: true,
      message: 'تم حذف الملف بنجاح'
    });
  } catch (error) {
    logger.error('Error deleting file:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في حذف الملف'
    });
  }
}));

// Error handling for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'عدد الملفات كبير جداً'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'حقل ملف غير متوقع'
      });
    }
  }
  
  if (error.message.includes('نوع الملف غير مدعوم')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }
  
  next(error);
});

module.exports = router;
