const qaService = require('../services/qaService');
const logger = require('../utils/logger');

class QAController {
  // Create Q&A session
  async createQASession(req, res) {
    try {
      const sessionData = req.body;
      const createdBy = req.user.id;

      const qaSession = await qaService.createQASession(sessionData, createdBy);

      res.status(201).json({
        success: true,
        message: 'Q&A session created successfully',
        data: qaSession
      });
    } catch (error) {
      logger.error('Error creating Q&A session:', error);
      res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to create Q&A session',
          code: 'CREATE_QA_SESSION_ERROR'
        }
      });
    }
  }

  // Get Q&A session by ID
  async getQASessionById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const qaSession = await qaService.getQASessionById(id, userId);

      res.json({
        success: true,
        data: qaSession
      });
    } catch (error) {
      logger.error('Error getting Q&A session by ID:', error);
      
      if (error.message === 'Q&A session not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Q&A session not found',
            code: 'QA_SESSION_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get Q&A session',
          code: 'GET_QA_SESSION_ERROR'
        }
      });
    }
  }

  // Submit question
  async submitQuestion(req, res) {
    try {
      const { id: qaSessionId } = req.params;
      const { content, isAnonymous = false } = req.body;
      const userId = req.user?.id;

      const question = await qaService.submitQuestion(qaSessionId, userId, content, isAnonymous);

      res.status(201).json({
        success: true,
        message: 'Question submitted successfully',
        data: question
      });
    } catch (error) {
      logger.error('Error submitting question:', error);
      
      const errorMessages = {
        'Q&A session not found': 'QA_SESSION_NOT_FOUND',
        'Q&A session is not active or live': 'QA_SESSION_NOT_ACTIVE',
        'Q&A session has ended': 'QA_SESSION_ENDED'
      };

      const errorCode = errorMessages[error.message] || 'SUBMIT_QUESTION_ERROR';
      const statusCode = errorCode === 'QA_SESSION_NOT_FOUND' ? 404 : 400;

      res.status(statusCode).json({
        success: false,
        error: {
          message: error.message || 'Failed to submit question',
          code: errorCode
        }
      });
    }
  }

  // Vote on question
  async voteOnQuestion(req, res) {
    try {
      const { id: questionId } = req.params;
      const { voteType } = req.body;
      const userId = req.user.id;

      if (!['UPVOTE', 'DOWNVOTE'].includes(voteType)) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid vote type. Must be UPVOTE or DOWNVOTE',
            code: 'INVALID_VOTE_TYPE'
          }
        });
      }

      const question = await qaService.voteOnQuestion(questionId, userId, voteType);

      res.json({
        success: true,
        message: 'Vote submitted successfully',
        data: question
      });
    } catch (error) {
      logger.error('Error voting on question:', error);
      
      if (error.message === 'Question not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Question not found',
            code: 'QUESTION_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to vote on question',
          code: 'VOTE_QUESTION_ERROR'
        }
      });
    }
  }

  // Answer question
  async answerQuestion(req, res) {
    try {
      const { id: questionId } = req.params;
      const { answer } = req.body;
      const userId = req.user.id;

      const answeredQuestion = await qaService.answerQuestion(questionId, userId, answer);

      res.json({
        success: true,
        message: 'Question answered successfully',
        data: answeredQuestion
      });
    } catch (error) {
      logger.error('Error answering question:', error);
      
      const errorMessages = {
        'Question not found': { code: 'QUESTION_NOT_FOUND', status: 404 },
        'Unauthorized to answer this question': { code: 'UNAUTHORIZED', status: 403 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'ANSWER_QUESTION_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to answer question',
          code: errorInfo.code
        }
      });
    }
  }

  // Moderate question
  async moderateQuestion(req, res) {
    try {
      const { id: questionId } = req.params;
      const { status } = req.body;
      const userId = req.user.id;

      if (!['APPROVED', 'REJECTED'].includes(status)) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid status. Must be APPROVED or REJECTED',
            code: 'INVALID_STATUS'
          }
        });
      }

      const question = await qaService.moderateQuestion(questionId, userId, status);

      res.json({
        success: true,
        message: `Question ${status.toLowerCase()} successfully`,
        data: question
      });
    } catch (error) {
      logger.error('Error moderating question:', error);
      
      const errorMessages = {
        'Question not found': { code: 'QUESTION_NOT_FOUND', status: 404 },
        'Unauthorized to moderate this question': { code: 'UNAUTHORIZED', status: 403 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'MODERATE_QUESTION_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to moderate question',
          code: errorInfo.code
        }
      });
    }
  }

  // Highlight question
  async highlightQuestion(req, res) {
    try {
      const { id: questionId } = req.params;
      const { isHighlighted = true } = req.body;
      const userId = req.user.id;

      const question = await qaService.highlightQuestion(questionId, userId, isHighlighted);

      res.json({
        success: true,
        message: `Question ${isHighlighted ? 'highlighted' : 'unhighlighted'} successfully`,
        data: question
      });
    } catch (error) {
      logger.error('Error highlighting question:', error);
      
      const errorMessages = {
        'Question not found': { code: 'QUESTION_NOT_FOUND', status: 404 },
        'Unauthorized to highlight this question': { code: 'UNAUTHORIZED', status: 403 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'HIGHLIGHT_QUESTION_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to highlight question',
          code: errorInfo.code
        }
      });
    }
  }

  // Get pending questions
  async getPendingQuestions(req, res) {
    try {
      const { id: qaSessionId } = req.params;
      const userId = req.user.id;

      const questions = await qaService.getPendingQuestions(qaSessionId, userId);

      res.json({
        success: true,
        data: questions
      });
    } catch (error) {
      logger.error('Error getting pending questions:', error);
      
      const errorMessages = {
        'Q&A session not found': { code: 'QA_SESSION_NOT_FOUND', status: 404 },
        'Unauthorized to view pending questions': { code: 'UNAUTHORIZED', status: 403 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'GET_PENDING_QUESTIONS_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to get pending questions',
          code: errorInfo.code
        }
      });
    }
  }

  // Toggle Q&A session live status
  async toggleQASessionLive(req, res) {
    try {
      const { id: sessionId } = req.params;
      const { isLive } = req.body;
      const userId = req.user.id;

      const qaSession = await qaService.toggleQASessionLive(sessionId, isLive, userId);

      res.json({
        success: true,
        message: `Q&A session ${isLive ? 'started' : 'stopped'} successfully`,
        data: qaSession
      });
    } catch (error) {
      logger.error('Error toggling Q&A session live status:', error);
      
      const errorMessages = {
        'Q&A session not found': { code: 'QA_SESSION_NOT_FOUND', status: 404 },
        'Unauthorized to modify this Q&A session': { code: 'UNAUTHORIZED', status: 403 }
      };

      const errorInfo = errorMessages[error.message] || { code: 'TOGGLE_QA_SESSION_ERROR', status: 500 };

      res.status(errorInfo.status).json({
        success: false,
        error: {
          message: error.message || 'Failed to toggle Q&A session status',
          code: errorInfo.code
        }
      });
    }
  }

  // Get Q&A statistics
  async getQAStats(req, res) {
    try {
      const stats = await qaService.getQAStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting Q&A stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get Q&A statistics',
          code: 'GET_QA_STATS_ERROR'
        }
      });
    }
  }

  // Get live Q&A sessions for a session
  async getLiveQASessions(req, res) {
    try {
      const { sessionId } = req.params;

      // This would be implemented similar to live polls
      // For now, return a placeholder
      res.json({
        success: true,
        data: [],
        message: 'Live Q&A sessions functionality to be implemented'
      });
    } catch (error) {
      logger.error('Error getting live Q&A sessions:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get live Q&A sessions',
          code: 'GET_LIVE_QA_SESSIONS_ERROR'
        }
      });
    }
  }
}

module.exports = new QAController();
