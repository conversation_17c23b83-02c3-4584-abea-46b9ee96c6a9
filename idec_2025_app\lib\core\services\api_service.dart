import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final String baseUrl = AppConstants.baseUrl;
  String? _authToken;

  // Headers
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (_authToken != null) 'Authorization': 'Bearer $_authToken',
  };

  // Initialize service
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(AppConstants.tokenKey);
  }

  // Set auth token
  Future<void> setAuthToken(String token) async {
    _authToken = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, token);
  }

  // Clear auth token
  Future<void> clearAuthToken() async {
    _authToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
  }

  // Generic GET request
  Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic POST request
  Future<Map<String, dynamic>> post(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: jsonEncode(data),
      );

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic PUT request
  Future<Map<String, dynamic>> put(String endpoint, Map<String, dynamic> data) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
        body: jsonEncode(data),
      );

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Generic DELETE request
  Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: _headers,
      );

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    final Map<String, dynamic> data = jsonDecode(response.body);

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      final error = data['error'] ?? {};
      final message = error['message'] ?? 'Unknown error occurred';
      throw ApiException(message, statusCode: response.statusCode);
    }
  }

  // Authentication endpoints
  Future<Map<String, dynamic>> login(String email, String password) async {
    return await post('/auth/login', {
      'email': email,
      'password': password,
    });
  }

  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    return await post('/auth/register', userData);
  }

  Future<Map<String, dynamic>> refreshToken() async {
    return await post('/auth/refresh', {});
  }

  Future<void> logout() async {
    await post('/auth/logout', {});
    await clearAuthToken();
  }

  // User endpoints
  Future<Map<String, dynamic>> getUserProfile() async {
    return await get('/users/profile');
  }

  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> data) async {
    return await put('/users/profile', data);
  }

  // Sessions endpoints
  Future<Map<String, dynamic>> getSessions() async {
    return await get('/sessions');
  }

  Future<Map<String, dynamic>> getSessionById(String id) async {
    return await get('/sessions/$id');
  }

  Future<Map<String, dynamic>> registerForSession(String sessionId) async {
    return await post('/sessions/$sessionId/register', {});
  }

  Future<Map<String, dynamic>> checkInSession(String sessionId, String qrCode) async {
    return await post('/sessions/$sessionId/checkin', {'qrCode': qrCode});
  }

  // Speakers endpoints
  Future<Map<String, dynamic>> getSpeakers() async {
    return await get('/speakers');
  }

  Future<Map<String, dynamic>> getSpeakerById(String id) async {
    return await get('/speakers/$id');
  }

  // Exhibition endpoints
  Future<Map<String, dynamic>> getExhibitors() async {
    return await get('/exhibition/exhibitors');
  }

  Future<Map<String, dynamic>> getExhibitorById(String id) async {
    return await get('/exhibition/exhibitors/$id');
  }

  Future<Map<String, dynamic>> visitBooth(String boothId) async {
    return await post('/exhibition/booths/$boothId/visit', {});
  }

  // Library endpoints
  Future<Map<String, dynamic>> getLibraryItems() async {
    return await get('/library/items');
  }

  Future<Map<String, dynamic>> getLibraryCategories() async {
    return await get('/library/categories');
  }

  Future<Map<String, dynamic>> rateLibraryItem(String itemId, int rating) async {
    return await post('/library/items/$itemId/rate', {'rating': rating});
  }

  // Gamification endpoints
  Future<Map<String, dynamic>> getUserPoints() async {
    return await get('/gamification/points');
  }

  Future<Map<String, dynamic>> getUserBadges() async {
    return await get('/gamification/badges');
  }

  Future<Map<String, dynamic>> getLeaderboard() async {
    return await get('/gamification/leaderboard');
  }

  // Social endpoints
  Future<Map<String, dynamic>> getConnections() async {
    return await get('/social/connections');
  }

  Future<Map<String, dynamic>> sendConnectionRequest(String userId) async {
    return await post('/social/connections/request', {'userId': userId});
  }

  Future<Map<String, dynamic>> acceptConnectionRequest(String requestId) async {
    return await put('/social/connections/$requestId/accept', {});
  }

  // Polling endpoints
  Future<Map<String, dynamic>> getLivePolls(String sessionId) async {
    return await get('/polling/sessions/$sessionId/polls/live');
  }

  Future<Map<String, dynamic>> submitPollResponse(String pollId, Map<String, dynamic> response) async {
    return await post('/polling/polls/$pollId/respond', response);
  }

  Future<Map<String, dynamic>> getLiveQASessions(String sessionId) async {
    return await get('/polling/sessions/$sessionId/qa-sessions/live');
  }

  Future<Map<String, dynamic>> submitQuestion(String qaSessionId, String content, {bool isAnonymous = false}) async {
    return await post('/polling/qa-sessions/$qaSessionId/questions', {
      'content': content,
      'isAnonymous': isAnonymous,
    });
  }

  Future<Map<String, dynamic>> voteOnQuestion(String questionId, String voteType) async {
    return await post('/polling/questions/$questionId/vote', {'voteType': voteType});
  }

  // Notifications endpoints
  Future<Map<String, dynamic>> getNotifications() async {
    return await get('/notifications');
  }

  Future<Map<String, dynamic>> markNotificationAsRead(String notificationId) async {
    return await put('/notifications/$notificationId/read', {});
  }

  // News endpoints
  Future<Map<String, dynamic>> getNews() async {
    return await get('/news');
  }

  Future<Map<String, dynamic>> getNewsById(String id) async {
    return await get('/news/$id');
  }

  // Courses endpoints
  Future<Map<String, dynamic>> getCourses() async {
    return await get('/courses');
  }

  Future<Map<String, dynamic>> getCourseById(String id) async {
    return await get('/courses/$id');
  }

  Future<Map<String, dynamic>> registerForCourse(String courseId) async {
    return await post('/courses/$courseId/register', {});
  }
}

// Custom exception class
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, {this.statusCode});

  @override
  String toString() => 'ApiException: $message';
}

// Response wrapper class
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final String? error;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.error,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJson) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      data: json['data'] != null ? fromJson(json['data']) : null,
      message: json['message'],
      error: json['error']?['message'],
    );
  }
}

// Network connectivity checker
class NetworkChecker {
  static Future<bool> isConnected() async {
    try {
      final response = await http.get(Uri.parse('https://www.google.com'));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
