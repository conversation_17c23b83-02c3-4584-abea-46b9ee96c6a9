const libraryService = require('../services/libraryService');
const logger = require('../utils/logger');

class LibraryController {
  // Get all categories
  async getCategories(req, res) {
    try {
      const { includeInactive = false } = req.query;

      const categories = await libraryService.getCategories(includeInactive === 'true');

      res.json({
        success: true,
        data: categories
      });
    } catch (error) {
      logger.error('Error getting categories:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get categories',
          code: 'GET_CATEGORIES_ERROR'
        }
      });
    }
  }

  // Get library items
  async getLibraryItems(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        categoryId,
        type,
        search,
        tags,
        author,
        isFeatured,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const filters = {
        categoryId,
        type,
        search,
        tags: tags ? tags.split(',') : undefined,
        author,
        isFeatured: isFeatured === 'true' ? true : undefined,
        sortBy,
        sortOrder
      };

      const pagination = { page: parseInt(page), limit: parseInt(limit) };

      const result = await libraryService.getLibraryItems(filters, pagination);

      res.json({
        success: true,
        data: result.items,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error getting library items:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get library items',
          code: 'GET_LIBRARY_ITEMS_ERROR'
        }
      });
    }
  }

  // Get library item by ID
  async getLibraryItemById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const item = await libraryService.getLibraryItemById(id, userId);

      // Track view
      await libraryService.trackView(
        id,
        userId,
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        data: item
      });
    } catch (error) {
      logger.error('Error getting library item by ID:', error);
      
      if (error.message === 'Library item not found') {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Library item not found',
            code: 'LIBRARY_ITEM_NOT_FOUND'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get library item',
          code: 'GET_LIBRARY_ITEM_ERROR'
        }
      });
    }
  }

  // Rate library item
  async rateLibraryItem(req, res) {
    try {
      const { id } = req.params;
      const { rating, comment } = req.body;
      const userId = req.user.id;

      const libraryRating = await libraryService.rateLibraryItem(id, userId, rating, comment);

      res.json({
        success: true,
        message: 'Rating submitted successfully',
        data: libraryRating
      });
    } catch (error) {
      logger.error('Error rating library item:', error);
      
      if (error.message === 'Rating must be between 1 and 5') {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Rating must be between 1 and 5',
            code: 'INVALID_RATING'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to rate library item',
          code: 'RATE_LIBRARY_ITEM_ERROR'
        }
      });
    }
  }

  // Bookmark library item
  async bookmarkLibraryItem(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const bookmark = await libraryService.bookmarkLibraryItem(id, userId);

      res.json({
        success: true,
        message: 'Item bookmarked successfully',
        data: bookmark
      });
    } catch (error) {
      logger.error('Error bookmarking library item:', error);
      
      if (error.message === 'Item already bookmarked') {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Item already bookmarked',
            code: 'ALREADY_BOOKMARKED'
          }
        });
      }

      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to bookmark item',
          code: 'BOOKMARK_ERROR'
        }
      });
    }
  }

  // Remove bookmark
  async removeBookmark(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      await libraryService.removeBookmark(id, userId);

      res.json({
        success: true,
        message: 'Bookmark removed successfully'
      });
    } catch (error) {
      logger.error('Error removing bookmark:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to remove bookmark',
          code: 'REMOVE_BOOKMARK_ERROR'
        }
      });
    }
  }

  // Get user's bookmarks
  async getUserBookmarks(req, res) {
    try {
      const { page = 1, limit = 20 } = req.query;
      const userId = req.user.id;

      const pagination = { page: parseInt(page), limit: parseInt(limit) };

      const result = await libraryService.getUserBookmarks(userId, pagination);

      res.json({
        success: true,
        data: result.bookmarks,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error getting user bookmarks:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get bookmarks',
          code: 'GET_BOOKMARKS_ERROR'
        }
      });
    }
  }

  // Add note to library item
  async addNote(req, res) {
    try {
      const { id } = req.params;
      const { content, isPrivate = true } = req.body;
      const userId = req.user.id;

      const note = await libraryService.addNote(id, userId, content, isPrivate);

      res.status(201).json({
        success: true,
        message: 'Note added successfully',
        data: note
      });
    } catch (error) {
      logger.error('Error adding note:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to add note',
          code: 'ADD_NOTE_ERROR'
        }
      });
    }
  }

  // Get user's notes for an item
  async getUserNotes(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const notes = await libraryService.getUserNotes(id, userId);

      res.json({
        success: true,
        data: notes
      });
    } catch (error) {
      logger.error('Error getting user notes:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get notes',
          code: 'GET_NOTES_ERROR'
        }
      });
    }
  }

  // Search library
  async searchLibrary(req, res) {
    try {
      const { q: query, type, categoryId, tags } = req.query;

      if (!query || query.length < 2) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Search query must be at least 2 characters',
            code: 'INVALID_SEARCH_QUERY'
          }
        });
      }

      const filters = {
        type,
        categoryId,
        tags: tags ? tags.split(',') : undefined
      };

      const items = await libraryService.searchLibrary(query, filters);

      res.json({
        success: true,
        data: items
      });
    } catch (error) {
      logger.error('Error searching library:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to search library',
          code: 'SEARCH_LIBRARY_ERROR'
        }
      });
    }
  }

  // Get library statistics
  async getLibraryStats(req, res) {
    try {
      const stats = await libraryService.getLibraryStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting library stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get library statistics',
          code: 'GET_LIBRARY_STATS_ERROR'
        }
      });
    }
  }

  // Get featured content
  async getFeaturedContent(req, res) {
    try {
      const result = await libraryService.getLibraryItems(
        { isFeatured: true },
        { limit: 10 }
      );

      res.json({
        success: true,
        data: result.items
      });
    } catch (error) {
      logger.error('Error getting featured content:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get featured content',
          code: 'GET_FEATURED_CONTENT_ERROR'
        }
      });
    }
  }

  // Get popular content
  async getPopularContent(req, res) {
    try {
      const result = await libraryService.getLibraryItems(
        { sortBy: 'viewCount', sortOrder: 'desc' },
        { limit: 10 }
      );

      res.json({
        success: true,
        data: result.items
      });
    } catch (error) {
      logger.error('Error getting popular content:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get popular content',
          code: 'GET_POPULAR_CONTENT_ERROR'
        }
      });
    }
  }
}

module.exports = new LibraryController();
