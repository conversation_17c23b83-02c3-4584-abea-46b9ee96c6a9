import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class ExhibitorsScreen extends StatefulWidget {
  const ExhibitorsScreen({super.key});

  @override
  State<ExhibitorsScreen> createState() => _ExhibitorsScreenState();
}

class _ExhibitorsScreenState extends State<ExhibitorsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  String _selectedCategory = 'الكل';
  String _selectedHall = 'الكل';
  String _searchQuery = '';
  
  final List<String> _categories = [
    'الكل', 'أجهزة طبية', 'مواد سنية', 'تقنيات حديثة', 'برمجيات', 'تعليم', 'خدمات'
  ];
  final List<String> _halls = ['الكل', 'القاعة A', 'القاعة B', 'القاعة C', 'القاعة الخارجية'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 340,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'العارضون والشركات',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _scaleAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: 1.0 + (0.1 * _scaleAnimation.value),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/exhibitors_bg.png'),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 60),
                                
                                // Exhibitors Icon
                                Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.business,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                                
                                const SizedBox(height: AppSizes.paddingM),
                                
                                // Stats
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    _buildStatItem('75+', 'شركة عارضة'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('300+', 'منتج وخدمة'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('4', 'قاعات عرض'),
                                  ],
                                ),
                                
                                const SizedBox(height: AppSizes.paddingM),
                                
                                // Search Bar
                                Container(
                                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                  ),
                                  child: TextField(
                                    onChanged: (value) {
                                      setState(() {
                                        _searchQuery = value;
                                      });
                                    },
                                    style: const TextStyle(
                                      color: Color(AppColors.primaryWhite),
                                    ),
                                    decoration: InputDecoration(
                                      hintText: 'ابحث عن شركة أو منتج...',
                                      hintStyle: TextStyle(
                                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                                      ),
                                      prefixIcon: const Icon(
                                        Icons.search,
                                        color: Color(AppColors.primaryWhite),
                                      ),
                                      border: InputBorder.none,
                                      contentPadding: const EdgeInsets.all(AppSizes.paddingM),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                tabs: const [
                  Tab(text: 'الشركات'),
                  Tab(text: 'المنتجات'),
                  Tab(text: 'العروض الخاصة'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Filters
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Row(
                children: [
                  Expanded(
                    child: _buildFilterDropdown(
                      'الفئة',
                      _selectedCategory,
                      _categories,
                      (value) => setState(() => _selectedCategory = value!),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingS),
                  Expanded(
                    child: _buildFilterDropdown(
                      'القاعة',
                      _selectedHall,
                      _halls,
                      (value) => setState(() => _selectedHall = value!),
                    ),
                  ),
                  IconButton(
                    onPressed: _showExhibitionMap,
                    icon: const Icon(
                      Icons.map,
                      color: Color(AppColors.primaryRed),
                    ),
                    tooltip: 'خريطة المعرض',
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildCompaniesTab(),
                    _buildProductsTab(),
                    _buildSpecialOffersTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showFavoriteExhibitors,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.favorite),
        label: const Text('المفضلة'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Text(label),
          isExpanded: true,
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildCompaniesTab() {
    final companies = _getCompanies();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: companies.length,
      itemBuilder: (context, index) {
        final company = companies[index];
        return _buildCompanyCard(company, index);
      },
    );
  }

  Widget _buildProductsTab() {
    final products = _getProducts();
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 0.8,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return _buildProductCard(product);
      },
    );
  }

  Widget _buildSpecialOffersTab() {
    final offers = _getSpecialOffers();

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: offers.length,
      itemBuilder: (context, index) {
        final offer = offers[index];
        return _buildOfferCard(offer);
      },
    );
  }

  Widget _buildCompanyCard(Map<String, dynamic> company, int index) {
    final isFavorite = company['isFavorite'] ?? false;
    final isSponsored = company['isSponsored'] ?? false;
    final category = company['category'] ?? 'عام';

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: InkWell(
          onTap: () => _showCompanyDetails(company),
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
              gradient: LinearGradient(
                colors: [
                  const Color(AppColors.primaryWhite),
                  const Color(AppColors.offWhite),
                ],
              ),
              border: Border.all(
                color: isSponsored
                    ? const Color(AppColors.warning)
                    : const Color(AppColors.lightGrey),
                width: isSponsored ? 2 : 1,
              ),
            ),
            child: Column(
              children: [
                // Company Header
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isSponsored
                          ? [const Color(AppColors.warning), const Color(AppColors.warning).withValues(alpha: 0.8)]
                          : [const Color(AppColors.primaryRed), const Color(AppColors.darkRed)],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppSizes.radiusL),
                      topRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Company Logo
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryWhite),
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(AppColors.primaryBlack).withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.business,
                          size: 30,
                          color: Color(AppColors.primaryRed),
                        ),
                      ),

                      const SizedBox(width: AppSizes.paddingM),

                      // Company Info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              company['name'],
                              style: const TextStyle(
                                color: Color(AppColors.primaryWhite),
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: AppSizes.paddingXS),
                            Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  color: Color(AppColors.primaryWhite),
                                  size: AppSizes.iconS,
                                ),
                                const SizedBox(width: AppSizes.paddingXS),
                                Text(
                                  'جناح ${company['boothNumber']} - ${company['hall']}',
                                  style: TextStyle(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Badges and Actions
                      Column(
                        children: [
                          if (isSponsored)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingS,
                                vertical: AppSizes.paddingXS,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.primaryWhite),
                                borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              ),
                              child: const Text(
                                'راعي',
                                style: TextStyle(
                                  color: Color(AppColors.warning),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),

                          const SizedBox(height: AppSizes.paddingS),

                          IconButton(
                            onPressed: () => _toggleFavoriteCompany(company),
                            icon: Icon(
                              isFavorite ? Icons.favorite : Icons.favorite_border,
                              color: const Color(AppColors.primaryWhite),
                              size: AppSizes.iconS,
                            ),
                            style: IconButton.styleFrom(
                              backgroundColor: const Color(AppColors.primaryBlack).withValues(alpha: 0.3),
                              minimumSize: const Size(30, 30),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Company Content
                Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Category and Country
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: _getCategoryColor(category).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: Text(
                              category,
                              style: TextStyle(
                                color: _getCategoryColor(category),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.paddingS),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.info).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.flag,
                                  size: 12,
                                  color: Color(AppColors.info),
                                ),
                                const SizedBox(width: AppSizes.paddingXS),
                                Text(
                                  company['country'],
                                  style: const TextStyle(
                                    color: Color(AppColors.info),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppSizes.paddingS),

                      // Description
                      Text(
                        company['description'],
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(AppColors.grey),
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppSizes.paddingM),

                      // Products Count and Rating
                      Row(
                        children: [
                          Icon(
                            Icons.inventory,
                            size: AppSizes.iconS,
                            color: const Color(AppColors.primaryRed),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            '${company['productsCount']} منتج',
                            style: const TextStyle(
                              color: Color(AppColors.primaryBlack),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(width: AppSizes.paddingM),
                          Icon(
                            Icons.star,
                            size: AppSizes.iconS,
                            color: const Color(AppColors.warning),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            '${company['rating']}',
                            style: const TextStyle(
                              color: Color(AppColors.primaryBlack),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (company['hasSpecialOffer'] == true)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingS,
                                vertical: AppSizes.paddingXS,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(AppColors.success),
                                borderRadius: BorderRadius.circular(AppSizes.radiusS),
                              ),
                              child: const Text(
                                'عرض خاص',
                                style: TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),

                      const SizedBox(height: AppSizes.paddingM),

                      // Action Buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () => _visitBooth(company),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(AppColors.primaryRed),
                                foregroundColor: const Color(AppColors.primaryWhite),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                                ),
                              ),
                              icon: const Icon(Icons.store, size: AppSizes.iconS),
                              label: const Text('زيارة الجناح', style: TextStyle(fontSize: 12)),
                            ),
                          ),

                          const SizedBox(width: AppSizes.paddingS),

                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _contactCompany(company),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: const Color(AppColors.primaryRed),
                                side: const BorderSide(color: Color(AppColors.primaryRed)),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(AppSizes.radiusM),
                                ),
                              ),
                              icon: const Icon(Icons.contact_phone, size: AppSizes.iconS),
                              label: const Text('تواصل', style: TextStyle(fontSize: 12)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    final isFavorite = product['isFavorite'] ?? false;

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _showProductDetails(product),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
          ),
          child: Column(
            children: [
              // Product Image
              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(AppColors.info).withValues(alpha: 0.8),
                        const Color(AppColors.info).withValues(alpha: 0.6),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppSizes.radiusL),
                      topRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Stack(
                    children: [
                      const Center(
                        child: Icon(
                          Icons.medical_services,
                          size: 40,
                          color: Color(AppColors.primaryWhite),
                        ),
                      ),

                      // Favorite Button
                      Positioned(
                        top: AppSizes.paddingS,
                        right: AppSizes.paddingS,
                        child: IconButton(
                          onPressed: () => _toggleFavoriteProduct(product),
                          icon: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: const Color(AppColors.primaryWhite),
                            size: AppSizes.iconS,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: const Color(AppColors.primaryBlack).withValues(alpha: 0.5),
                            minimumSize: const Size(25, 25),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Product Info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Name
                      Text(
                        product['name'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Color(AppColors.primaryBlack),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: AppSizes.paddingXS),

                      // Company
                      Text(
                        product['company'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(AppColors.grey),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const Spacer(),

                      // Price and Rating
                      Row(
                        children: [
                          if (product['price'] != null)
                            Text(
                              '${product['price']} ريال',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Color(AppColors.primaryRed),
                              ),
                            ),
                          const Spacer(),
                          Icon(
                            Icons.star,
                            size: 12,
                            color: const Color(AppColors.warning),
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            '${product['rating']}',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Color(AppColors.grey),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfferCard(Map<String, dynamic> offer) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.success).withValues(alpha: 0.1),
                const Color(AppColors.primaryWhite),
              ],
            ),
            border: Border.all(
              color: const Color(AppColors.success),
              width: 2,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Offer Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppSizes.paddingS),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(AppColors.success),
                            const Color(AppColors.success).withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      child: const Icon(
                        Icons.local_offer,
                        size: 24,
                        color: Color(AppColors.primaryWhite),
                      ),
                    ),

                    const SizedBox(width: AppSizes.paddingM),

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            offer['title'],
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(AppColors.primaryBlack),
                            ),
                          ),
                          Text(
                            offer['company'],
                            style: const TextStyle(
                              color: Color(AppColors.success),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: AppSizes.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryRed),
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                      child: Text(
                        '${offer['discount']}%',
                        style: const TextStyle(
                          color: Color(AppColors.primaryWhite),
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppSizes.paddingM),

                // Description
                Text(
                  offer['description'],
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(AppColors.grey),
                    height: 1.4,
                  ),
                ),

                const SizedBox(height: AppSizes.paddingM),

                // Offer Details
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: AppSizes.iconS,
                      color: const Color(AppColors.warning),
                    ),
                    const SizedBox(width: AppSizes.paddingXS),
                    Text(
                      'ينتهي في: ${offer['expiryDate']}',
                      style: const TextStyle(
                        color: Color(AppColors.warning),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton(
                      onPressed: () => _claimOffer(offer),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(AppColors.success),
                        foregroundColor: const Color(AppColors.primaryWhite),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppSizes.radiusM),
                        ),
                      ),
                      child: const Text('احصل على العرض'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'أجهزة طبية':
        return const Color(AppColors.info);
      case 'مواد سنية':
        return const Color(AppColors.success);
      case 'تقنيات حديثة':
        return const Color(AppColors.primaryRed);
      case 'برمجيات':
        return const Color(AppColors.warning);
      case 'تعليم':
        return const Color(AppColors.primaryBlack);
      default:
        return const Color(AppColors.grey);
    }
  }

  void _showExhibitionMap() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خريطة المعرض'),
        content: Container(
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            color: const Color(AppColors.lightGrey),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.map,
                  size: 60,
                  color: Color(AppColors.primaryRed),
                ),
                SizedBox(height: AppSizes.paddingM),
                Text(
                  'خريطة تفاعلية\nقريباً...',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showFavoriteExhibitors() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Text(
                'الشركات المفضلة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Favorite companies list would go here
            const Expanded(
              child: Center(
                child: Text('لا توجد شركات مفضلة بعد'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCompanyDetails(Map<String, dynamic> company) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Company Header
                    Row(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(AppColors.primaryRed),
                                const Color(AppColors.darkRed),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: const Icon(
                            Icons.business,
                            size: 40,
                            color: Color(AppColors.primaryWhite),
                          ),
                        ),

                        const SizedBox(width: AppSizes.paddingM),

                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                company['name'],
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                company['category'],
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: const Color(AppColors.grey),
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(top: AppSizes.paddingXS),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.paddingS,
                                  vertical: AppSizes.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusS),
                                ),
                                child: Text(
                                  'جناح ${company['boothNumber']} - ${company['hall']}',
                                  style: const TextStyle(
                                    color: Color(AppColors.primaryRed),
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingL),

                    // Description
                    Text(
                      'نبذة عن الشركة',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      company['fullDescription'] ?? company['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavoriteCompany(Map<String, dynamic> company) {
    setState(() {
      company['isFavorite'] = !(company['isFavorite'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          company['isFavorite']
              ? 'تم إضافة الشركة للمفضلة'
              : 'تم إزالة الشركة من المفضلة',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _visitBooth(Map<String, dynamic> company) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('توجه إلى جناح ${company['boothNumber']} - ${company['hall']}'),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _contactCompany(Map<String, dynamic> company) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تواصل مع ${company['name']}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: AppSizes.paddingL),

            ListTile(
              leading: const Icon(Icons.phone),
              title: const Text('اتصال هاتفي'),
              subtitle: const Text('+966 50 123 4567'),
              onTap: () => Navigator.pop(context),
            ),

            ListTile(
              leading: const Icon(Icons.email),
              title: const Text('بريد إلكتروني'),
              subtitle: const Text('<EMAIL>'),
              onTap: () => Navigator.pop(context),
            ),

            ListTile(
              leading: const Icon(Icons.language),
              title: const Text('موقع الشركة'),
              subtitle: const Text('www.company.com'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _showProductDetails(Map<String, dynamic> product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product['name'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      'الشركة: ${product['company']}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: const Color(AppColors.grey),
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      product['description'] ?? 'وصف المنتج...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavoriteProduct(Map<String, dynamic> product) {
    setState(() {
      product['isFavorite'] = !(product['isFavorite'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          product['isFavorite']
              ? 'تم إضافة المنتج للمفضلة'
              : 'تم إزالة المنتج من المفضلة',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _claimOffer(Map<String, dynamic> offer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد العرض'),
        content: Text('هل تريد الحصول على عرض: ${offer['title']}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم الحصول على العرض: ${offer['title']}'),
                  backgroundColor: const Color(AppColors.success),
                ),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getCompanies() {
    return [
      {
        'name': 'شركة الأجهزة الطبية المتقدمة',
        'category': 'أجهزة طبية',
        'description': 'أحدث الأجهزة الطبية لعيادات الأسنان مع ضمان عالمي',
        'fullDescription': 'شركة رائدة في مجال الأجهزة الطبية المتخصصة في طب الأسنان، تقدم أحدث التقنيات والحلول المبتكرة مع خدمة ما بعد البيع المتميزة.',
        'boothNumber': 'A-01',
        'hall': 'القاعة A',
        'country': 'ألمانيا',
        'productsCount': 25,
        'rating': 4.8,
        'isSponsored': true,
        'isFavorite': false,
        'hasSpecialOffer': true,
      },
      {
        'name': 'مؤسسة المواد السنية الحديثة',
        'category': 'مواد سنية',
        'description': 'مواد سنية عالية الجودة ومعتمدة دولياً من أفضل المصانع',
        'fullDescription': 'متخصصون في توفير أفضل المواد السنية المستوردة من أوروبا وأمريكا مع شهادات الجودة العالمية.',
        'boothNumber': 'A-02',
        'hall': 'القاعة A',
        'country': 'الولايات المتحدة',
        'productsCount': 40,
        'rating': 4.6,
        'isSponsored': false,
        'isFavorite': true,
        'hasSpecialOffer': false,
      },
      {
        'name': 'تقنيات الليزر للأسنان',
        'category': 'تقنيات حديثة',
        'description': 'أجهزة الليزر المتطورة لعلاج الأسنان واللثة',
        'fullDescription': 'نقدم أحدث تقنيات الليزر في علاج الأسنان واللثة مع التدريب المتخصص والدعم الفني.',
        'boothNumber': 'B-01',
        'hall': 'القاعة B',
        'country': 'إيطاليا',
        'productsCount': 15,
        'rating': 4.9,
        'isSponsored': true,
        'isFavorite': false,
        'hasSpecialOffer': true,
      },
    ];
  }

  List<Map<String, dynamic>> _getProducts() {
    return [
      {
        'name': 'جهاز الأشعة الرقمي المتطور',
        'company': 'شركة الأجهزة الطبية المتقدمة',
        'price': 45000,
        'rating': 4.8,
        'isFavorite': false,
        'description': 'جهاز أشعة رقمي عالي الدقة مع تقنية الذكاء الاصطناعي',
      },
      {
        'name': 'مواد الحشو التجميلي',
        'company': 'مؤسسة المواد السنية الحديثة',
        'price': 250,
        'rating': 4.6,
        'isFavorite': true,
        'description': 'مواد حشو تجميلي عالية الجودة مع ألوان متنوعة',
      },
      {
        'name': 'جهاز الليزر العلاجي',
        'company': 'تقنيات الليزر للأسنان',
        'price': 85000,
        'rating': 4.9,
        'isFavorite': false,
        'description': 'جهاز ليزر متعدد الاستخدامات لعلاج الأسنان واللثة',
      },
      {
        'name': 'نظام إدارة العيادات الذكي',
        'company': 'برمجيات إدارة العيادات',
        'price': null,
        'rating': 4.5,
        'isFavorite': false,
        'description': 'نظام متكامل لإدارة المواعيد والملفات الطبية',
      },
    ];
  }

  List<Map<String, dynamic>> _getSpecialOffers() {
    return [
      {
        'title': 'خصم خاص على أجهزة الأشعة',
        'company': 'شركة الأجهزة الطبية المتقدمة',
        'description': 'خصم 25% على جميع أجهزة الأشعة الرقمية مع ضمان 3 سنوات وتدريب مجاني',
        'discount': 25,
        'expiryDate': '31 مارس 2025',
      },
      {
        'title': 'عرض المواد السنية الشامل',
        'company': 'مؤسسة المواد السنية الحديثة',
        'description': 'اشتري 10 عبوات واحصل على 3 مجاناً من جميع مواد الحشو التجميلي',
        'discount': 30,
        'expiryDate': '20 مارس 2025',
      },
      {
        'title': 'باقة الليزر الكاملة',
        'company': 'تقنيات الليزر للأسنان',
        'description': 'جهاز ليزر + تدريب متقدم + صيانة لمدة سنة بسعر مخفض',
        'discount': 20,
        'expiryDate': '15 مارس 2025',
      },
    ];
  }
}
