{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"vite": "^7.0.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "react-hook-form": "^7.59.0", "react-router-dom": "^7.6.3", "yup": "^1.6.1"}}