const { PrismaClient } = require('@prisma/client');
const { NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { createPaginationResult } = require('../middleware/validation');
const gamificationService = require('./gamificationService');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
} catch (error) {
  logger.error('Failed to initialize Prisma client:', error);
  prisma = null;
}

class ConferenceService {
  // Get all sessions with filters and pagination
  async getSessions(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 10 } = pagination;
      const offset = (page - 1) * limit;

      if (prisma) {
        const where = {};
        
        if (filters.type) {
          where.type = filters.type;
        }
        
        if (filters.date) {
          const startOfDay = new Date(filters.date);
          const endOfDay = new Date(filters.date);
          endOfDay.setHours(23, 59, 59, 999);
          
          where.startTime = {
            gte: startOfDay,
            lte: endOfDay
          };
        }

        if (filters.speakerId) {
          where.speakerId = filters.speakerId;
        }

        const [sessions, total] = await Promise.all([
          prisma.session.findMany({
            where,
            skip: offset,
            take: limit,
            include: {
              speaker: true,
              attendances: {
                select: {
                  id: true
                }
              }
            },
            orderBy: { startTime: 'asc' }
          }),
          prisma.session.count({ where })
        ]);

        // Add attendance count to each session
        const sessionsWithCount = sessions.map(session => ({
          ...session,
          attendeeCount: session.attendances.length,
          attendances: undefined // Remove detailed attendances for privacy
        }));

        return {
          sessions: sessionsWithCount,
          pagination: createPaginationResult(page, limit, total)
        };
      } else {
        // Mock data
        const mockSessions = [
          {
            id: 'session1',
            titleAr: 'مستقبل زراعة الأسنان',
            titleEn: 'The Future of Dental Implants',
            descriptionAr: 'محاضرة شاملة حول أحدث التقنيات في زراعة الأسنان',
            descriptionEn: 'Comprehensive lecture on the latest techniques in dental implants',
            startTime: new Date('2025-03-15T09:00:00Z'),
            endTime: new Date('2025-03-15T10:30:00Z'),
            type: 'LECTURE',
            cmeHours: 1.5,
            maxAttendees: 500,
            attendeeCount: 245,
            speaker: {
              id: 'speaker1',
              nameAr: 'د. أحمد محمد السعيد',
              nameEn: 'Dr. Ahmed Mohammed Al-Saeed',
              bio: 'استشاري طب الأسنان مع خبرة 15 عام في زراعة الأسنان'
            }
          },
          {
            id: 'session2',
            titleAr: 'تقنيات التجميل الحديثة',
            titleEn: 'Modern Cosmetic Techniques',
            descriptionAr: 'ورشة عمل حول أحدث تقنيات تجميل الأسنان',
            descriptionEn: 'Workshop on the latest cosmetic dentistry techniques',
            startTime: new Date('2025-03-15T11:00:00Z'),
            endTime: new Date('2025-03-15T12:30:00Z'),
            type: 'WORKSHOP',
            cmeHours: 1.5,
            maxAttendees: 100,
            attendeeCount: 87,
            speaker: {
              id: 'speaker2',
              nameAr: 'د. فاطمة أحمد',
              nameEn: 'Dr. Fatima Ahmed',
              bio: 'أخصائية تجميل الأسنان'
            }
          }
        ];

        return {
          sessions: mockSessions,
          pagination: createPaginationResult(1, 10, mockSessions.length)
        };
      }
    } catch (error) {
      logger.error('Error getting sessions:', error);
      throw error;
    }
  }

  // Get session by ID
  async getSessionById(sessionId) {
    try {
      if (prisma) {
        const session = await prisma.session.findUnique({
          where: { id: sessionId },
          include: {
            speaker: true,
            attendances: {
              include: {
                user: {
                  select: {
                    id: true,
                    arabicName: true,
                    englishName: true
                  }
                }
              }
            }
          }
        });

        if (!session) {
          throw new NotFoundError('Session not found');
        }

        return {
          ...session,
          attendeeCount: session.attendances.length
        };
      } else {
        // Mock data
        if (sessionId === 'session1') {
          return {
            id: 'session1',
            titleAr: 'مستقبل زراعة الأسنان',
            titleEn: 'The Future of Dental Implants',
            descriptionAr: 'محاضرة شاملة حول أحدث التقنيات في زراعة الأسنان والتطورات المستقبلية في هذا المجال',
            descriptionEn: 'Comprehensive lecture on the latest techniques in dental implants and future developments in this field',
            startTime: new Date('2025-03-15T09:00:00Z'),
            endTime: new Date('2025-03-15T10:30:00Z'),
            type: 'LECTURE',
            cmeHours: 1.5,
            maxAttendees: 500,
            attendeeCount: 245,
            speaker: {
              id: 'speaker1',
              nameAr: 'د. أحمد محمد السعيد',
              nameEn: 'Dr. Ahmed Mohammed Al-Saeed',
              bio: 'استشاري طب الأسنان مع خبرة 15 عام في زراعة الأسنان',
              linkedin: 'https://linkedin.com/in/ahmed-saeed',
              email: '<EMAIL>'
            }
          };
        }
        throw new NotFoundError('Session not found');
      }
    } catch (error) {
      logger.error('Error getting session by ID:', error);
      throw error;
    }
  }

  // Get all speakers
  async getSpeakers() {
    try {
      if (prisma) {
        const speakers = await prisma.speaker.findMany({
          include: {
            sessions: {
              select: {
                id: true,
                titleAr: true,
                titleEn: true,
                startTime: true,
                type: true
              }
            }
          },
          orderBy: { nameEn: 'asc' }
        });

        return speakers;
      } else {
        // Mock data
        return [
          {
            id: 'speaker1',
            nameAr: 'د. أحمد محمد السعيد',
            nameEn: 'Dr. Ahmed Mohammed Al-Saeed',
            bio: 'استشاري طب الأسنان مع خبرة 15 عام في زراعة الأسنان',
            linkedin: 'https://linkedin.com/in/ahmed-saeed',
            sessions: [
              {
                id: 'session1',
                titleAr: 'مستقبل زراعة الأسنان',
                titleEn: 'The Future of Dental Implants',
                startTime: new Date('2025-03-15T09:00:00Z'),
                type: 'LECTURE'
              }
            ]
          },
          {
            id: 'speaker2',
            nameAr: 'د. فاطمة أحمد',
            nameEn: 'Dr. Fatima Ahmed',
            bio: 'أخصائية تجميل الأسنان مع خبرة 10 سنوات',
            linkedin: 'https://linkedin.com/in/fatima-ahmed',
            sessions: [
              {
                id: 'session2',
                titleAr: 'تقنيات التجميل الحديثة',
                titleEn: 'Modern Cosmetic Techniques',
                startTime: new Date('2025-03-15T11:00:00Z'),
                type: 'WORKSHOP'
              }
            ]
          }
        ];
      }
    } catch (error) {
      logger.error('Error getting speakers:', error);
      throw error;
    }
  }

  // Get speaker by ID
  async getSpeakerById(speakerId) {
    try {
      if (prisma) {
        const speaker = await prisma.speaker.findUnique({
          where: { id: speakerId },
          include: {
            sessions: {
              select: {
                id: true,
                titleAr: true,
                titleEn: true,
                startTime: true,
                endTime: true,
                type: true,
                cmeHours: true
              },
              orderBy: { startTime: 'asc' }
            }
          }
        });

        if (!speaker) {
          throw new NotFoundError('Speaker not found');
        }

        return speaker;
      } else {
        // Mock data
        if (speakerId === 'speaker1') {
          return {
            id: 'speaker1',
            nameAr: 'د. أحمد محمد السعيد',
            nameEn: 'Dr. Ahmed Mohammed Al-Saeed',
            bio: 'استشاري طب الأسنان مع خبرة 15 عام في زراعة الأسنان. حاصل على الدكتوراه من جامعة الملك سعود ومتخصص في الزراعة الفورية للأسنان.',
            linkedin: 'https://linkedin.com/in/ahmed-saeed',
            email: '<EMAIL>',
            sessions: [
              {
                id: 'session1',
                titleAr: 'مستقبل زراعة الأسنان',
                titleEn: 'The Future of Dental Implants',
                startTime: new Date('2025-03-15T09:00:00Z'),
                endTime: new Date('2025-03-15T10:30:00Z'),
                type: 'LECTURE',
                cmeHours: 1.5
              }
            ]
          };
        }
        throw new NotFoundError('Speaker not found');
      }
    } catch (error) {
      logger.error('Error getting speaker by ID:', error);
      throw error;
    }
  }

  // Register attendance for a session
  async registerAttendance(sessionId, userId) {
    try {
      // Check if session exists
      const session = await this.getSessionById(sessionId);
      
      if (prisma) {
        // Check if user is already registered
        const existingAttendance = await prisma.attendance.findUnique({
          where: {
            userId_sessionId: {
              userId,
              sessionId
            }
          }
        });

        if (existingAttendance) {
          throw new ValidationError('User already registered for this session');
        }

        // Check session capacity
        const attendanceCount = await prisma.attendance.count({
          where: { sessionId }
        });

        if (attendanceCount >= session.maxAttendees) {
          throw new ValidationError('Session is full');
        }

        // Create attendance record
        const attendance = await prisma.attendance.create({
          data: {
            userId,
            sessionId,
            registeredAt: new Date()
          },
          include: {
            session: {
              select: {
                titleAr: true,
                titleEn: true,
                startTime: true
              }
            }
          }
        });

        logger.logUserAction(userId, 'SESSION_ATTENDANCE_REGISTERED', {
          sessionId,
          sessionTitle: session.titleEn
        });

        return attendance;
      } else {
        // Mock attendance
        const attendance = {
          id: 'attendance_' + Date.now(),
          userId,
          sessionId,
          registeredAt: new Date(),
          session: {
            titleAr: session.titleAr,
            titleEn: session.titleEn,
            startTime: session.startTime
          }
        };

        logger.logUserAction(userId, 'SESSION_ATTENDANCE_REGISTERED', {
          sessionId,
          sessionTitle: session.titleEn
        });

        return attendance;
      }
    } catch (error) {
      logger.error('Error registering attendance:', error);
      throw error;
    }
  }

  // Check-in to session with QR code
  async checkInToSession(sessionId, userId, qrCode) {
    try {
      // Verify session exists and is active
      const session = await this.getSessionById(sessionId);

      if (prisma) {
        // Verify QR code
        const validQR = await prisma.qRCode.findFirst({
          where: {
            code: qrCode,
            type: 'SESSION_ATTENDANCE',
            data: {
              path: ['sessionId'],
              equals: sessionId
            },
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          }
        });

        if (!validQR) {
          throw new ValidationError('Invalid or expired QR code');
        }

        // Check if user already checked in
        const existingAttendance = await prisma.sessionAttendance.findUnique({
          where: {
            userId_sessionId: {
              userId,
              sessionId
            }
          }
        });

        if (existingAttendance) {
          throw new ValidationError('User already checked in to this session');
        }

        // Create attendance record
        const attendance = await prisma.sessionAttendance.create({
          data: {
            userId,
            sessionId,
            checkInTime: new Date(),
            qrCode
          },
          include: {
            session: {
              select: {
                titleAr: true,
                titleEn: true,
                startTime: true,
                cmeHours: true
              }
            }
          }
        });

        // Award points for session attendance
        await gamificationService.awardPoints(
          userId,
          'SESSION_ATTENDANCE',
          20,
          `Attended session: ${session.titleEn}`,
          sessionId
        );

        logger.logUserAction(userId, 'SESSION_CHECK_IN', {
          sessionId,
          sessionTitle: session.titleEn,
          checkInTime: attendance.checkInTime
        });

        return attendance;
      } else {
        // Mock check-in
        const attendance = {
          id: 'attendance_' + Date.now(),
          userId,
          sessionId,
          checkInTime: new Date(),
          qrCode,
          session: {
            titleAr: session.titleAr,
            titleEn: session.titleEn,
            startTime: session.startTime,
            cmeHours: session.cmeHours
          }
        };

        // Award points for session attendance
        await gamificationService.awardPoints(
          userId,
          'SESSION_ATTENDANCE',
          20,
          `Attended session: ${session.titleEn}`,
          sessionId
        );

        logger.logUserAction(userId, 'SESSION_CHECK_IN', {
          sessionId,
          sessionTitle: session.titleEn,
          checkInTime: attendance.checkInTime
        });

        return attendance;
      }
    } catch (error) {
      logger.error('Error checking in to session:', error);
      throw error;
    }
  }

  // Check-out from session
  async checkOutFromSession(sessionId, userId) {
    try {
      if (prisma) {
        const attendance = await prisma.sessionAttendance.findUnique({
          where: {
            userId_sessionId: {
              userId,
              sessionId
            }
          },
          include: {
            session: {
              select: {
                titleEn: true,
                cmeHours: true
              }
            }
          }
        });

        if (!attendance) {
          throw new NotFoundError('Attendance record not found');
        }

        if (attendance.checkOutTime) {
          throw new ValidationError('User already checked out');
        }

        const checkOutTime = new Date();
        const duration = Math.floor((checkOutTime - attendance.checkInTime) / (1000 * 60)); // minutes

        const updatedAttendance = await prisma.sessionAttendance.update({
          where: { id: attendance.id },
          data: {
            checkOutTime,
            attendanceDuration: duration
          },
          include: {
            session: {
              select: {
                titleAr: true,
                titleEn: true,
                cmeHours: true
              }
            }
          }
        });

        // Award bonus points for completing full session (if attended > 80% of session)
        const sessionDuration = 60; // Assume 60 minutes default
        if (duration >= sessionDuration * 0.8) {
          await gamificationService.awardPoints(
            userId,
            'SESSION_ATTENDANCE',
            10,
            `Completed full session: ${attendance.session.titleEn}`,
            sessionId
          );
        }

        logger.logUserAction(userId, 'SESSION_CHECK_OUT', {
          sessionId,
          sessionTitle: attendance.session.titleEn,
          duration: duration
        });

        return updatedAttendance;
      } else {
        // Mock check-out
        return {
          id: 'attendance_' + Date.now(),
          userId,
          sessionId,
          checkOutTime: new Date(),
          attendanceDuration: 60
        };
      }
    } catch (error) {
      logger.error('Error checking out from session:', error);
      throw error;
    }
  }
}

module.exports = new ConferenceService();
