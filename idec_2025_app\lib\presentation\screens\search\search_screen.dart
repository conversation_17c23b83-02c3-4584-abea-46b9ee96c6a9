import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  
  final List<String> _categories = ['الكل', 'الجلسات', 'المتحدثون', 'المواضيع'];
  final List<String> _recentSearches = [
    'زراعة الأسنان',
    'تقويم الأسنان',
    'علاج الجذور',
    'جراحة اللثة',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Search Header
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(AppColors.primaryRed),
                    Color(AppColors.darkRed),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Search Bar
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: Color(AppColors.primaryWhite),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryWhite),
                            borderRadius: BorderRadius.circular(AppSizes.radiusL),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _searchController,
                            autofocus: true,
                            decoration: InputDecoration(
                              hintText: 'ابحث عن الجلسات، المتحدثين، المواضيع...',
                              prefixIcon: const Icon(
                                Icons.search,
                                color: Color(AppColors.primaryRed),
                              ),
                              suffixIcon: _searchQuery.isNotEmpty
                                  ? IconButton(
                                      onPressed: () {
                                        _searchController.clear();
                                        setState(() {
                                          _searchQuery = '';
                                        });
                                      },
                                      icon: const Icon(
                                        Icons.clear,
                                        color: Color(AppColors.grey),
                                      ),
                                    )
                                  : null,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: const Color(AppColors.primaryWhite),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: AppSizes.paddingM,
                                vertical: AppSizes.paddingM,
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _searchQuery = value;
                              });
                            },
                            onSubmitted: (value) {
                              _performSearch(value);
                            },
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: _showFilterDialog,
                        icon: const Icon(
                          Icons.tune,
                          color: Color(AppColors.primaryWhite),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSizes.paddingM),
                  
                  // Category Filters
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: _categories.map((category) {
                        final isSelected = category == _selectedCategory;
                        return Container(
                          margin: const EdgeInsets.only(right: AppSizes.paddingS),
                          child: FilterChip(
                            label: Text(category),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = category;
                              });
                            },
                            backgroundColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                            selectedColor: const Color(AppColors.primaryWhite),
                            labelStyle: TextStyle(
                              color: isSelected
                                  ? const Color(AppColors.primaryRed)
                                  : const Color(AppColors.primaryWhite),
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusL),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
            
            // Search Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: _searchQuery.isEmpty
                    ? _buildEmptyState()
                    : _buildSearchResults(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          if (_recentSearches.isNotEmpty) ...[
            Text(
              'عمليات البحث الأخيرة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(AppColors.primaryBlack),
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingM),
            
            Wrap(
              spacing: AppSizes.paddingS,
              runSpacing: AppSizes.paddingS,
              children: _recentSearches.map((search) => InkWell(
                onTap: () {
                  _searchController.text = search;
                  setState(() {
                    _searchQuery = search;
                  });
                  _performSearch(search);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingM,
                    vertical: AppSizes.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(AppColors.lightGrey),
                    borderRadius: BorderRadius.circular(AppSizes.radiusL),
                    border: Border.all(
                      color: const Color(AppColors.primaryRed).withValues(alpha: 0.2),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.history,
                        size: AppSizes.iconS,
                        color: Color(AppColors.grey),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Text(
                        search,
                        style: const TextStyle(
                          color: Color(AppColors.primaryBlack),
                        ),
                      ),
                    ],
                  ),
                ),
              )).toList(),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
          ],
          
          // Popular Topics
          Text(
            'المواضيع الشائعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          _buildPopularTopics(),
          
          const SizedBox(height: AppSizes.paddingL),
          
          // Quick Actions
          Text(
            'إجراءات سريعة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(AppColors.primaryBlack),
            ),
          ),
          
          const SizedBox(height: AppSizes.paddingM),
          
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildPopularTopics() {
    final topics = [
      {'title': 'زراعة الأسنان', 'count': '15 جلسة', 'icon': Icons.medical_services},
      {'title': 'تقويم الأسنان', 'count': '12 جلسة', 'icon': Icons.straighten},
      {'title': 'علاج الجذور', 'count': '8 جلسات', 'icon': Icons.healing},
      {'title': 'جراحة الفم', 'count': '10 جلسات', 'icon': Icons.local_hospital},
      {'title': 'طب أسنان الأطفال', 'count': '6 جلسات', 'icon': Icons.child_care},
      {'title': 'التركيبات السنية', 'count': '9 جلسات', 'icon': Icons.build},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 1.2,
      ),
      itemCount: topics.length,
      itemBuilder: (context, index) {
        final topic = topics[index];
        return InkWell(
          onTap: () {
            _searchController.text = topic['title'] as String;
            setState(() {
              _searchQuery = topic['title'] as String;
            });
            _performSearch(topic['title'] as String);
          },
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
          child: Container(
            padding: const EdgeInsets.all(AppSizes.paddingM),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                  const Color(AppColors.primaryBlack).withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(AppSizes.radiusL),
              border: Border.all(
                color: const Color(AppColors.primaryRed).withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(AppColors.primaryRed),
                        const Color(AppColors.darkRed),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  ),
                  child: Icon(
                    topic['icon'] as IconData,
                    color: const Color(AppColors.primaryWhite),
                    size: AppSizes.iconM,
                  ),
                ),
                const SizedBox(height: AppSizes.paddingS),
                Text(
                  topic['title'] as String,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Color(AppColors.primaryBlack),
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  topic['count'] as String,
                  style: TextStyle(
                    color: const Color(AppColors.grey).withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {
        'title': 'جدولي الشخصي',
        'subtitle': 'عرض الجلسات المسجلة',
        'icon': Icons.schedule,
        'color': const Color(AppColors.primaryRed),
      },
      {
        'title': 'المتحدثون',
        'subtitle': 'تصفح جميع المتحدثين',
        'icon': Icons.people,
        'color': const Color(AppColors.primaryBlack),
      },
      {
        'title': 'الإشعارات',
        'subtitle': 'آخر التحديثات',
        'icon': Icons.notifications,
        'color': const Color(AppColors.warning),
      },
    ];

    return Column(
      children: actions.map((action) => Container(
        margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
        child: ListTile(
          leading: Container(
            padding: const EdgeInsets.all(AppSizes.paddingS),
            decoration: BoxDecoration(
              color: (action['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSizes.radiusM),
            ),
            child: Icon(
              action['icon'] as IconData,
              color: action['color'] as Color,
            ),
          ),
          title: Text(
            action['title'] as String,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Color(AppColors.primaryBlack),
            ),
          ),
          subtitle: Text(
            action['subtitle'] as String,
            style: const TextStyle(
              color: Color(AppColors.grey),
            ),
          ),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: Color(AppColors.grey),
            size: AppSizes.iconS,
          ),
          onTap: () {
            // Navigate to respective screen
          },
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          tileColor: const Color(AppColors.lightGrey),
        ),
      )).toList(),
    );
  }

  Widget _buildSearchResults() {
    return DefaultTabController(
      length: 4,
      child: Column(
        children: [
          // Tab Bar
          Container(
            color: const Color(AppColors.primaryWhite),
            child: TabBar(
              controller: _tabController,
              indicatorColor: const Color(AppColors.primaryRed),
              labelColor: const Color(AppColors.primaryRed),
              unselectedLabelColor: const Color(AppColors.grey),
              tabs: const [
                Tab(text: 'الكل'),
                Tab(text: 'الجلسات'),
                Tab(text: 'المتحدثون'),
                Tab(text: 'المواضيع'),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllResults(),
                _buildSessionResults(),
                _buildSpeakerResults(),
                _buildTopicResults(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllResults() {
    return ListView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      children: [
        _buildResultSection('الجلسات', _getSessionResults()),
        const SizedBox(height: AppSizes.paddingL),
        _buildResultSection('المتحدثون', _getSpeakerResults()),
        const SizedBox(height: AppSizes.paddingL),
        _buildResultSection('المواضيع', _getTopicResults()),
      ],
    );
  }

  Widget _buildResultSection(String title, List<Widget> results) {
    if (results.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(AppColors.primaryBlack),
          ),
        ),
        const SizedBox(height: AppSizes.paddingM),
        ...results.take(3), // Show only first 3 results
        if (results.length > 3)
          TextButton(
            onPressed: () {
              // Show all results
            },
            child: Text('عرض جميع النتائج (${results.length})'),
          ),
      ],
    );
  }

  Widget _buildSessionResults() {
    return ListView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      children: _getSessionResults(),
    );
  }

  Widget _buildSpeakerResults() {
    return ListView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      children: _getSpeakerResults(),
    );
  }

  Widget _buildTopicResults() {
    return ListView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      children: _getTopicResults(),
    );
  }

  List<Widget> _getSessionResults() {
    // Mock search results for sessions
    final sessions = [
      {
        'title': 'أحدث تقنيات زراعة الأسنان',
        'speaker': 'د. أحمد محمد علي',
        'time': '11:00 - 12:30',
        'date': '15 مارس',
        'location': 'قاعة المحاضرات A',
      },
      {
        'title': 'زراعة الأسنان الفورية',
        'speaker': 'د. فاطمة أحمد حسن',
        'time': '14:00 - 15:30',
        'date': '16 مارس',
        'location': 'قاعة المحاضرات B',
      },
    ];

    return sessions.map((session) => Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppSizes.paddingS),
          decoration: BoxDecoration(
            color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Icon(
            Icons.event,
            color: Color(AppColors.primaryRed),
          ),
        ),
        title: Text(
          session['title']!,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المتحدث: ${session['speaker']}'),
            Text('${session['date']} • ${session['time']}'),
            Text('المكان: ${session['location']}'),
          ],
        ),
        isThreeLine: true,
        onTap: () {
          // Navigate to session details
        },
      ),
    )).toList();
  }

  List<Widget> _getSpeakerResults() {
    // Mock search results for speakers
    final speakers = [
      {
        'name': 'د. أحمد محمد علي',
        'title': 'استشاري جراحة الفم والوجه والفكين',
        'sessions': '3 جلسات',
      },
      {
        'name': 'د. فاطمة أحمد حسن',
        'title': 'أستاذة طب أسنان الأطفال',
        'sessions': '2 جلسة',
      },
    ];

    return speakers.map((speaker) => Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
          child: const Icon(
            Icons.person,
            color: Color(AppColors.primaryRed),
          ),
        ),
        title: Text(
          speaker['name']!,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(speaker['title']!),
            Text(speaker['sessions']!),
          ],
        ),
        onTap: () {
          // Navigate to speaker details
        },
      ),
    )).toList();
  }

  List<Widget> _getTopicResults() {
    // Mock search results for topics
    final topics = [
      {
        'title': 'زراعة الأسنان',
        'description': 'جميع الجلسات المتعلقة بزراعة الأسنان',
        'count': '15 جلسة',
      },
      {
        'title': 'تقنيات الزراعة الحديثة',
        'description': 'أحدث التقنيات في مجال زراعة الأسنان',
        'count': '8 جلسات',
      },
    ];

    return topics.map((topic) => Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppSizes.paddingS),
          decoration: BoxDecoration(
            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
          child: const Icon(
            Icons.topic,
            color: Color(AppColors.primaryBlack),
          ),
        ),
        title: Text(
          topic['title']!,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(topic['description']!),
            Text(topic['count']!),
          ],
        ),
        onTap: () {
          // Navigate to topic details
        },
      ),
    )).toList();
  }

  void _performSearch(String query) {
    // Add to recent searches if not already present
    if (!_recentSearches.contains(query)) {
      setState(() {
        _recentSearches.insert(0, query);
        if (_recentSearches.length > 5) {
          _recentSearches.removeLast();
        }
      });
    }
    
    // Perform actual search logic here
    print('Searching for: $query');
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة النتائج',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            const Text('التاريخ:'),
            const SizedBox(height: AppSizes.paddingS),
            Wrap(
              spacing: AppSizes.paddingS,
              children: ['اليوم', 'غداً', 'هذا الأسبوع'].map((date) => 
                FilterChip(
                  label: Text(date),
                  selected: false,
                  onSelected: (selected) {},
                ),
              ).toList(),
            ),
            
            const SizedBox(height: AppSizes.paddingM),
            
            const Text('المستوى:'),
            const SizedBox(height: AppSizes.paddingS),
            Wrap(
              spacing: AppSizes.paddingS,
              children: ['مبتدئ', 'متوسط', 'متقدم'].map((level) => 
                FilterChip(
                  label: Text(level),
                  selected: false,
                  onSelected: (selected) {},
                ),
              ).toList(),
            ),
            
            const SizedBox(height: AppSizes.paddingL),
            
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إعادة تعيين'),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
