const { PrismaClient } = require('@prisma/client');
const { NotFoundError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Initialize Prisma client
let prisma;
try {
  prisma = new PrismaClient();
} catch (error) {
  logger.error('Failed to initialize Prisma client:', error);
  prisma = null;
}

class SettingsService {
  constructor() {
    // Default system settings
    this.defaultSettings = {
      // Conference Settings
      conference: {
        name_ar: 'المؤتمر الدولي لطب الأسنان 2025',
        name_en: 'International Dental Conference 2025',
        description_ar: 'المؤتمر الدولي الأول لطب الأسنان في المملكة العربية السعودية',
        description_en: 'The First International Dental Conference in Saudi Arabia',
        start_date: '2025-03-15',
        end_date: '2025-03-17',
        location: 'الرياض، المملكة العربية السعودية',
        max_attendees: 2000,
        registration_open: true,
        early_bird_deadline: '2025-02-01',
        regular_deadline: '2025-02-28',
        late_deadline: '2025-03-10'
      },
      
      // Pricing Settings
      pricing: {
        currency: 'SAR',
        doctor_early: 500.00,
        doctor_regular: 700.00,
        doctor_late: 900.00,
        student_year_5_early: 200.00,
        student_year_5_regular: 300.00,
        student_year_5_late: 400.00,
        student_year_4_early: 150.00,
        student_year_4_regular: 250.00,
        student_year_4_late: 350.00,
        student_other_early: 100.00,
        student_other_regular: 200.00,
        student_other_late: 300.00
      },
      
      // Email Settings
      email: {
        smtp_host: 'smtp.gmail.com',
        smtp_port: 587,
        smtp_secure: false,
        from_name: 'IDEC 2025',
        from_email: '<EMAIL>',
        support_email: '<EMAIL>',
        admin_email: '<EMAIL>'
      },
      
      // SMS Settings
      sms: {
        provider: 'local',
        api_key: '',
        sender_name: 'IDEC2025',
        enabled: true
      },
      
      // Payment Settings
      payment: {
        gateway: 'local',
        test_mode: true,
        merchant_id: '',
        api_key: '',
        webhook_secret: '',
        supported_methods: ['credit_card', 'bank_transfer', 'apple_pay', 'stc_pay']
      },
      
      // System Settings
      system: {
        maintenance_mode: false,
        registration_enabled: true,
        login_enabled: true,
        max_login_attempts: 5,
        session_timeout: 24, // hours
        file_upload_max_size: 10, // MB
        allowed_file_types: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'],
        timezone: 'Asia/Riyadh',
        language_default: 'ar'
      },
      
      // Security Settings
      security: {
        password_min_length: 8,
        password_require_uppercase: true,
        password_require_lowercase: true,
        password_require_numbers: true,
        password_require_symbols: true,
        two_factor_enabled: false,
        session_security: true,
        api_rate_limit: 100, // requests per minute
        admin_rate_limit: 200
      },
      
      // Notification Settings
      notifications: {
        email_enabled: true,
        sms_enabled: true,
        push_enabled: false,
        welcome_email: true,
        payment_confirmation: true,
        subscription_updates: true,
        system_alerts: true
      }
    };
  }

  // Get all settings
  async getAllSettings() {
    try {
      if (prisma) {
        const settings = await prisma.setting.findMany();
        
        // Convert array to nested object
        const settingsObject = {};
        settings.forEach(setting => {
          const keys = setting.key.split('.');
          let current = settingsObject;
          
          for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
              current[keys[i]] = {};
            }
            current = current[keys[i]];
          }
          
          current[keys[keys.length - 1]] = this.parseValue(setting.value, setting.type);
        });
        
        // Merge with defaults for missing settings
        return this.mergeWithDefaults(settingsObject);
      } else {
        // Return default settings
        return this.defaultSettings;
      }
    } catch (error) {
      logger.error('Error getting all settings:', error);
      return this.defaultSettings;
    }
  }

  // Get settings by category
  async getSettingsByCategory(category) {
    try {
      const allSettings = await this.getAllSettings();
      return allSettings[category] || {};
    } catch (error) {
      logger.error('Error getting settings by category:', error);
      return this.defaultSettings[category] || {};
    }
  }

  // Get single setting
  async getSetting(key) {
    try {
      if (prisma) {
        const setting = await prisma.setting.findUnique({
          where: { key }
        });
        
        if (setting) {
          return this.parseValue(setting.value, setting.type);
        }
      }
      
      // Return default value
      return this.getDefaultValue(key);
    } catch (error) {
      logger.error('Error getting setting:', error);
      return this.getDefaultValue(key);
    }
  }

  // Update setting
  async updateSetting(key, value, updatedBy) {
    try {
      const type = this.getValueType(value);
      const stringValue = this.stringifyValue(value);
      
      let setting;
      if (prisma) {
        setting = await prisma.setting.upsert({
          where: { key },
          update: {
            value: stringValue,
            type,
            updatedBy,
            updatedAt: new Date()
          },
          create: {
            key,
            value: stringValue,
            type,
            createdBy: updatedBy,
            updatedBy,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        });
      } else {
        // Mock setting update
        setting = {
          key,
          value: stringValue,
          type,
          updatedBy,
          updatedAt: new Date()
        };
      }

      logger.logAudit(updatedBy, 'SETTING_UPDATED', 'setting', key, {
        oldValue: await this.getSetting(key),
        newValue: value
      });

      return this.parseValue(setting.value, setting.type);
    } catch (error) {
      logger.error('Error updating setting:', error);
      throw error;
    }
  }

  // Update multiple settings
  async updateSettings(settings, updatedBy) {
    try {
      const results = {};
      
      for (const [key, value] of Object.entries(settings)) {
        results[key] = await this.updateSetting(key, value, updatedBy);
      }

      logger.logAudit(updatedBy, 'SETTINGS_BULK_UPDATE', 'settings', null, {
        updatedKeys: Object.keys(settings)
      });

      return results;
    } catch (error) {
      logger.error('Error updating settings:', error);
      throw error;
    }
  }

  // Reset settings to defaults
  async resetSettings(category = null, updatedBy) {
    try {
      if (category) {
        // Reset specific category
        const defaultCategorySettings = this.defaultSettings[category];
        if (!defaultCategorySettings) {
          throw new ValidationError(`Category '${category}' not found`);
        }
        
        const flatSettings = this.flattenObject(defaultCategorySettings, category);
        return await this.updateSettings(flatSettings, updatedBy);
      } else {
        // Reset all settings
        const flatSettings = this.flattenObject(this.defaultSettings);
        return await this.updateSettings(flatSettings, updatedBy);
      }
    } catch (error) {
      logger.error('Error resetting settings:', error);
      throw error;
    }
  }

  // Helper methods
  parseValue(value, type) {
    switch (type) {
      case 'boolean':
        return value === 'true';
      case 'number':
        return parseFloat(value);
      case 'integer':
        return parseInt(value);
      case 'array':
        return JSON.parse(value);
      case 'object':
        return JSON.parse(value);
      default:
        return value;
    }
  }

  stringifyValue(value) {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    return String(value);
  }

  getValueType(value) {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'integer' : 'number';
    }
    if (Array.isArray(value)) return 'array';
    if (typeof value === 'object') return 'object';
    return 'string';
  }

  getDefaultValue(key) {
    const keys = key.split('.');
    let current = this.defaultSettings;
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }
    
    return current;
  }

  mergeWithDefaults(settings) {
    const merged = JSON.parse(JSON.stringify(this.defaultSettings));
    
    function deepMerge(target, source) {
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          if (!target[key]) target[key] = {};
          deepMerge(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    }
    
    deepMerge(merged, settings);
    return merged;
  }

  flattenObject(obj, prefix = '') {
    const flattened = {};
    
    for (const key in obj) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        Object.assign(flattened, this.flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
    
    return flattened;
  }

  // Get system status
  async getSystemStatus() {
    const settings = await this.getAllSettings();
    
    return {
      maintenance_mode: settings.system.maintenance_mode,
      registration_enabled: settings.system.registration_enabled,
      login_enabled: settings.system.login_enabled,
      conference_active: new Date() >= new Date(settings.conference.start_date) && 
                        new Date() <= new Date(settings.conference.end_date),
      early_bird_active: new Date() <= new Date(settings.conference.early_bird_deadline),
      registration_deadline_passed: new Date() > new Date(settings.conference.late_deadline)
    };
  }
}

module.exports = new SettingsService();
