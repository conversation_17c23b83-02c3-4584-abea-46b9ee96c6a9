import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class AgendaScreen extends StatefulWidget {
  const AgendaScreen({super.key});

  @override
  State<AgendaScreen> createState() => _AgendaScreenState();
}

class _AgendaScreenState extends State<AgendaScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  int _selectedDay = 0;
  final List<String> _days = ['اليوم الأول', 'اليوم الثاني', 'اليوم الثالث'];
  final List<String> _dates = ['15 مارس', '16 مارس', '17 مارس'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 280,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'جدولي الشخصي',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background Pattern
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                50 * _animationController.value,
                                -30 * _animationController.value,
                              ),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/calendar_pattern.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60),
                            
                            // Calendar Icon with Animation
                            AnimatedBuilder(
                              animation: _animationController,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: 0.8 + (0.2 * _animationController.value),
                                  child: Container(
                                    padding: const EdgeInsets.all(AppSizes.paddingL),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                          const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                      border: Border.all(
                                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        width: 2,
                                      ),
                                    ),
                                    child: const Icon(
                                      Icons.calendar_today,
                                      size: 50,
                                      color: Color(AppColors.primaryWhite),
                                    ),
                                  ),
                                );
                              },
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Stats Row
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('12', 'جلسة مسجلة'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('8', 'ساعات تعليم'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('3', 'أيام'),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                tabs: List.generate(3, (index) {
                  return Tab(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(_days[index]),
                        Text(
                          _dates[index],
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
          ];
        },
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDaySchedule(0),
                _buildDaySchedule(1),
                _buildDaySchedule(2),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddSessionDialog(),
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.add),
        label: const Text('إضافة جلسة'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildDaySchedule(int dayIndex) {
    final sessions = _getSessionsForDay(dayIndex);
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: sessions.length,
      itemBuilder: (context, index) {
        final session = sessions[index];
        return _buildSessionCard(session, index);
      },
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session, int index) {
    final isRegistered = session['isRegistered'] ?? false;
    final isLive = session['isLive'] ?? false;
    final hasConflict = session['hasConflict'] ?? false;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
            border: Border.all(
              color: isLive 
                  ? const Color(AppColors.success)
                  : hasConflict 
                      ? const Color(AppColors.error)
                      : const Color(AppColors.primaryRed).withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              // Session Header
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isLive
                        ? [const Color(AppColors.success), const Color(AppColors.success).withValues(alpha: 0.8)]
                        : hasConflict
                            ? [const Color(AppColors.error), const Color(AppColors.error).withValues(alpha: 0.8)]
                            : [const Color(AppColors.primaryRed), const Color(AppColors.darkRed)],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppSizes.radiusL),
                    topRight: Radius.circular(AppSizes.radiusL),
                  ),
                ),
                child: Row(
                  children: [
                    // Time
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingM,
                        vertical: AppSizes.paddingS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.access_time,
                            color: Color(AppColors.primaryWhite),
                            size: AppSizes.iconS,
                          ),
                          const SizedBox(width: AppSizes.paddingXS),
                          Text(
                            session['time'],
                            style: const TextStyle(
                              color: Color(AppColors.primaryWhite),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // Status Badges
                    if (isLive)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingS,
                          vertical: AppSizes.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryWhite),
                          borderRadius: BorderRadius.circular(AppSizes.radiusS),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Color(AppColors.success),
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: AppSizes.paddingXS),
                            const Text(
                              'مباشر الآن',
                              style: TextStyle(
                                color: Color(AppColors.success),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    
                    if (hasConflict && !isLive)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSizes.paddingS,
                          vertical: AppSizes.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryWhite),
                          borderRadius: BorderRadius.circular(AppSizes.radiusS),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.warning,
                              color: Color(AppColors.error),
                              size: 12,
                            ),
                            SizedBox(width: AppSizes.paddingXS),
                            Text(
                              'تعارض',
                              style: TextStyle(
                                color: Color(AppColors.error),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              
              // Session Content
              Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      session['title'],
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingS),
                    
                    // Speaker
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 15,
                          backgroundColor: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                          child: const Icon(
                            Icons.person,
                            size: 16,
                            color: Color(AppColors.primaryRed),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Expanded(
                          child: Text(
                            session['speaker'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: const Color(AppColors.grey),
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingS),
                    
                    // Location
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on,
                          size: AppSizes.iconS,
                          color: Color(AppColors.primaryRed),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Text(
                          session['location'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: const Color(AppColors.grey),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            '${session['credits']} ساعة',
                            style: const TextStyle(
                              color: Color(AppColors.primaryRed),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: isRegistered ? null : () => _registerForSession(session),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isRegistered 
                                  ? const Color(AppColors.success)
                                  : const Color(AppColors.primaryRed),
                              foregroundColor: const Color(AppColors.primaryWhite),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            icon: Icon(
                              isRegistered ? Icons.check : Icons.add,
                              size: AppSizes.iconS,
                            ),
                            label: Text(
                              isRegistered ? 'مسجل' : 'تسجيل',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: AppSizes.paddingS),
                        
                        OutlinedButton.icon(
                          onPressed: () => _showSessionDetails(session),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: const Color(AppColors.primaryRed),
                            side: const BorderSide(color: Color(AppColors.primaryRed)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusM),
                            ),
                          ),
                          icon: const Icon(Icons.info_outline, size: AppSizes.iconS),
                          label: const Text('تفاصيل', style: TextStyle(fontSize: 12)),
                        ),
                        
                        const SizedBox(width: AppSizes.paddingS),
                        
                        IconButton(
                          onPressed: () => _shareSession(session),
                          icon: const Icon(
                            Icons.share,
                            color: Color(AppColors.primaryRed),
                            size: AppSizes.iconS,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _registerForSession(Map<String, dynamic> session) {
    // Registration logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم التسجيل في جلسة: ${session['title']}'),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _showSessionDetails(Map<String, dynamic> session) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      session['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingM),
                    
                    Text(
                      'وصف الجلسة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryBlack),
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.paddingS),
                    
                    Text(
                      session['description'] ?? 'وصف تفصيلي للجلسة العلمية وما ستتضمنه من مواضيع ومناقشات مهمة في مجال طب الأسنان.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(AppColors.primaryBlack),
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareSession(Map<String, dynamic> session) {
    // Share logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة جلسة: ${session['title']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _showAddSessionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة جلسة جديدة'),
        content: const Text('هذه الميزة ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getSessionsForDay(int dayIndex) {
    // Mock data for sessions
    final allSessions = [
      // Day 0
      [
        {
          'time': '09:00 - 10:30',
          'title': 'الافتتاح والكلمة الترحيبية',
          'speaker': 'د. أحمد محمد علي',
          'location': 'القاعة الرئيسية',
          'credits': 1.5,
          'isRegistered': true,
          'isLive': false,
          'hasConflict': false,
        },
        {
          'time': '11:00 - 12:30',
          'title': 'أحدث تقنيات زراعة الأسنان',
          'speaker': 'د. فاطمة أحمد حسن',
          'location': 'قاعة المحاضرات A',
          'credits': 1.5,
          'isRegistered': true,
          'isLive': true,
          'hasConflict': false,
        },
        {
          'time': '14:00 - 15:30',
          'title': 'التقويم الشفاف والتقنيات الحديثة',
          'speaker': 'د. محمد عبدالله الزهراني',
          'location': 'قاعة المحاضرات B',
          'credits': 1.5,
          'isRegistered': false,
          'isLive': false,
          'hasConflict': true,
        },
      ],
      // Day 1
      [
        {
          'time': '09:00 - 10:30',
          'title': 'علاج الجذور بالمجهر',
          'speaker': 'د. عبدالرحمن علي محمد',
          'location': 'قاعة المحاضرات A',
          'credits': 1.5,
          'isRegistered': true,
          'isLive': false,
          'hasConflict': false,
        },
        {
          'time': '11:00 - 12:30',
          'title': 'جراحة اللثة التجميلية',
          'speaker': 'د. نورا أحمد سالم',
          'location': 'قاعة المحاضرات B',
          'credits': 1.5,
          'isRegistered': true,
          'isLive': false,
          'hasConflict': false,
        },
      ],
      // Day 2
      [
        {
          'time': '09:00 - 10:30',
          'title': 'المواد السنية المتقدمة',
          'speaker': 'د. سارة محمد قاسم',
          'location': 'قاعة المحاضرات A',
          'credits': 1.5,
          'isRegistered': false,
          'isLive': false,
          'hasConflict': false,
        },
        {
          'time': '11:00 - 12:30',
          'title': 'الختام وتوزيع الشهادات',
          'speaker': 'اللجنة المنظمة',
          'location': 'القاعة الرئيسية',
          'credits': 1.5,
          'isRegistered': true,
          'isLive': false,
          'hasConflict': false,
        },
      ],
    ];
    
    return allSessions[dayIndex];
  }
}
