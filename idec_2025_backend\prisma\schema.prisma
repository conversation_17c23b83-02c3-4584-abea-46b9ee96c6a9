// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String   @id @default(uuid())
  email             String   @unique
  phone             String?  @unique
  passwordHash      String
  
  // Personal Information
  arabicName        String
  englishName       String
  birthDate         DateTime?
  gender            Gender?
  nationality       String?
  
  // Professional Information
  qualification     Qualification
  specialization    String?
  university        String?
  graduationYear    Int?
  licenseNumber     String?
  
  // Profile
  profileImage      String?
  bio               String?
  
  // Account Status
  status            UserStatus @default(PENDING)
  emailVerified     Boolean    @default(false)
  phoneVerified     Boolean    @default(false)
  
  // Timestamps
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  lastLoginAt       DateTime?
  
  // Relations
  subscriptions     Subscription[]
  payments          Payment[]
  sessionAttendance SessionAttendance[]
  courseEnrollments CourseEnrollment[]
  qrCodes           QRCode[]
  notifications     UserNotification[]
  posts             Post[]
  comments          Comment[]
  likes             Like[]
  connections       Connection[] @relation("UserConnections")
  connectedBy       Connection[] @relation("ConnectedUsers")
  
  @@map("users")
}

model Subscription {
  id              String             @id @default(uuid())
  userId          String
  subscriptionType SubscriptionType
  status          SubscriptionStatus @default(PENDING)
  
  // Documents
  documents       Document[]
  
  // Approval Process
  submittedAt     DateTime           @default(now())
  reviewedAt      DateTime?
  reviewedBy      String?
  approvedAt      DateTime?
  approvedBy      String?
  rejectedAt      DateTime?
  rejectedBy      String?
  rejectionReason String?
  
  // Payment
  paymentDeadline DateTime?
  paidAt          DateTime?
  amount          Decimal?
  currency        String             @default("SAR")
  
  // Relations
  user            User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]
  
  @@map("subscriptions")
}

model Document {
  id             String       @id @default(uuid())
  subscriptionId String
  type           DocumentType
  fileName       String
  filePath       String
  fileSize       Int
  mimeType       String
  uploadedAt     DateTime     @default(now())
  
  // Relations
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  
  @@map("documents")
}

// Conference Management
model Session {
  id              String              @id @default(uuid())
  titleAr         String
  titleEn         String
  descriptionAr   String?
  descriptionEn   String?
  
  // Schedule
  startTime       DateTime
  endTime         DateTime
  timezone        String              @default("Asia/Riyadh")
  
  // Location
  roomId          String?
  room            Room?               @relation(fields: [roomId], references: [id])
  
  // Session Details
  sessionType     SessionType
  category        String?
  tags            String[]
  cmeHours        Decimal?
  maxAttendees    Int?
  
  // Content
  abstract        String?
  materials       String[]
  recordingUrl    String?
  
  // Status
  status          SessionStatus       @default(SCHEDULED)
  isLive          Boolean             @default(false)
  
  // Timestamps
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  
  // Relations
  speakers        SessionSpeaker[]
  attendance      SessionAttendance[]
  evaluations     SessionEvaluation[]
  qrCodes         QRCode[]
  
  @@map("sessions")
}

model Speaker {
  id              String           @id @default(uuid())
  arabicName      String
  englishName     String
  bio             String?
  profileImage    String?
  
  // Professional Info
  title           String?
  organization    String?
  specialization  String?
  country         String?
  
  // Contact
  email           String?
  phone           String?
  website         String?
  socialMedia     Json?
  
  // Timestamps
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  
  // Relations
  sessions        SessionSpeaker[]
  
  @@map("speakers")
}

model SessionSpeaker {
  id        String  @id @default(uuid())
  sessionId String
  speakerId String
  role      SpeakerRole @default(SPEAKER)
  order     Int     @default(0)
  
  // Relations
  session   Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  speaker   Speaker @relation(fields: [speakerId], references: [id], onDelete: Cascade)
  
  @@unique([sessionId, speakerId])
  @@map("session_speakers")
}

model Room {
  id          String    @id @default(uuid())
  nameAr      String
  nameEn      String
  capacity    Int
  location    String?
  facilities  String[]
  
  // Relations
  sessions    Session[]
  
  @@map("rooms")
}

model SessionAttendance {
  id           String    @id @default(uuid())
  userId       String
  sessionId    String
  
  // Attendance Details
  checkInTime  DateTime?
  checkOutTime DateTime?
  duration     Int?      // in minutes
  qrCodeId     String?
  
  // Timestamps
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  
  // Relations
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  session      Session   @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  qrCode       QRCode?   @relation(fields: [qrCodeId], references: [id])
  
  @@unique([userId, sessionId])
  @@map("session_attendance")
}

model SessionEvaluation {
  id              String  @id @default(uuid())
  userId          String
  sessionId       String
  
  // Ratings (1-5)
  contentRating   Int
  speakerRating   Int
  organizationRating Int
  overallRating   Int
  
  // Feedback
  comments        String?
  suggestions     String?
  
  // Timestamps
  createdAt       DateTime @default(now())
  
  // Relations
  session         Session  @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, sessionId])
  @@map("session_evaluations")
}

// Course Management
model Course {
  id              String            @id @default(uuid())
  titleAr         String
  titleEn         String
  descriptionAr   String?
  descriptionEn   String?

  // Course Details
  courseType      CourseType
  category        String?
  level           CourseLevel
  duration        Int               // in hours
  maxParticipants Int

  // Schedule
  startDate       DateTime
  endDate         DateTime
  schedule        Json?             // Flexible schedule structure

  // Pricing
  price           Decimal
  currency        String            @default("SAR")
  earlyBirdPrice  Decimal?
  earlyBirdDeadline DateTime?

  // Content
  syllabus        String?
  materials       String[]
  prerequisites   String?

  // Instructor
  instructorId    String?

  // Status
  status          CourseStatus      @default(DRAFT)

  // Timestamps
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  // Relations
  enrollments     CourseEnrollment[]

  @@map("courses")
}

model CourseEnrollment {
  id          String            @id @default(uuid())
  userId      String
  courseId    String
  status      EnrollmentStatus  @default(PENDING)
  
  // Payment
  amount      Decimal
  currency    String            @default("SAR")
  paidAt      DateTime?
  
  // Completion
  completedAt DateTime?
  certificateUrl String?
  
  // Timestamps
  enrolledAt  DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  // Relations
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  course      Course            @relation(fields: [courseId], references: [id], onDelete: Cascade)
  payments    Payment[]
  
  @@unique([userId, courseId])
  @@map("course_enrollments")
}

// Payment Management
model Payment {
  id              String        @id @default(uuid())
  userId          String
  subscriptionId  String?
  enrollmentId    String?
  
  // Payment Details
  amount          Decimal
  currency        String        @default("SAR")
  purpose         PaymentPurpose
  
  // Gateway Information
  gateway         PaymentGateway
  gatewayTransactionId String?
  gatewayResponse Json?
  
  // Status
  status          PaymentStatus @default(PENDING)
  
  // Timestamps
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  paidAt          DateTime?
  
  // Relations
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription    Subscription? @relation(fields: [subscriptionId], references: [id])
  enrollment      CourseEnrollment? @relation(fields: [enrollmentId], references: [id])
  
  @@map("payments")
}

// QR Code Management
model QRCode {
  id          String     @id @default(uuid())
  userId      String?
  sessionId   String?
  type        QRCodeType
  code        String     @unique
  data        Json?
  
  // Validity
  isActive    Boolean    @default(true)
  expiresAt   DateTime?
  usageCount  Int        @default(0)
  maxUsage    Int?
  
  // Timestamps
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Relations
  user        User?      @relation(fields: [userId], references: [id])
  session     Session?   @relation(fields: [sessionId], references: [id])
  attendance  SessionAttendance[]
  
  @@map("qr_codes")
}

// Content Management
model News {
  id          String      @id @default(uuid())
  titleAr     String
  titleEn     String
  contentAr   String
  contentEn   String
  excerpt     String?
  
  // Media
  featuredImage String?
  images      String[]
  
  // Categorization
  category    NewsCategory
  tags        String[]
  priority    NewsPriority @default(NORMAL)
  
  // Publishing
  status      ContentStatus @default(DRAFT)
  publishedAt DateTime?
  
  // Timestamps
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  @@map("news")
}

model LibraryItem {
  id          String        @id @default(uuid())
  titleAr     String
  titleEn     String
  descriptionAr String?
  descriptionEn String?
  
  // Content
  type        LibraryItemType
  filePath    String?
  fileSize    Int?
  mimeType    String?
  url         String?
  
  // Metadata
  author      String?
  publisher   String?
  publishDate DateTime?
  isbn        String?
  doi         String?
  
  // Categorization
  category    String?
  tags        String[]
  
  // Access Control
  accessLevel AccessLevel   @default(PUBLIC)
  
  // Status
  status      ContentStatus @default(DRAFT)
  
  // Timestamps
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  @@map("library_items")
}

// Exhibition Management
model Exhibitor {
  id          String    @id @default(uuid())
  nameAr      String
  nameEn      String
  descriptionAr String?
  descriptionEn String?
  
  // Company Info
  logo        String?
  website     String?
  email       String?
  phone       String?
  country     String?
  
  // Exhibition Details
  boothNumber String?
  category    String?
  products    String[]
  services    String[]
  
  // Special Offers
  hasOffers   Boolean   @default(false)
  offers      Json?
  
  // Status
  status      ExhibitorStatus @default(ACTIVE)
  
  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  @@map("exhibitors")
}

// Social Features
model Post {
  id          String    @id @default(uuid())
  userId      String
  content     String
  images      String[]
  
  // Engagement
  likesCount  Int       @default(0)
  commentsCount Int     @default(0)
  sharesCount Int       @default(0)
  
  // Status
  status      PostStatus @default(PUBLISHED)
  
  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  comments    Comment[]
  likes       Like[]
  
  @@map("posts")
}

model Comment {
  id        String   @id @default(uuid())
  postId    String
  userId    String
  content   String
  
  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("comments")
}

model Like {
  id        String   @id @default(uuid())
  postId    String
  userId    String
  
  // Timestamps
  createdAt DateTime @default(now())
  
  // Relations
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([postId, userId])
  @@map("likes")
}

model Connection {
  id          String          @id @default(uuid())
  requesterId String
  receiverId  String
  status      ConnectionStatus @default(PENDING)
  
  // Timestamps
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  
  // Relations
  requester   User            @relation("UserConnections", fields: [requesterId], references: [id], onDelete: Cascade)
  receiver    User            @relation("ConnectedUsers", fields: [receiverId], references: [id], onDelete: Cascade)
  
  @@unique([requesterId, receiverId])
  @@map("connections")
}

// Notification System
model Notification {
  id          String            @id @default(uuid())
  type        NotificationType
  titleAr     String
  titleEn     String
  contentAr   String
  contentEn   String
  
  // Targeting
  targetType  NotificationTarget
  targetUsers String[]          // User IDs for specific targeting
  
  // Delivery
  channels    NotificationChannel[]
  scheduledAt DateTime?
  sentAt      DateTime?
  
  // Status
  status      NotificationStatus @default(DRAFT)
  
  // Timestamps
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  // Relations
  userNotifications UserNotification[]
  
  @@map("notifications")
}

model UserNotification {
  id             String       @id @default(uuid())
  userId         String
  notificationId String
  
  // Status
  isRead         Boolean      @default(false)
  readAt         DateTime?
  
  // Timestamps
  createdAt      DateTime     @default(now())
  
  // Relations
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  
  @@unique([userId, notificationId])
  @@map("user_notifications")
}

// Enums
enum Gender {
  MALE
  FEMALE
}

enum Qualification {
  STUDENT
  DENTIST
  SPECIALIST
  CONSULTANT
  PROFESSOR
  RESEARCHER
  OTHER
}

enum UserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  BANNED
  INACTIVE
}

enum SubscriptionType {
  STUDENT
  DENTIST
  SPECIALIST
  INTERNATIONAL
  COMPANION
  EXHIBITOR
}

enum SubscriptionStatus {
  PENDING
  DOCUMENT_REVIEW
  APPROVED
  PAYMENT_PENDING
  PAID
  REJECTED
  CANCELLED
}

enum DocumentType {
  ID_COPY
  QUALIFICATION_CERTIFICATE
  LICENSE
  STUDENT_ID
  PASSPORT
  OTHER
}

enum SessionType {
  KEYNOTE
  SCIENTIFIC_SESSION
  WORKSHOP
  PANEL_DISCUSSION
  POSTER_SESSION
  BREAK
  LUNCH
  NETWORKING
}

enum SessionStatus {
  SCHEDULED
  LIVE
  COMPLETED
  CANCELLED
  POSTPONED
}

enum SpeakerRole {
  KEYNOTE_SPEAKER
  SPEAKER
  MODERATOR
  PANELIST
  CHAIRPERSON
}

enum CourseType {
  PRE_CONFERENCE
  DURING_CONFERENCE
  POST_CONFERENCE
  ONLINE
}

enum CourseLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  ALL_LEVELS
}

enum CourseStatus {
  DRAFT
  PUBLISHED
  FULL
  CANCELLED
  COMPLETED
}

enum EnrollmentStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum PaymentPurpose {
  CONFERENCE_REGISTRATION
  COURSE_ENROLLMENT
  ADDITIONAL_SERVICES
}

enum PaymentGateway {
  STRIPE
  PAYPAL
  BANK_TRANSFER
  CASH
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum QRCodeType {
  USER_ENTRY
  SESSION_ATTENDANCE
  COURSE_ATTENDANCE
  EXHIBITION_VISIT
}

enum NewsCategory {
  ANNOUNCEMENT
  SCIENTIFIC_NEWS
  CONFERENCE_UPDATE
  GENERAL
}

enum NewsPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum ContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  DELETED
}

enum LibraryItemType {
  BOOK
  RESEARCH_PAPER
  PRESENTATION
  VIDEO
  AUDIO
  DOCUMENT
  LINK
}

enum AccessLevel {
  PUBLIC
  REGISTERED_USERS
  PAID_USERS
  PREMIUM
  ADMIN_ONLY
}

enum ExhibitorStatus {
  ACTIVE
  INACTIVE
  PENDING_APPROVAL
}

enum PostStatus {
  DRAFT
  PUBLISHED
  HIDDEN
  DELETED
}

enum ConnectionStatus {
  PENDING
  ACCEPTED
  REJECTED
  BLOCKED
}

enum NotificationType {
  SYSTEM
  CONFERENCE
  PAYMENT
  SOCIAL
  REMINDER
  MARKETING
}

enum NotificationTarget {
  ALL_USERS
  SPECIFIC_USERS
  USER_TYPE
  SUBSCRIPTION_TYPE
}

enum NotificationChannel {
  IN_APP
  EMAIL
  SMS
  PUSH
  WHATSAPP
}

enum NotificationStatus {
  DRAFT
  SCHEDULED
  SENT
  FAILED
}
