// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.baseURL = 'http://localhost:3000/api/v1';
        this.token = localStorage.getItem('adminToken');
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDashboardData();
        this.initializeCharts();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link[data-section]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('data-section');
                this.showSection(section);
                this.updateActiveNav(e.target);
            });
        });

        // Auto-refresh data every 30 seconds
        setInterval(() => {
            this.loadDashboardData();
        }, 30000);
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('d-none');
        });

        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.remove('d-none');
        }

        // Load section-specific data
        switch (sectionName) {
            case 'users':
                this.loadUsers();
                break;
            case 'sessions':
                this.loadSessions();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'content':
                this.loadContent();
                break;
        }
    }

    updateActiveNav(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }

    async loadDashboardData() {
        try {
            this.showLoading();
            
            const response = await this.apiCall('/reports/dashboard');
            const data = response.data;

            // Update statistics
            this.updateStatistics(data);
            this.updateRecentActivities();
            this.updateAlerts();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('فشل في تحميل بيانات لوحة التحكم');
        } finally {
            this.hideLoading();
        }
    }

    updateStatistics(data) {
        // Update stat cards
        document.getElementById('total-users').textContent = 
            this.formatNumber(data.users?.total || 0);
        document.getElementById('total-sessions').textContent = 
            this.formatNumber(data.sessions?.total || 0);
        document.getElementById('total-registrations').textContent = 
            this.formatNumber(data.registrations?.total || 0);
        document.getElementById('total-revenue').textContent = 
            this.formatCurrency(data.financial?.totalRevenue || 0);
    }

    updateRecentActivities() {
        const activities = [
            {
                icon: 'fas fa-user-plus',
                iconColor: 'bg-success',
                title: 'مستخدم جديد',
                description: 'انضم د. أحمد محمد إلى المؤتمر',
                time: 'منذ 5 دقائق'
            },
            {
                icon: 'fas fa-calendar-check',
                iconColor: 'bg-primary',
                title: 'جلسة جديدة',
                description: 'تم إضافة جلسة "تقنيات الزراعة الحديثة"',
                time: 'منذ 15 دقيقة'
            },
            {
                icon: 'fas fa-dollar-sign',
                iconColor: 'bg-warning',
                title: 'دفعة جديدة',
                description: 'تم استلام دفعة بقيمة 500 ريال',
                time: 'منذ 30 دقيقة'
            }
        ];

        const activitiesHTML = activities.map(activity => `
            <div class="activity-item d-flex align-items-center">
                <div class="activity-icon ${activity.iconColor} text-white">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content flex-grow-1">
                    <h6>${activity.title}</h6>
                    <p>${activity.description}</p>
                </div>
                <div class="activity-time">
                    ${activity.time}
                </div>
            </div>
        `).join('');

        document.getElementById('recent-activities').innerHTML = activitiesHTML;
    }

    updateAlerts() {
        const alerts = [
            {
                type: 'warning',
                title: 'تنبيه',
                message: 'يوجد 5 تسجيلات في انتظار الموافقة'
            },
            {
                type: 'info',
                title: 'معلومة',
                message: 'ستبدأ جلسة "الذكاء الاصطناعي في طب الأسنان" خلال ساعة'
            }
        ];

        const alertsHTML = alerts.map(alert => `
            <div class="alert-item alert-${alert.type}">
                <h6>${alert.title}</h6>
                <p class="mb-0">${alert.message}</p>
            </div>
        `).join('');

        document.getElementById('alerts').innerHTML = alertsHTML;
    }

    initializeCharts() {
        this.initRegistrationsChart();
        this.initUsersChart();
    }

    initRegistrationsChart() {
        const ctx = document.getElementById('registrationsChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'التسجيلات',
                    data: [12, 19, 25, 32, 28, 35],
                    borderColor: '#DC143C',
                    backgroundColor: 'rgba(220, 20, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    initUsersChart() {
        const ctx = document.getElementById('usersChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['أطباء', 'طلاب', 'باحثين', 'أخرى'],
                datasets: [{
                    data: [45, 25, 20, 10],
                    backgroundColor: [
                        '#DC143C',
                        '#10B981',
                        '#F59E0B',
                        '#6B7280'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    async loadUsers() {
        try {
            const response = await this.apiCall('/admin/users');
            const users = response.data;

            const tableBody = document.querySelector('#users-table tbody');
            tableBody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.arabicName || user.englishName}</td>
                    <td>${user.email}</td>
                    <td><span class="badge badge-info">${this.translateRole(user.role)}</span></td>
                    <td><span class="badge badge-${this.getStatusColor(user.status)}">${this.translateStatus(user.status)}</span></td>
                    <td>${this.formatDate(user.createdAt)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editUser('${user.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteUser('${user.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('Error loading users:', error);
            this.showError('فشل في تحميل بيانات المستخدمين');
        }
    }

    async loadSessions() {
        try {
            const response = await this.apiCall('/sessions');
            const sessions = response.data;

            const tableBody = document.querySelector('#sessions-table tbody');
            tableBody.innerHTML = sessions.map(session => `
                <tr>
                    <td>${session.titleAr || session.titleEn}</td>
                    <td>${session.speaker?.nameAr || session.speaker?.nameEn || 'غير محدد'}</td>
                    <td>${this.formatDateTime(session.startTime)}</td>
                    <td>${session.room?.nameAr || session.room?.nameEn || 'غير محدد'}</td>
                    <td><span class="badge badge-${this.getStatusColor(session.status)}">${this.translateStatus(session.status)}</span></td>
                    <td>${session.attendanceCount || 0}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editSession('${session.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="viewAttendance('${session.id}')">
                            <i class="fas fa-users"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('Error loading sessions:', error);
            this.showError('فشل في تحميل بيانات الجلسات');
        }
    }

    async loadReports() {
        // Reports are generated on demand
        console.log('Reports section loaded');
    }

    async loadContent() {
        // Load news, library, and exhibitors
        this.loadNews();
        this.loadLibrary();
        this.loadExhibitors();
    }

    async loadNews() {
        try {
            const response = await this.apiCall('/news');
            const news = response.data;

            const newsList = document.getElementById('news-list');
            newsList.innerHTML = news.map(item => `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6>${item.titleAr || item.titleEn}</h6>
                                <p class="text-muted mb-2">${item.summary || ''}</p>
                                <small class="text-muted">${this.formatDate(item.createdAt)}</small>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-primary me-1" onclick="editNews('${item.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteNews('${item.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('Error loading news:', error);
        }
    }

    async loadLibrary() {
        // Similar implementation for library items
        console.log('Loading library items...');
    }

    async loadExhibitors() {
        // Similar implementation for exhibitors
        console.log('Loading exhibitors...');
    }

    // Utility methods
    async apiCall(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            ...options
        };

        const response = await fetch(url, config);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    }

    formatDate(dateString) {
        return new Intl.DateTimeFormat('ar-SA').format(new Date(dateString));
    }

    formatDateTime(dateString) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(dateString));
    }

    translateRole(role) {
        const roles = {
            'SUPER_ADMIN': 'مدير عام',
            'ADMIN': 'مدير',
            'MODERATOR': 'مشرف',
            'SPEAKER': 'متحدث',
            'ATTENDEE': 'حاضر'
        };
        return roles[role] || role;
    }

    translateStatus(status) {
        const statuses = {
            'ACTIVE': 'نشط',
            'INACTIVE': 'غير نشط',
            'PENDING': 'في الانتظار',
            'APPROVED': 'موافق عليه',
            'REJECTED': 'مرفوض',
            'COMPLETED': 'مكتمل'
        };
        return statuses[status] || status;
    }

    getStatusColor(status) {
        const colors = {
            'ACTIVE': 'success',
            'INACTIVE': 'secondary',
            'PENDING': 'warning',
            'APPROVED': 'success',
            'REJECTED': 'danger',
            'COMPLETED': 'info'
        };
        return colors[status] || 'secondary';
    }

    showLoading() {
        document.getElementById('loading-overlay').classList.remove('d-none');
    }

    hideLoading() {
        document.getElementById('loading-overlay').classList.add('d-none');
    }

    showError(message) {
        // Show error toast or alert
        console.error(message);
    }

    showSuccess(message) {
        // Show success toast or alert
        console.log(message);
    }
}

// Global functions for button actions
function editUser(userId) {
    console.log('Edit user:', userId);
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        console.log('Delete user:', userId);
    }
}

function editSession(sessionId) {
    console.log('Edit session:', sessionId);
}

function viewAttendance(sessionId) {
    console.log('View attendance for session:', sessionId);
}

function addUser() {
    console.log('Add new user');
}

function addSession() {
    console.log('Add new session');
}

function addNews() {
    console.log('Add news');
}

function addLibraryItem() {
    console.log('Add library item');
}

function addExhibitor() {
    console.log('Add exhibitor');
}

function exportUsers() {
    console.log('Export users');
}

function exportSessions() {
    console.log('Export sessions');
}

function generateUserReport() {
    console.log('Generate user report');
}

function generateFinancialReport() {
    console.log('Generate financial report');
}

function generateAttendanceReport() {
    console.log('Generate attendance report');
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('adminToken');
        window.location.href = 'login.html';
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminDashboard();
});
