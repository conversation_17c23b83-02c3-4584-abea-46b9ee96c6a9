const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');
const gamificationService = require('./gamificationService');

const prisma = new PrismaClient();

class LibraryService {
  // Get all categories with hierarchy
  async getCategories(includeInactive = false) {
    try {
      const where = includeInactive ? {} : { isActive: true };

      const categories = await prisma.libraryCategory.findMany({
        where,
        include: {
          children: {
            where: includeInactive ? {} : { isActive: true },
            orderBy: { sortOrder: 'asc' }
          },
          _count: {
            select: { items: true }
          }
        },
        orderBy: { sortOrder: 'asc' }
      });

      // Build hierarchy (root categories only)
      const rootCategories = categories.filter(cat => !cat.parentId);
      return rootCategories;
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  // Get library items with filters
  async getLibraryItems(filters = {}, pagination = {}) {
    try {
      const { page = 1, limit = 20 } = pagination;
      const { 
        categoryId, 
        type, 
        search, 
        tags, 
        author, 
        isFeatured, 
        isPublic = true,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = filters;

      const where = { isActive: true, isPublic };

      if (categoryId) {
        where.categoryId = categoryId;
      }

      if (type) {
        where.type = type;
      }

      if (isFeatured !== undefined) {
        where.isFeatured = isFeatured;
      }

      if (author) {
        where.OR = [
          { author: { contains: author, mode: 'insensitive' } },
          { authorAr: { contains: author, mode: 'insensitive' } },
          { authorEn: { contains: author, mode: 'insensitive' } }
        ];
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { titleAr: { contains: search, mode: 'insensitive' } },
          { titleEn: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } }
        ];
      }

      if (tags && tags.length > 0) {
        where.tags = {
          hasSome: Array.isArray(tags) ? tags : [tags]
        };
      }

      const orderBy = {};
      orderBy[sortBy] = sortOrder;

      const [items, total] = await Promise.all([
        prisma.libraryItem.findMany({
          where,
          include: {
            category: {
              select: {
                id: true,
                nameAr: true,
                nameEn: true
              }
            },
            _count: {
              select: {
                ratings: true,
                bookmarks: true,
                views: true
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy
        }),
        prisma.libraryItem.count({ where })
      ]);

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting library items:', error);
      throw error;
    }
  }

  // Get library item by ID
  async getLibraryItemById(id, userId = null) {
    try {
      const item = await prisma.libraryItem.findUnique({
        where: { id },
        include: {
          category: true,
          ratings: {
            include: {
              user: {
                select: {
                  id: true,
                  arabicName: true,
                  englishName: true
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 10
          },
          _count: {
            select: {
              ratings: true,
              bookmarks: true,
              views: true
            }
          }
        }
      });

      if (!item) {
        throw new Error('Library item not found');
      }

      // Check if user has bookmarked this item
      let isBookmarked = false;
      let userRating = null;

      if (userId) {
        const [bookmark, rating] = await Promise.all([
          prisma.libraryBookmark.findUnique({
            where: {
              userId_itemId: {
                userId,
                itemId: id
              }
            }
          }),
          prisma.libraryRating.findUnique({
            where: {
              userId_itemId: {
                userId,
                itemId: id
              }
            }
          })
        ]);

        isBookmarked = !!bookmark;
        userRating = rating?.rating || null;
      }

      return {
        ...item,
        isBookmarked,
        userRating
      };
    } catch (error) {
      logger.error('Error getting library item by ID:', error);
      throw error;
    }
  }

  // Track view for library item
  async trackView(itemId, userId = null, ipAddress = null, userAgent = null) {
    try {
      // Create view record
      await prisma.libraryView.create({
        data: {
          itemId,
          userId,
          ipAddress,
          userAgent
        }
      });

      // Update view count
      await prisma.libraryItem.update({
        where: { id: itemId },
        data: {
          viewCount: {
            increment: 1
          }
        }
      });

      // Award points for viewing content (if user is logged in)
      if (userId) {
        await gamificationService.awardPoints(
          userId,
          'SOCIAL_INTERACTION',
          2,
          'Viewed library content'
        );
      }

      logger.info(`View tracked for library item ${itemId}`);
    } catch (error) {
      logger.error('Error tracking view:', error);
      throw error;
    }
  }

  // Rate library item
  async rateLibraryItem(itemId, userId, rating, comment = null) {
    try {
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      // Create or update rating
      const libraryRating = await prisma.libraryRating.upsert({
        where: {
          userId_itemId: {
            userId,
            itemId
          }
        },
        update: {
          rating,
          comment
        },
        create: {
          userId,
          itemId,
          rating,
          comment
        }
      });

      // Recalculate average rating
      const ratings = await prisma.libraryRating.findMany({
        where: { itemId },
        select: { rating: true }
      });

      const averageRating = ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length;

      await prisma.libraryItem.update({
        where: { id: itemId },
        data: {
          rating: averageRating,
          ratingCount: ratings.length
        }
      });

      // Award points for rating
      await gamificationService.awardPoints(
        userId,
        'RATING_GIVEN',
        5,
        'Rated library content'
      );

      logger.info(`User ${userId} rated library item ${itemId} with ${rating} stars`);
      return libraryRating;
    } catch (error) {
      logger.error('Error rating library item:', error);
      throw error;
    }
  }

  // Bookmark library item
  async bookmarkLibraryItem(itemId, userId) {
    try {
      const bookmark = await prisma.libraryBookmark.create({
        data: {
          userId,
          itemId
        }
      });

      logger.info(`User ${userId} bookmarked library item ${itemId}`);
      return bookmark;
    } catch (error) {
      if (error.code === 'P2002') {
        throw new Error('Item already bookmarked');
      }
      logger.error('Error bookmarking library item:', error);
      throw error;
    }
  }

  // Remove bookmark
  async removeBookmark(itemId, userId) {
    try {
      await prisma.libraryBookmark.delete({
        where: {
          userId_itemId: {
            userId,
            itemId
          }
        }
      });

      logger.info(`User ${userId} removed bookmark for library item ${itemId}`);
    } catch (error) {
      logger.error('Error removing bookmark:', error);
      throw error;
    }
  }

  // Get user's bookmarks
  async getUserBookmarks(userId, pagination = {}) {
    try {
      const { page = 1, limit = 20 } = pagination;

      const [bookmarks, total] = await Promise.all([
        prisma.libraryBookmark.findMany({
          where: { userId },
          include: {
            item: {
              include: {
                category: {
                  select: {
                    nameAr: true,
                    nameEn: true
                  }
                }
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { createdAt: 'desc' }
        }),
        prisma.libraryBookmark.count({ where: { userId } })
      ]);

      return {
        bookmarks,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting user bookmarks:', error);
      throw error;
    }
  }

  // Add note to library item
  async addNote(itemId, userId, content, isPrivate = true) {
    try {
      const note = await prisma.libraryNote.create({
        data: {
          itemId,
          userId,
          content,
          isPrivate
        }
      });

      logger.info(`User ${userId} added note to library item ${itemId}`);
      return note;
    } catch (error) {
      logger.error('Error adding note:', error);
      throw error;
    }
  }

  // Get user's notes for an item
  async getUserNotes(itemId, userId) {
    try {
      const notes = await prisma.libraryNote.findMany({
        where: {
          itemId,
          userId
        },
        orderBy: { createdAt: 'desc' }
      });

      return notes;
    } catch (error) {
      logger.error('Error getting user notes:', error);
      throw error;
    }
  }

  // Search library
  async searchLibrary(query, filters = {}) {
    try {
      const { type, categoryId, tags } = filters;

      const where = {
        isActive: true,
        isPublic: true,
        OR: [
          { title: { contains: query, mode: 'insensitive' } },
          { titleAr: { contains: query, mode: 'insensitive' } },
          { titleEn: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
          { content: { contains: query, mode: 'insensitive' } },
          { author: { contains: query, mode: 'insensitive' } }
        ]
      };

      if (type) {
        where.type = type;
      }

      if (categoryId) {
        where.categoryId = categoryId;
      }

      if (tags && tags.length > 0) {
        where.tags = {
          hasSome: Array.isArray(tags) ? tags : [tags]
        };
      }

      const items = await prisma.libraryItem.findMany({
        where,
        include: {
          category: {
            select: {
              nameAr: true,
              nameEn: true
            }
          }
        },
        take: 50,
        orderBy: [
          { isFeatured: 'desc' },
          { viewCount: 'desc' },
          { rating: 'desc' }
        ]
      });

      return items;
    } catch (error) {
      logger.error('Error searching library:', error);
      throw error;
    }
  }

  // Get library statistics
  async getLibraryStats() {
    try {
      const [
        totalItems,
        totalCategories,
        totalViews,
        totalBookmarks,
        totalRatings,
        typeStats,
        popularItems
      ] = await Promise.all([
        prisma.libraryItem.count({ where: { isActive: true } }),
        prisma.libraryCategory.count({ where: { isActive: true } }),
        prisma.libraryView.count(),
        prisma.libraryBookmark.count(),
        prisma.libraryRating.count(),
        prisma.libraryItem.groupBy({
          by: ['type'],
          where: { isActive: true },
          _count: { type: true }
        }),
        prisma.libraryItem.findMany({
          where: { isActive: true },
          orderBy: { viewCount: 'desc' },
          take: 10,
          select: {
            id: true,
            title: true,
            titleEn: true,
            viewCount: true,
            rating: true
          }
        })
      ]);

      return {
        totalItems,
        totalCategories,
        totalViews,
        totalBookmarks,
        totalRatings,
        typeStats,
        popularItems
      };
    } catch (error) {
      logger.error('Error getting library stats:', error);
      throw error;
    }
  }
}

module.exports = new LibraryService();
