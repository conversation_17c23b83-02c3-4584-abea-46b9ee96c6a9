import 'package:flutter/foundation.dart';
import '../services/offline_service.dart';
import '../services/api_service.dart';
import '../models/session_model.dart';
import '../models/user_model.dart';

class OfflineProvider extends ChangeNotifier {
  final OfflineService _offlineService = OfflineService();
  final ApiService _apiService = ApiService();

  bool _isOfflineMode = false;
  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  String? _error;
  Map<String, dynamic> _cacheInfo = {};

  // Getters
  bool get isOfflineMode => _isOfflineMode;
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get error => _error;
  Map<String, dynamic> get cacheInfo => _cacheInfo;
  
  bool get hasOfflineData => _cacheInfo.isNotEmpty && 
      (_cacheInfo['sessions'] ?? 0) > 0;
  
  bool get isDataStale => _offlineService.isDataStale();

  // Initialize offline provider
  Future<void> initialize() async {
    try {
      await _offlineService.initialize();
      _isOfflineMode = _offlineService.isOfflineMode;
      _lastSyncTime = _offlineService.lastSyncTime;
      await _updateCacheInfo();
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Enable offline mode
  void enableOfflineMode() {
    _isOfflineMode = true;
    _offlineService.enableOfflineMode();
    notifyListeners();
  }

  // Disable offline mode
  void disableOfflineMode() {
    _isOfflineMode = false;
    _offlineService.disableOfflineMode();
    notifyListeners();
  }

  // Sync data for offline use
  Future<bool> syncDataForOffline() async {
    if (_isSyncing) return false;

    _isSyncing = true;
    _error = null;
    notifyListeners();

    try {
      // Fetch all required data from API
      final [
        sessionsResponse,
        userResponse,
        speakersResponse,
        newsResponse,
        exhibitorsResponse,
        libraryResponse,
      ] = await Future.wait([
        _apiService.getSessions(),
        _apiService.getUserProfile(),
        _apiService.getSpeakers(),
        _apiService.getNews(),
        _apiService.getExhibitors(),
        _apiService.getLibraryItems(),
      ]);

      // Parse data
      final sessions = (sessionsResponse['data'] as List)
          .map((json) => Session.fromJson(json))
          .toList();
      
      final user = User.fromJson(userResponse['data']);
      
      final speakers = (speakersResponse['data'] as List)
          .map((json) => Speaker.fromJson(json))
          .toList();
      
      final news = (newsResponse['data'] as List)
          .cast<Map<String, dynamic>>();
      
      final exhibitors = (exhibitorsResponse['data'] as List)
          .cast<Map<String, dynamic>>();
      
      final library = (libraryResponse['data'] as List)
          .cast<Map<String, dynamic>>();

      // Sync all data to offline storage
      await _offlineService.syncAllData(
        sessions: sessions,
        user: user,
        speakers: speakers,
        news: news,
        exhibitors: exhibitors,
        library: library,
      );

      _lastSyncTime = DateTime.now();
      await _updateCacheInfo();
      
      debugPrint('Successfully synced data for offline use');
      return true;
    } catch (e) {
      _error = e.toString();
      debugPrint('Error syncing data for offline: $e');
      return false;
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  // Get offline sessions
  Future<List<Session>> getOfflineSessions() async {
    try {
      return await _offlineService.getCachedSessions();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get offline user
  Future<User?> getOfflineUser() async {
    try {
      return await _offlineService.getCachedUser();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Get offline speakers
  Future<List<Speaker>> getOfflineSpeakers() async {
    try {
      return await _offlineService.getCachedSpeakers();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get offline news
  Future<List<Map<String, dynamic>>> getOfflineNews() async {
    try {
      return await _offlineService.getCachedNews();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get offline exhibitors
  Future<List<Map<String, dynamic>>> getOfflineExhibitors() async {
    try {
      return await _offlineService.getCachedExhibitors();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get offline library
  Future<List<Map<String, dynamic>>> getOfflineLibrary() async {
    try {
      return await _offlineService.getCachedLibrary();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Clear offline cache
  Future<void> clearOfflineCache() async {
    try {
      await _offlineService.clearCache();
      _lastSyncTime = null;
      await _updateCacheInfo();
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Update cache info
  Future<void> _updateCacheInfo() async {
    try {
      _cacheInfo = await _offlineService.getCacheInfo();
    } catch (e) {
      _cacheInfo = {};
    }
  }

  // Check network and auto-sync if needed
  Future<void> checkAndAutoSync() async {
    if (_isSyncing) return;

    try {
      // Check if data is stale and needs refresh
      if (isDataStale || !hasOfflineData) {
        await syncDataForOffline();
      }
    } catch (e) {
      debugPrint('Auto-sync failed: $e');
    }
  }

  // Get cache size in MB (estimated)
  double get cacheSizeMB {
    if (_cacheInfo.isEmpty) return 0.0;
    
    final totalItems = (_cacheInfo['sessions'] ?? 0) +
                      (_cacheInfo['speakers'] ?? 0) +
                      (_cacheInfo['news'] ?? 0) +
                      (_cacheInfo['exhibitors'] ?? 0) +
                      (_cacheInfo['library'] ?? 0);
    
    // Rough estimation: 1KB per item on average
    return (totalItems * 1024) / (1024 * 1024);
  }

  // Get sync status message
  String get syncStatusMessage {
    if (_isSyncing) return 'جاري المزامنة...';
    if (_error != null) return 'خطأ في المزامنة';
    if (!hasOfflineData) return 'لا توجد بيانات محفوظة';
    if (isDataStale) return 'البيانات قديمة - تحتاج تحديث';
    return 'البيانات محدثة';
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}

// Offline status enum
enum OfflineStatus {
  online,
  offline,
  syncing,
  error,
}

// Offline data info
class OfflineDataInfo {
  final int sessionsCount;
  final int speakersCount;
  final int newsCount;
  final int exhibitorsCount;
  final int libraryCount;
  final DateTime? lastSync;
  final bool isStale;
  final double sizeMB;

  OfflineDataInfo({
    required this.sessionsCount,
    required this.speakersCount,
    required this.newsCount,
    required this.exhibitorsCount,
    required this.libraryCount,
    this.lastSync,
    required this.isStale,
    required this.sizeMB,
  });

  int get totalItems => sessionsCount + speakersCount + newsCount + 
                       exhibitorsCount + libraryCount;

  bool get hasData => totalItems > 0;
}
