const { query, param, body } = require('express-validator');
const { asyncHandler } = require('../middleware/errorHandler');
const { handleValidationErrors, pagination } = require('../middleware/validation');
const conferenceService = require('../services/conferenceService');
const logger = require('../utils/logger');

// Validation rules
const getSessionsValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('رقم الصفحة يجب أن يكون رقم صحيح'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('حد العرض يجب أن يكون بين 1 و 100'),
  query('type').optional().isIn(['LECTURE', 'WORKSHOP', 'PRESENTATION', 'POSTER_SESSION', 'PANEL_DISCUSSION']).withMessage('نوع الجلسة غير صحيح'),
  query('date').optional().isISO8601().withMessage('تاريخ غير صحيح'),
  query('speakerId').optional().isString().withMessage('معرف المتحدث غير صحيح'),
  handleValidationErrors
];

const sessionIdValidation = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  handleValidationErrors
];

const speakerIdValidation = [
  param('id').notEmpty().withMessage('معرف المتحدث مطلوب'),
  handleValidationErrors
];

const rateSessionValidation = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  body('rating').isInt({ min: 1, max: 5 }).withMessage('التقييم يجب أن يكون بين 1 و 5'),
  body('comment').optional().isLength({ max: 500 }).withMessage('التعليق يجب أن يكون أقل من 500 حرف'),
  handleValidationErrors
];

const checkInValidation = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  body('qrCode').notEmpty().withMessage('رمز QR مطلوب'),
  handleValidationErrors
];

const checkOutValidation = [
  param('id').notEmpty().withMessage('معرف الجلسة مطلوب'),
  handleValidationErrors
];

// Get all sessions
const getSessions = [
  getSessionsValidation,
  pagination,
  asyncHandler(async (req, res) => {
    const filters = {
      type: req.query.type,
      date: req.query.date,
      speakerId: req.query.speakerId
    };

    const result = await conferenceService.getSessions(filters, req.pagination);

    logger.info('Sessions requested', { 
      filters, 
      pagination: req.pagination,
      resultCount: result.sessions.length 
    });

    req.paginationResult = result.pagination;

    res.json({
      success: true,
      data: {
        sessions: result.sessions,
        filters: filters
      }
    });
  })
];

// Get session details
const getSessionById = [
  sessionIdValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const session = await conferenceService.getSessionById(id);

    logger.info('Session details requested', { sessionId: id });

    res.json({
      success: true,
      data: {
        session
      }
    });
  })
];

// Get all speakers
const getSpeakers = asyncHandler(async (req, res) => {
  const speakers = await conferenceService.getSpeakers();

  logger.info('Speakers list requested', { count: speakers.length });

  res.json({
    success: true,
    data: {
      speakers
    }
  });
});

// Get speaker details
const getSpeakerById = [
  speakerIdValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    const speaker = await conferenceService.getSpeakerById(id);

    logger.info('Speaker details requested', { speakerId: id });

    res.json({
      success: true,
      data: {
        speaker
      }
    });
  })
];

// Register attendance for a session
const registerAttendance = [
  sessionIdValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    const attendance = await conferenceService.registerAttendance(id, userId);

    res.json({
      success: true,
      message: 'تم تسجيل الحضور بنجاح',
      data: {
        attendance: {
          id: attendance.id,
          sessionId: id,
          registeredAt: attendance.registeredAt,
          session: attendance.session
        }
      }
    });
  })
];

// Get user's personal agenda
const getPersonalAgenda = asyncHandler(async (req, res) => {
  const userId = req.user.id;

  // TODO: Implement get personal agenda from database
  logger.logUserAction(userId, 'PERSONAL_AGENDA_VIEWED');

  res.json({
    success: true,
    data: {
      agenda: [],
      message: 'Personal agenda endpoint - Coming soon'
    }
  });
});

// Add session to personal agenda
const addToAgenda = asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { sessionId } = req.body;

  if (!sessionId) {
    return res.status(400).json({
      success: false,
      message: 'معرف الجلسة مطلوب'
    });
  }

  // TODO: Implement add to agenda logic
  logger.logUserAction(userId, 'SESSION_ADDED_TO_AGENDA', { sessionId });

  res.json({
    success: true,
    message: 'تم إضافة الجلسة إلى جدولك الشخصي',
    data: {
      sessionId,
      addedAt: new Date().toISOString()
    }
  });
});

// Remove session from personal agenda
const removeFromAgenda = [
  param('sessionId').notEmpty().withMessage('معرف الجلسة مطلوب'),
  handleValidationErrors,
  asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const { sessionId } = req.params;

    // TODO: Implement remove from agenda logic
    logger.logUserAction(userId, 'SESSION_REMOVED_FROM_AGENDA', { sessionId });

    res.json({
      success: true,
      message: 'تم حذف الجلسة من جدولك الشخصي'
    });
  })
];

// Rate a session
const rateSession = [
  rateSessionValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;
    const { rating, comment } = req.body;

    // TODO: Implement session rating logic
    logger.logUserAction(userId, 'SESSION_RATED', { 
      sessionId: id, 
      rating, 
      hasComment: !!comment 
    });

    res.json({
      success: true,
      message: 'تم تقييم الجلسة بنجاح',
      data: {
        sessionId: id,
        rating,
        comment,
        ratedAt: new Date().toISOString()
      }
    });
  })
];

// Get QR code for session attendance
const getSessionQR = [
  sessionIdValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const userId = req.user.id;

    // Verify session exists
    await conferenceService.getSessionById(id);

    // Generate QR code data
    const qrData = {
      type: 'SESSION_ATTENDANCE',
      sessionId: id,
      userId: userId,
      timestamp: Date.now(),
      version: '1.0'
    };

    // TODO: Generate actual QR code image
    const qrCodeBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    logger.logUserAction(userId, 'SESSION_QR_GENERATED', { sessionId: id });

    res.json({
      success: true,
      data: {
        qrCode: qrCodeBase64,
        qrData: JSON.stringify(qrData),
        sessionId: id,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      }
    });
  })
];

// Get session statistics (for speakers/admins)
const getSessionStats = [
  sessionIdValidation,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    // TODO: Implement session statistics
    logger.info('Session statistics requested', { sessionId: id });

    res.json({
      success: true,
      data: {
        sessionId: id,
        totalRegistrations: 245,
        actualAttendance: 220,
        averageRating: 4.5,
        totalRatings: 180,
        message: 'Session statistics endpoint - Coming soon'
      }
    });
  })
];

// Search sessions
const searchSessions = [
  query('q').notEmpty().withMessage('كلمة البحث مطلوبة'),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  handleValidationErrors,
  pagination,
  asyncHandler(async (req, res) => {
    const { q: searchQuery } = req.query;

    // TODO: Implement session search
    logger.info('Session search requested', { query: searchQuery });

    res.json({
      success: true,
      data: {
        sessions: [],
        searchQuery,
        message: 'Session search endpoint - Coming soon'
      }
    });
  })
];

// Check-in to session with QR code
const checkInToSession = [
  checkInValidation,
  asyncHandler(async (req, res) => {
    const { id: sessionId } = req.params;
    const { qrCode } = req.body;
    const userId = req.user.id;

    const attendance = await conferenceService.checkInToSession(sessionId, userId, qrCode);

    logger.info('User checked in to session', {
      userId,
      sessionId,
      sessionTitle: attendance.session.titleEn
    });

    res.json({
      success: true,
      message: 'تم تسجيل الحضور بنجاح',
      data: attendance
    });
  })
];

// Check-out from session
const checkOutFromSession = [
  checkOutValidation,
  asyncHandler(async (req, res) => {
    const { id: sessionId } = req.params;
    const userId = req.user.id;

    const attendance = await conferenceService.checkOutFromSession(sessionId, userId);

    logger.info('User checked out from session', {
      userId,
      sessionId,
      duration: attendance.attendanceDuration
    });

    res.json({
      success: true,
      message: 'تم تسجيل الانصراف بنجاح',
      data: attendance
    });
  })
];

module.exports = {
  getSessions,
  getSessionById,
  getSpeakers,
  getSpeakerById,
  registerAttendance,
  getPersonalAgenda,
  addToAgenda,
  removeFromAgenda,
  rateSession,
  getSessionQR,
  getSessionStats,
  searchSessions,
  checkInToSession,
  checkOutFromSession
};
