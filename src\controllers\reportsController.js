const { PrismaClient } = require('@prisma/client');
const logger = require('../utils/logger');

const prisma = new PrismaClient();

class ReportsController {
  // Get comprehensive dashboard statistics
  async getDashboardStats(req, res) {
    try {
      const [
        // User statistics
        totalUsers,
        activeUsers,
        newUsersThisMonth,
        usersByRole,
        
        // Registration statistics
        totalRegistrations,
        approvedRegistrations,
        pendingRegistrations,
        registrationsByCategory,
        
        // Session statistics
        totalSessions,
        completedSessions,
        sessionAttendance,
        
        // Financial statistics
        totalRevenue,
        paidRegistrations,
        pendingPayments,
        
        // Engagement statistics
        totalPoints,
        totalBadges,
        socialConnections,
        libraryViews,
        
        // Exhibition statistics
        totalExhibitors,
        boothVisits,
        
        // Polling statistics
        totalPolls,
        totalQuestions
      ] = await Promise.all([
        // User stats
        prisma.user.count(),
        prisma.user.count({ where: { status: 'ACTIVE' } }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        }),
        prisma.user.groupBy({
          by: ['role'],
          _count: { role: true }
        }),
        
        // Registration stats
        prisma.registration.count(),
        prisma.registration.count({ where: { status: 'APPROVED' } }),
        prisma.registration.count({ where: { status: 'PENDING' } }),
        prisma.registration.groupBy({
          by: ['category'],
          _count: { category: true }
        }),
        
        // Session stats
        prisma.session.count(),
        prisma.session.count({ where: { status: 'COMPLETED' } }),
        prisma.sessionAttendance.count(),
        
        // Financial stats
        prisma.payment.aggregate({
          where: { status: 'COMPLETED' },
          _sum: { amount: true }
        }),
        prisma.registration.count({
          where: { paymentStatus: 'PAID' }
        }),
        prisma.registration.count({
          where: { paymentStatus: 'PENDING' }
        }),
        
        // Engagement stats
        prisma.userPoint.aggregate({
          _sum: { points: true }
        }),
        prisma.userBadge.count(),
        prisma.connection.count({ where: { status: 'ACCEPTED' } }),
        prisma.libraryView.count(),
        
        // Exhibition stats
        prisma.exhibitor.count({ where: { isActive: true } }),
        prisma.boothVisit.count(),
        
        // Polling stats
        prisma.poll.count(),
        prisma.question.count()
      ]);

      const stats = {
        users: {
          total: totalUsers,
          active: activeUsers,
          newThisMonth: newUsersThisMonth,
          byRole: usersByRole
        },
        registrations: {
          total: totalRegistrations,
          approved: approvedRegistrations,
          pending: pendingRegistrations,
          byCategory: registrationsByCategory
        },
        sessions: {
          total: totalSessions,
          completed: completedSessions,
          totalAttendance: sessionAttendance
        },
        financial: {
          totalRevenue: totalRevenue._sum.amount || 0,
          paidRegistrations,
          pendingPayments
        },
        engagement: {
          totalPoints: totalPoints._sum.points || 0,
          totalBadges,
          socialConnections,
          libraryViews
        },
        exhibition: {
          totalExhibitors,
          boothVisits
        },
        polling: {
          totalPolls,
          totalQuestions
        }
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get dashboard statistics',
          code: 'GET_DASHBOARD_STATS_ERROR'
        }
      });
    }
  }

  // Get user engagement report
  async getUserEngagementReport(req, res) {
    try {
      const { startDate, endDate, limit = 100 } = req.query;

      const dateFilter = {};
      if (startDate) dateFilter.gte = new Date(startDate);
      if (endDate) dateFilter.lte = new Date(endDate);

      const whereClause = Object.keys(dateFilter).length > 0 
        ? { createdAt: dateFilter }
        : {};

      const [
        topPointEarners,
        mostActiveUsers,
        socialNetworkers,
        libraryUsers
      ] = await Promise.all([
        // Top point earners
        prisma.userPoint.groupBy({
          by: ['userId'],
          where: whereClause,
          _sum: { points: true },
          orderBy: { _sum: { points: 'desc' } },
          take: parseInt(limit)
        }).then(results => 
          Promise.all(results.map(async (result) => {
            const user = await prisma.user.findUnique({
              where: { id: result.userId },
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                qualification: true
              }
            });
            return {
              user,
              totalPoints: result._sum.points
            };
          }))
        ),

        // Most active users (by session attendance)
        prisma.sessionAttendance.groupBy({
          by: ['userId'],
          where: whereClause,
          _count: { userId: true },
          orderBy: { _count: { userId: 'desc' } },
          take: parseInt(limit)
        }).then(results => 
          Promise.all(results.map(async (result) => {
            const user = await prisma.user.findUnique({
              where: { id: result.userId },
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                qualification: true
              }
            });
            return {
              user,
              sessionsAttended: result._count.userId
            };
          }))
        ),

        // Top social networkers
        prisma.connection.groupBy({
          by: ['requesterId'],
          where: {
            status: 'ACCEPTED',
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
          },
          _count: { requesterId: true },
          orderBy: { _count: { requesterId: 'desc' } },
          take: parseInt(limit)
        }).then(results => 
          Promise.all(results.map(async (result) => {
            const user = await prisma.user.findUnique({
              where: { id: result.requesterId },
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                qualification: true
              }
            });
            return {
              user,
              connections: result._count.requesterId
            };
          }))
        ),

        // Library content viewers
        prisma.libraryView.groupBy({
          by: ['userId'],
          where: {
            userId: { not: null },
            ...(Object.keys(dateFilter).length > 0 && { viewedAt: dateFilter })
          },
          _count: { userId: true },
          orderBy: { _count: { userId: 'desc' } },
          take: parseInt(limit)
        }).then(results => 
          Promise.all(results.map(async (result) => {
            const user = await prisma.user.findUnique({
              where: { id: result.userId },
              select: {
                id: true,
                arabicName: true,
                englishName: true,
                qualification: true
              }
            });
            return {
              user,
              libraryViews: result._count.userId
            };
          }))
        )
      ]);

      res.json({
        success: true,
        data: {
          topPointEarners,
          mostActiveUsers,
          socialNetworkers,
          libraryUsers
        }
      });
    } catch (error) {
      logger.error('Error getting user engagement report:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get user engagement report',
          code: 'GET_ENGAGEMENT_REPORT_ERROR'
        }
      });
    }
  }

  // Get financial report
  async getFinancialReport(req, res) {
    try {
      const { startDate, endDate, groupBy = 'day' } = req.query;

      const dateFilter = {};
      if (startDate) dateFilter.gte = new Date(startDate);
      if (endDate) dateFilter.lte = new Date(endDate);

      const [
        revenueByPeriod,
        paymentsByStatus,
        registrationsByCategory,
        refunds
      ] = await Promise.all([
        // Revenue by time period
        prisma.payment.findMany({
          where: {
            status: 'COMPLETED',
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
          },
          select: {
            amount: true,
            currency: true,
            createdAt: true
          },
          orderBy: { createdAt: 'asc' }
        }),

        // Payments by status
        prisma.payment.groupBy({
          by: ['status'],
          where: Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {},
          _count: { status: true },
          _sum: { amount: true }
        }),

        // Registrations by category with revenue
        prisma.registration.groupBy({
          by: ['category'],
          where: {
            paymentStatus: 'PAID',
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
          },
          _count: { category: true }
        }),

        // Refunds
        prisma.payment.findMany({
          where: {
            status: 'REFUNDED',
            ...(Object.keys(dateFilter).length > 0 && { createdAt: dateFilter })
          },
          select: {
            amount: true,
            currency: true,
            createdAt: true,
            registration: {
              select: {
                category: true,
                user: {
                  select: {
                    arabicName: true,
                    englishName: true
                  }
                }
              }
            }
          }
        })
      ]);

      res.json({
        success: true,
        data: {
          revenueByPeriod,
          paymentsByStatus,
          registrationsByCategory,
          refunds
        }
      });
    } catch (error) {
      logger.error('Error getting financial report:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get financial report',
          code: 'GET_FINANCIAL_REPORT_ERROR'
        }
      });
    }
  }

  // Get session analytics
  async getSessionAnalytics(req, res) {
    try {
      const [
        sessionAttendanceStats,
        popularSessions,
        sessionRatings,
        speakerStats
      ] = await Promise.all([
        // Session attendance statistics
        prisma.session.findMany({
          include: {
            _count: {
              select: { attendance: true }
            },
            attendance: {
              select: {
                checkInTime: true,
                checkOutTime: true,
                attendanceDuration: true
              }
            }
          }
        }),

        // Most popular sessions
        prisma.session.findMany({
          include: {
            _count: {
              select: { attendance: true }
            },
            speaker: {
              select: {
                nameAr: true,
                nameEn: true
              }
            }
          },
          orderBy: {
            attendance: {
              _count: 'desc'
            }
          },
          take: 10
        }),

        // Session ratings
        prisma.rating.groupBy({
          by: ['sessionId'],
          _avg: { rating: true },
          _count: { rating: true }
        }),

        // Speaker statistics
        prisma.speaker.findMany({
          include: {
            _count: {
              select: { sessions: true }
            },
            sessions: {
              include: {
                _count: {
                  select: { attendance: true }
                }
              }
            }
          }
        })
      ]);

      res.json({
        success: true,
        data: {
          sessionAttendanceStats,
          popularSessions,
          sessionRatings,
          speakerStats
        }
      });
    } catch (error) {
      logger.error('Error getting session analytics:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get session analytics',
          code: 'GET_SESSION_ANALYTICS_ERROR'
        }
      });
    }
  }

  // Export report data
  async exportReport(req, res) {
    try {
      const { reportType, format = 'json', startDate, endDate } = req.query;

      let data;
      switch (reportType) {
        case 'users':
          data = await this.getUsersReportData(startDate, endDate);
          break;
        case 'financial':
          data = await this.getFinancialReportData(startDate, endDate);
          break;
        case 'sessions':
          data = await this.getSessionsReportData(startDate, endDate);
          break;
        case 'engagement':
          data = await this.getEngagementReportData(startDate, endDate);
          break;
        default:
          return res.status(400).json({
            success: false,
            error: {
              message: 'Invalid report type',
              code: 'INVALID_REPORT_TYPE'
            }
          });
      }

      if (format === 'csv') {
        // Convert to CSV format
        const csv = this.convertToCSV(data);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${reportType}_report.csv"`);
        res.send(csv);
      } else {
        res.json({
          success: true,
          data,
          exportedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('Error exporting report:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to export report',
          code: 'EXPORT_REPORT_ERROR'
        }
      });
    }
  }

  // Helper method to convert data to CSV
  convertToCSV(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value}"` : value;
        }).join(',')
      )
    ].join('\n');

    return csvContent;
  }

  // Helper methods for different report types
  async getUsersReportData(startDate, endDate) {
    const dateFilter = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    return await prisma.user.findMany({
      where: Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {},
      select: {
        id: true,
        arabicName: true,
        englishName: true,
        email: true,
        role: true,
        status: true,
        qualification: true,
        specialization: true,
        createdAt: true
      }
    });
  }

  async getFinancialReportData(startDate, endDate) {
    const dateFilter = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    return await prisma.payment.findMany({
      where: Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {},
      include: {
        registration: {
          include: {
            user: {
              select: {
                arabicName: true,
                englishName: true,
                email: true
              }
            }
          }
        }
      }
    });
  }

  async getSessionsReportData(startDate, endDate) {
    const dateFilter = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    return await prisma.session.findMany({
      where: Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {},
      include: {
        speaker: true,
        room: true,
        _count: {
          select: { attendance: true }
        }
      }
    });
  }

  async getEngagementReportData(startDate, endDate) {
    const dateFilter = {};
    if (startDate) dateFilter.gte = new Date(startDate);
    if (endDate) dateFilter.lte = new Date(endDate);

    return await prisma.userPoint.findMany({
      where: Object.keys(dateFilter).length > 0 ? { createdAt: dateFilter } : {},
      include: {
        user: {
          select: {
            arabicName: true,
            englishName: true,
            email: true
          }
        }
      }
    });
  }
}

module.exports = new ReportsController();
