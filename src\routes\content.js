const express = require('express');
const { authenticate, requireAdmin } = require('../middleware/auth');
const contentController = require('../controllers/contentController');

const router = express.Router();

// All content management routes require admin authentication
router.use(authenticate);
router.use(requireAdmin);

// Session Management
router.post('/sessions', contentController.createSession);
router.put('/sessions/:id', contentController.updateSession);
router.delete('/sessions/:id', contentController.deleteSession);

// Speaker Management
router.post('/speakers', contentController.createSpeaker);
router.put('/speakers/:id', contentController.updateSpeaker);
router.delete('/speakers/:id', contentController.deleteSpeaker);

// Announcement Management
router.get('/announcements', contentController.getAnnouncements);
router.post('/announcements', contentController.createAnnouncement);
router.put('/announcements/:id', contentController.updateAnnouncement);
router.delete('/announcements/:id', contentController.deleteAnnouncement);

// Content Statistics
router.get('/stats', contentController.getContentStats);

module.exports = router;
