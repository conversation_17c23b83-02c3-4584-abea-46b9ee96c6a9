# دليل إعداد VPS خطوة بخطوة - مشروع IDEC 2025

## متطلبات الخادم المقترحة

### المواصفات الدنيا
- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 100GB SSD
- **Bandwidth**: 1TB/شهر
- **OS**: Ubuntu 22.04 LTS

### مقدمي الخدمة المقترحين
1. **Contabo**: €8.99/شهر (ممتاز للمشاريع الكبيرة)
2. **Hetzner**: €15.29/شهر (أداء عالي)
3. **DigitalOcean**: $48/شهر (سهولة الإدارة)
4. **Vultr**: $24/شهر (شبكة عالمية)

## 1. الإعداد الأولي للخادم

### 1.1 الاتصال بالخادم وتحديث النظام
```bash
# الاتصال بالخادم
ssh root@your-server-ip

# تحديث النظام
apt update && apt upgrade -y

# تثبيت الأدوات الأساسية
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
```

### 1.2 إنشاء مستخدم جديد وتأمين SSH
```bash
# إنشاء مستخدم جديد
adduser idec-admin
usermod -aG sudo idec-admin

# إعداد SSH Key (من جهازك المحلي)
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
ssh-copy-id idec-admin@your-server-ip

# تأمين SSH
nano /etc/ssh/sshd_config
# تعديل الإعدادات التالية:
# Port 2222
# PermitRootLogin no
# PasswordAuthentication no
# PubkeyAuthentication yes

systemctl restart ssh
```

### 1.3 إعداد Firewall
```bash
# تفعيل UFW
ufw enable

# السماح بالاتصالات الأساسية
ufw allow 2222/tcp  # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS

# فحص الحالة
ufw status
```

## 2. تثبيت البرمجيات الأساسية

### 2.1 تثبيت Node.js
```bash
# إضافة مستودع NodeSource
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# تثبيت Node.js
apt install -y nodejs

# التحقق من التثبيت
node --version
npm --version

# تثبيت PM2 لإدارة العمليات
npm install -g pm2
```

### 2.2 تثبيت PostgreSQL
```bash
# تثبيت PostgreSQL
apt install -y postgresql postgresql-contrib

# تشغيل الخدمة
systemctl start postgresql
systemctl enable postgresql

# إعداد قاعدة البيانات
sudo -u postgres psql

-- داخل PostgreSQL console
CREATE DATABASE idec2025;
CREATE USER idec_user WITH ENCRYPTED PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE idec2025 TO idec_user;
\q
```

### 2.3 تثبيت Redis
```bash
# تثبيت Redis
apt install -y redis-server

# تكوين Redis
nano /etc/redis/redis.conf
# تعديل الإعدادات:
# maxmemory 512mb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# إعادة تشغيل Redis
systemctl restart redis-server
systemctl enable redis-server
```

### 2.4 تثبيت Nginx
```bash
# تثبيت Nginx
apt install -y nginx

# تشغيل الخدمة
systemctl start nginx
systemctl enable nginx

# إنشاء تكوين للتطبيق
nano /etc/nginx/sites-available/idec2025
```

#### تكوين Nginx
```nginx
# /etc/nginx/sites-available/idec2025
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration (سيتم إعدادها لاحقاً مع Let's Encrypt)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Main application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Admin dashboard
    location /admin {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files
    location /uploads {
        alias /var/www/idec2025/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API rate limiting
    location /api {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Rate limiting configuration
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}
```

```bash
# تفعيل الموقع
ln -s /etc/nginx/sites-available/idec2025 /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

## 3. إعداد SSL مع Let's Encrypt

### 3.1 تثبيت Certbot
```bash
# تثبيت Certbot
apt install -y certbot python3-certbot-nginx

# الحصول على شهادة SSL
certbot --nginx -d your-domain.com -d www.your-domain.com

# إعداد التجديد التلقائي
crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 4. نشر التطبيق

### 4.1 إعداد مجلد التطبيق
```bash
# إنشاء مجلد التطبيق
mkdir -p /var/www/idec2025
chown -R idec-admin:idec-admin /var/www/idec2025

# الانتقال للمجلد
cd /var/www/idec2025

# استنساخ المشروع (أو رفع الملفات)
git clone https://github.com/your-repo/idec2025.git .

# تثبيت التبعيات
npm install --production
```

### 4.2 إعداد متغيرات البيئة
```bash
# إنشاء ملف البيئة
nano .env
```

```env
# .env file
NODE_ENV=production
PORT=3000
ADMIN_PORT=3001

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=idec2025
DB_USER=idec_user
DB_PASSWORD=secure_password_here

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# File Upload
UPLOAD_PATH=/var/www/idec2025/uploads
MAX_FILE_SIZE=10485760

# SMS Service
SMS_API_KEY=your-sms-api-key
SMS_API_URL=https://api.sms-service.com/send

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 4.3 إعداد PM2
```bash
# إنشاء ملف تكوين PM2
nano ecosystem.config.js
```

```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'idec2025-api',
      script: './src/server.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    },
    {
      name: 'idec2025-admin',
      script: './src/admin-server.js',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/admin-err.log',
      out_file: './logs/admin-out.log',
      log_file: './logs/admin-combined.log',
      time: true
    }
  ]
};
```

```bash
# إنشاء مجلد السجلات
mkdir -p logs

# تشغيل التطبيق
pm2 start ecosystem.config.js

# حفظ تكوين PM2
pm2 save

# إعداد PM2 للتشغيل التلقائي
pm2 startup
pm2 save
```

## 5. إعداد المراقبة

### 5.1 تثبيت Prometheus
```bash
# إنشاء مستخدم prometheus
useradd --no-create-home --shell /bin/false prometheus

# تحميل Prometheus
cd /tmp
wget https://github.com/prometheus/prometheus/releases/download/v2.40.0/prometheus-2.40.0.linux-amd64.tar.gz
tar xvf prometheus-2.40.0.linux-amd64.tar.gz

# نسخ الملفات
cp prometheus-2.40.0.linux-amd64/prometheus /usr/local/bin/
cp prometheus-2.40.0.linux-amd64/promtool /usr/local/bin/
chown prometheus:prometheus /usr/local/bin/prometheus
chown prometheus:prometheus /usr/local/bin/promtool

# إنشاء مجلدات التكوين
mkdir /etc/prometheus
mkdir /var/lib/prometheus
chown prometheus:prometheus /etc/prometheus
chown prometheus:prometheus /var/lib/prometheus
```

### 5.2 تكوين Prometheus
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
  
  - job_name: 'idec2025'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
```

### 5.3 إنشاء خدمة systemd لـ Prometheus
```bash
nano /etc/systemd/system/prometheus.service
```

```ini
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/usr/local/bin/prometheus \
    --config.file /etc/prometheus/prometheus.yml \
    --storage.tsdb.path /var/lib/prometheus/ \
    --web.console.templates=/etc/prometheus/consoles \
    --web.console.libraries=/etc/prometheus/console_libraries \
    --web.listen-address=0.0.0.0:9090 \
    --web.enable-lifecycle

[Install]
WantedBy=multi-user.target
```

```bash
# تشغيل Prometheus
systemctl daemon-reload
systemctl start prometheus
systemctl enable prometheus
```

### 5.4 تثبيت Grafana
```bash
# إضافة مستودع Grafana
wget -q -O - https://packages.grafana.com/gpg.key | apt-key add -
echo "deb https://packages.grafana.com/oss/deb stable main" | tee -a /etc/apt/sources.list.d/grafana.list

# تثبيت Grafana
apt update
apt install -y grafana

# تشغيل Grafana
systemctl start grafana-server
systemctl enable grafana-server
```

## 6. إعداد النسخ الاحتياطي

### 6.1 إنشاء نص النسخ الاحتياطي
```bash
nano /usr/local/bin/backup-idec.sh
```

```bash
#!/bin/bash
# backup-idec.sh

# متغيرات
BACKUP_DIR="/backups/idec2025"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="idec2025"
DB_USER="idec_user"
APP_DIR="/var/www/idec2025"

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
PGPASSWORD="secure_password_here" pg_dump -h localhost -U $DB_USER $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# نسخ احتياطي للملفات
tar -czf $BACKUP_DIR/files_$DATE.tar.gz $APP_DIR/uploads

# نسخ احتياطي للتكوين
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /etc/nginx/sites-available/idec2025 $APP_DIR/.env

# حذف النسخ القديمة (أكثر من 7 أيام)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# جعل النص قابل للتنفيذ
chmod +x /usr/local/bin/backup-idec.sh

# إضافة مهمة cron للنسخ الاحتياطي اليومي
crontab -e
# إضافة السطر التالي:
0 2 * * * /usr/local/bin/backup-idec.sh >> /var/log/backup.log 2>&1
```

## 7. الأمان الإضافي

### 7.1 تثبيت Fail2Ban
```bash
# تثبيت Fail2Ban
apt install -y fail2ban

# تكوين Fail2Ban
nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

```bash
systemctl start fail2ban
systemctl enable fail2ban
```

### 7.2 تحديثات الأمان التلقائية
```bash
# تثبيت unattended-upgrades
apt install -y unattended-upgrades

# تكوين التحديثات التلقائية
dpkg-reconfigure -plow unattended-upgrades
```

هذا الدليل يوفر إعداد كامل لخادم VPS قادر على تشغيل مشروع IDEC 2025 بكفاءة عالية وتكلفة منخفضة!
