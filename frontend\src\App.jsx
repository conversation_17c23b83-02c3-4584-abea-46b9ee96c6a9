import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Container, Typography, Box, Button } from '@mui/material';

// Simple placeholder components
const HomePage = () => (
  <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
    <Typography variant="h2" gutterBottom color="primary">
      المؤتمر الدولي لطب الأسنان 2025
    </Typography>
    <Typography variant="h5" paragraph color="text.secondary">
      المؤتمر الدولي الأول لطب الأسنان في المملكة العربية السعودية
    </Typography>
    <Typography variant="body1" paragraph>
      15-17 مارس 2025 - الرياض، السعودية
    </Typography>
    <Box sx={{ mt: 4 }}>
      <Button variant="contained" size="large" sx={{ mr: 2 }}>
        سجل الآن
      </Button>
      <Button variant="outlined" size="large">
        عرض البرنامج
      </Button>
    </Box>
  </Container>
);

const ConferencePage = () => (
  <Container maxWidth="lg" sx={{ py: 4 }}>
    <Typography variant="h4" gutterBottom>صفحة المؤتمر</Typography>
    <Typography>قريباً...</Typography>
  </Container>
);

const LoginPage = () => (
  <Container maxWidth="sm" sx={{ py: 4 }}>
    <Typography variant="h4" gutterBottom>تسجيل الدخول</Typography>
    <Typography>قريباً...</Typography>
  </Container>
);

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Create Material-UI theme with Arabic support
const theme = createTheme({
  direction: 'rtl',
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: [
      'Cairo',
      'Roboto',
      'Arial',
      'sans-serif',
    ].join(','),
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/conference" element={<ConferencePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<HomePage />} />
          </Routes>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
