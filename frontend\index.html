<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>IDEC 2025 - المؤتمر الدولي لطب الأسنان</title>

    <!-- Arabic Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Meta Tags -->
    <meta name="description" content="المؤتمر الدولي الأول لطب الأسنان في المملكة العربية السعودية - 15-17 مارس 2025">
    <meta name="keywords" content="طب الأسنان, مؤتمر, السعودية, الرياض, IDEC 2025">
    <meta name="author" content="IDEC 2025">

    <!-- Open Graph -->
    <meta property="og:title" content="IDEC 2025 - المؤتمر الدولي لطب الأسنان">
    <meta property="og:description" content="المؤتمر الدولي الأول لطب الأسنان في المملكة العربية السعودية">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_SA">

    <style>
      body {
        font-family: 'Cairo', 'Roboto', sans-serif;
        margin: 0;
        padding: 0;
        direction: rtl;
      }

      #root {
        min-height: 100vh;
      }

      /* Loading spinner */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
        gap: 20px;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #1976d2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        color: #666;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري التحميل...</div>
      </div>
    </div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
