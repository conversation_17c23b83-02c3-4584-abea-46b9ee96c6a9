/* Admin Dashboard Styles */
:root {
    --primary-red: #DC143C;
    --dark-red: #B91C3C;
    --primary-black: #1F2937;
    --light-gray: #F8F9FA;
    --border-color: #E5E7EB;
}

* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: var(--light-gray);
    padding-top: 76px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, var(--primary-red), var(--dark-red)) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

/* Page Title */
.page-title {
    color: var(--primary-black);
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--primary-red);
    display: inline-block;
}

/* Statistics Cards */
.stat-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.5rem;
}

.stat-icon {
    font-size: 3rem;
    opacity: 0.3;
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid var(--border-color);
    border-radius: 15px 15px 0 0 !important;
    padding: 1.25rem;
}

.card-title {
    color: var(--primary-black);
    font-weight: 600;
    margin: 0;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: var(--light-gray);
    border-top: none;
    font-weight: 600;
    color: var(--primary-black);
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--dark-red), var(--primary-red));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 20, 60, 0.3);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

.badge-success {
    background-color: #10B981;
}

.badge-warning {
    background-color: #F59E0B;
}

.badge-danger {
    background-color: #EF4444;
}

.badge-info {
    background-color: #3B82F6;
}

/* Activity List */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: rgba(220, 20, 60, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.activity-content h6 {
    margin: 0;
    font-weight: 600;
    color: var(--primary-black);
}

.activity-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #6B7280;
}

.activity-time {
    font-size: 0.75rem;
    color: #9CA3AF;
}

/* Alert List */
.alert-list {
    max-height: 400px;
    overflow-y: auto;
}

.alert-item {
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-item.alert-warning {
    background-color: #FEF3C7;
    border-left-color: #F59E0B;
}

.alert-item.alert-danger {
    background-color: #FEE2E2;
    border-left-color: #EF4444;
}

.alert-item.alert-info {
    background-color: #DBEAFE;
    border-left-color: #3B82F6;
}

/* Report Cards */
.report-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Content Sections */
.content-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
}

/* Responsive */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-icon {
        font-size: 2rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-red);
}

/* Form Controls */
.form-control {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(220, 20, 60, 0.25);
}

/* Modal Enhancements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-title {
    font-weight: 600;
}

/* Dropdown Menus */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(220, 20, 60, 0.1);
    color: var(--primary-red);
}

/* Tabs */
.nav-tabs .nav-link {
    border: none;
    color: #6B7280;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom: 3px solid var(--primary-red);
    color: var(--primary-red);
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #E5E7EB;
}

.progress-bar {
    background: linear-gradient(135deg, var(--primary-red), var(--dark-red));
    border-radius: 4px;
}
