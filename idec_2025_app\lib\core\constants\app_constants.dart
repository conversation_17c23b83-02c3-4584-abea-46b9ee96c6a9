class AppConstants {
  // App Info
  static const String appName = 'IDEC 2025';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'المؤتمر الدولي لطب الأسنان 2025';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:3001/api/v1';
  static const String apiTimeout = '30';
  
  // Conference Info
  static const String conferenceName = 'المؤتمر الدولي لطب الأسنان 2025';
  static const String conferenceNameEn = 'International Dental Conference 2025';
  static const String conferenceDescription = 'المؤتمر الدولي الثالث لطب الأسنان في الجمهورية اليمنية';
  static const String conferenceLocation = 'صنعاء، الجمهورية اليمنية';
  static const String conferenceStartDate = '2025-03-15';
  static const String conferenceEndDate = '2025-03-17';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';
  
  // Default Values
  static const String defaultLanguage = 'ar';
  static const String defaultCurrency = 'YER';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  
  // Supported Languages
  static const List<String> supportedLanguages = ['ar', 'en'];
  
  // File Types
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];
}

class ApiEndpoints {
  // Auth
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh-token';
  static const String profile = '/users/profile';
  
  // Conference
  static const String sessions = '/conference/sessions';
  static const String speakers = '/conference/speakers';
  static const String agenda = '/conference/my-agenda';
  
  // Subscriptions
  static const String subscriptions = '/subscriptions';
  static const String mySubscription = '/subscriptions/my-subscription';
  static const String pricing = '/subscriptions/pricing';
  
  // Payments
  static const String payments = '/payments';
  static const String paymentHistory = '/users/payment-history';
  
  // Notifications
  static const String notifications = '/users/notifications';
  
  // Upload
  static const String upload = '/upload';
}

class AppColors {
  // Primary Conference Colors
  static const int primaryRed = 0xFFDC143C;      // Crimson Red
  static const int primaryBlack = 0xFF1A1A1A;    // Rich Black
  static const int primaryWhite = 0xFFFFFFFF;    // Pure White

  // Red Variations
  static const int lightRed = 0xFFFF6B6B;        // Light Red
  static const int darkRed = 0xFFB71C1C;         // Dark Red
  static const int redAccent = 0xFFFF1744;       // Red Accent

  // Black Variations
  static const int lightBlack = 0xFF2C2C2C;      // Light Black
  static const int darkBlack = 0xFF000000;       // Pure Black
  static const int charcoal = 0xFF36454F;        // Charcoal

  // White Variations
  static const int offWhite = 0xFFFAFAFA;        // Off White
  static const int lightGrey = 0xFFF5F5F5;       // Light Grey
  static const int mediumGrey = 0xFFE0E0E0;      // Medium Grey

  // Neutral Colors
  static const int grey = 0xFF9E9E9E;
  static const int darkGrey = 0xFF424242;

  // Status Colors
  static const int success = 0xFF4CAF50;
  static const int warning = 0xFFFF9800;
  static const int error = 0xFFF44336;
  static const int info = 0xFF2196F3;

  // Gradient Colors
  static const int gradientStart = primaryRed;
  static const int gradientEnd = darkRed;
  static const int gradientAccent = primaryBlack;
}

class AppSizes {
  // Padding & Margins
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Border Radius
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  // Icon Sizes
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // Button Heights
  static const double buttonHeight = 48.0;
  static const double buttonHeightS = 36.0;
  static const double buttonHeightL = 56.0;
}

class AppStrings {
  // Common
  static const String loading = 'جاري التحميل...';
  static const String error = 'خطأ';
  static const String success = 'نجح';
  static const String cancel = 'إلغاء';
  static const String save = 'حفظ';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String view = 'عرض';
  static const String search = 'بحث';
  static const String filter = 'تصفية';
  
  // Navigation
  static const String home = 'الرئيسية';
  static const String conference = 'المؤتمر';
  static const String speakers = 'المتحدثون';
  static const String agenda = 'جدولي';
  static const String profile = 'الملف الشخصي';
  static const String settings = 'الإعدادات';
  
  // Auth
  static const String login = 'تسجيل الدخول';
  static const String register = 'إنشاء حساب';
  static const String logout = 'تسجيل الخروج';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  
  // Forms
  static const String arabicName = 'الاسم العربي';
  static const String englishName = 'الاسم الإنجليزي';
  static const String phone = 'رقم الهاتف';
  static const String qualification = 'المؤهل العلمي';
  static const String specialization = 'التخصص';
  static const String university = 'الجامعة';
  
  // Conference
  static const String sessions = 'الجلسات';
  static const String sessionDetails = 'تفاصيل الجلسة';
  static const String registerForSession = 'تسجيل في الجلسة';
  static const String myAgenda = 'جدولي الشخصي';
  
  // Subscription
  static const String subscription = 'الاشتراك';
  static const String subscriptionStatus = 'حالة الاشتراك';
  static const String subscriptionPricing = 'أسعار الاشتراك';
  static const String submitSubscription = 'إرسال طلب الاشتراك';
  
  // Payment
  static const String payment = 'الدفع';
  static const String paymentHistory = 'تاريخ المدفوعات';
  static const String amount = 'المبلغ';
  static const String currency = 'العملة';
  static const String status = 'الحالة';
  
  // Status
  static const String pendingReview = 'قيد المراجعة';
  static const String approvedPendingPayment = 'مقبول - في انتظار الدفع';
  static const String active = 'نشط';
  static const String rejected = 'مرفوض';
  static const String completed = 'مكتمل';
  static const String pending = 'في الانتظار';
  static const String failed = 'فشل';
  
  // Messages
  static const String loginSuccess = 'تم تسجيل الدخول بنجاح';
  static const String loginError = 'فشل في تسجيل الدخول';
  static const String registerSuccess = 'تم إنشاء الحساب بنجاح';
  static const String registerError = 'فشل في إنشاء الحساب';
  static const String profileUpdateSuccess = 'تم تحديث الملف الشخصي بنجاح';
  static const String profileUpdateError = 'فشل في تحديث الملف الشخصي';
  
  // Validation
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String emailInvalid = 'البريد الإلكتروني غير صحيح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsDoNotMatch = 'كلمات المرور غير متطابقة';
  static const String phoneInvalid = 'رقم الهاتف غير صحيح';
}

class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';
  static const String login = '/login';
  static const String register = '/register';
  static const String profile = '/profile';
  static const String conference = '/conference';
  static const String speakers = '/speakers';
  static const String agenda = '/agenda';
  static const String subscription = '/subscription';
  static const String payment = '/payment';
  static const String settings = '/settings';
  static const String sessionDetails = '/session-details';
  static const String speakerDetails = '/speaker-details';
}
