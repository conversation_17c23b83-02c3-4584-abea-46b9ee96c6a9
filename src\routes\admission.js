const express = require('express');
const { body, param } = require('express-validator');
const router = express.Router();

const admissionController = require('../controllers/admissionController');
const { authenticate, requireRole } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');

// Validation rules
const reviewApplicationValidation = [
  param('subscriptionId').notEmpty().withMessage('معرف الطلب مطلوب'),
  body('status').isIn(['APPROVED', 'REJECTED', 'NEEDS_INFO']).withMessage('حالة المراجعة غير صحيحة'),
  body('comments').optional().isString().withMessage('التعليقات يجب أن تكون نص'),
  body('documentsChecked').optional().isObject().withMessage('قائمة الوثائق المراجعة يجب أن تكون كائن'),
  handleValidationErrors
];

const bulkReviewValidation = [
  body('applicationIds').isArray({ min: 1 }).withMessage('يجب تحديد طلب واحد على الأقل'),
  body('status').isIn(['APPROVED', 'REJECTED']).withMessage('حالة المراجعة غير صحيحة'),
  body('comments').optional().isString().withMessage('التعليقات يجب أن تكون نص'),
  handleValidationErrors
];

// Routes for Admission Committee
router.use(authenticate);
router.use(requireRole(['admission_committee', 'super_admin', 'admin']));

// Get pending applications
router.get('/applications/pending', admissionController.getPendingApplications);

// Get application details
router.get('/applications/:subscriptionId', admissionController.getApplicationDetails);

// Review single application
router.post('/applications/:subscriptionId/review', 
  reviewApplicationValidation,
  admissionController.reviewApplication
);

// Bulk review applications
router.post('/applications/bulk-review',
  bulkReviewValidation,
  admissionController.bulkReviewApplications
);

// Get review statistics
router.get('/statistics', admissionController.getReviewStatistics);

module.exports = router;
