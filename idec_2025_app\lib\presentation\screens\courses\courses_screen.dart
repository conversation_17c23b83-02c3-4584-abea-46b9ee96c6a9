import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  String _selectedCategory = 'الكل';
  String _selectedLevel = 'الكل';
  String _selectedDuration = 'الكل';
  
  final List<String> _categories = [
    'الكل', 'جراحة', 'تقويم', 'علاج جذور', 'أطفال', 'تجميل', 'إدارة'
  ];
  final List<String> _levels = ['الكل', 'مبتدئ', 'متوسط', 'متقدم'];
  final List<String> _durations = ['الكل', 'يوم واحد', 'يومان', 'ثلاثة أيام', 'أسبوع'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 320,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'الدورات التدريبية',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: 0.1 * _animationController.value,
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/courses_pattern.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.paddingL),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 60),
                            
                            // Courses Icon with Animation
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: SlideTransition(
                                position: _slideAnimation,
                                child: Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.school,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Stats Row
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('25+', 'دورة تدريبية'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('15+', 'مدرب خبير'),
                                  Container(
                                    width: 1,
                                    height: 30,
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                  ),
                                  _buildStatItem('100+', 'ساعة تدريب'),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: AppSizes.paddingM),
                            
                            // Quick Info
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Container(
                                padding: const EdgeInsets.all(AppSizes.paddingM),
                                decoration: BoxDecoration(
                                  color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(AppSizes.radiusL),
                                ),
                                child: Text(
                                  'دورات تدريبية متخصصة مع شهادات معتمدة',
                                  style: TextStyle(
                                    color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                isScrollable: true,
                tabs: const [
                  Tab(text: 'الكل'),
                  Tab(text: 'قبل المؤتمر'),
                  Tab(text: 'أثناء المؤتمر'),
                  Tab(text: 'بعد المؤتمر'),
                ],
              ),
            ),
          ];
        },
        body: Column(
          children: [
            // Filters
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingM),
              color: const Color(AppColors.offWhite),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildFilterDropdown(
                          'التخصص',
                          _selectedCategory,
                          _categories,
                          (value) => setState(() => _selectedCategory = value!),
                        ),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      Expanded(
                        child: _buildFilterDropdown(
                          'المستوى',
                          _selectedLevel,
                          _levels,
                          (value) => setState(() => _selectedLevel = value!),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.paddingS),
                  Row(
                    children: [
                      Expanded(
                        child: _buildFilterDropdown(
                          'المدة',
                          _selectedDuration,
                          _durations,
                          (value) => setState(() => _selectedDuration = value!),
                        ),
                      ),
                      const SizedBox(width: AppSizes.paddingS),
                      ElevatedButton.icon(
                        onPressed: _showMyCourses,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(AppColors.primaryRed),
                          foregroundColor: const Color(AppColors.primaryWhite),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppSizes.radiusM),
                          ),
                        ),
                        icon: const Icon(Icons.bookmark, size: AppSizes.iconS),
                        label: const Text('دوراتي', style: TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildCoursesList(0),
                    _buildCoursesList(1),
                    _buildCoursesList(2),
                    _buildCoursesList(3),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    ValueChanged<String?> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingM),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryWhite),
        borderRadius: BorderRadius.circular(AppSizes.radiusM),
        border: Border.all(
          color: const Color(AppColors.primaryRed).withValues(alpha: 0.3),
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          hint: Text(label),
          isExpanded: true,
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: Text(
                item,
                style: const TextStyle(fontSize: 14),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildCoursesList(int tabIndex) {
    final courses = _getCoursesForTab(tabIndex);

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: courses.length,
      itemBuilder: (context, index) {
        final course = courses[index];
        return _buildCourseCard(course, index);
      },
    );
  }

  Widget _buildCourseCard(Map<String, dynamic> course, int index) {
    final isEnrolled = course['isEnrolled'] ?? false;
    final isFull = course['isFull'] ?? false;
    final isStarted = course['isStarted'] ?? false;
    final level = course['level'] ?? 'متوسط';
    final category = course['category'] ?? 'عام';

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
            border: Border.all(
              color: isStarted
                  ? const Color(AppColors.success)
                  : isFull
                      ? const Color(AppColors.error)
                      : const Color(AppColors.primaryRed).withValues(alpha: 0.2),
              width: 2,
            ),
          ),
          child: Column(
            children: [
              // Course Header
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isStarted
                        ? [const Color(AppColors.success), const Color(AppColors.success).withValues(alpha: 0.8)]
                        : isFull
                            ? [const Color(AppColors.error), const Color(AppColors.error).withValues(alpha: 0.8)]
                            : [const Color(AppColors.primaryRed), const Color(AppColors.darkRed)],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppSizes.radiusL),
                    topRight: Radius.circular(AppSizes.radiusL),
                  ),
                ),
                child: Row(
                  children: [
                    // Course Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.schedule,
                                color: Color(AppColors.primaryWhite),
                                size: AppSizes.iconS,
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                '${course['duration']} • ${course['date']}',
                                style: const TextStyle(
                                  color: Color(AppColors.primaryWhite),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSizes.paddingXS),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on,
                                color: Color(AppColors.primaryWhite),
                                size: AppSizes.iconS,
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                course['location'],
                                style: TextStyle(
                                  color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Status and Price
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        if (isStarted)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.primaryWhite),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: const Text(
                              'بدأت',
                              style: TextStyle(
                                color: Color(AppColors.success),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                        if (isFull && !isStarted)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSizes.paddingS,
                              vertical: AppSizes.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(AppColors.primaryWhite),
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            child: const Text(
                              'ممتلئة',
                              style: TextStyle(
                                color: Color(AppColors.error),
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),

                        const SizedBox(height: AppSizes.paddingXS),

                        // Price
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            course['price'] == 0 ? 'مجاني' : '${course['price']} ريال',
                            style: const TextStyle(
                              color: Color(AppColors.primaryWhite),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Course Content
              Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and Badges
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            course['title'],
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(AppColors.primaryBlack),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: _getLevelColor(level).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            level,
                            style: TextStyle(
                              color: _getLevelColor(level),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    // Category and Credits
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryBlack).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            category,
                            style: const TextStyle(
                              color: Color(AppColors.primaryBlack),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.warning).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.school,
                                size: 12,
                                color: Color(AppColors.warning),
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                '${course['credits']} ساعة',
                                style: const TextStyle(
                                  color: Color(AppColors.warning),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    // Description
                    Text(
                      course['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: const Color(AppColors.grey),
                        height: 1.4,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    // Instructor
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                          child: const Icon(
                            Icons.person,
                            size: 20,
                            color: Color(AppColors.primaryRed),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                course['instructor'],
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: const Color(AppColors.primaryBlack),
                                ),
                              ),
                              Text(
                                course['instructorTitle'],
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: const Color(AppColors.grey),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Capacity
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.info).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.people,
                                size: 12,
                                color: Color(AppColors.info),
                              ),
                              const SizedBox(width: AppSizes.paddingXS),
                              Text(
                                '${course['enrolled']}/${course['capacity']}',
                                style: const TextStyle(
                                  color: Color(AppColors.info),
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: ElevatedButton.icon(
                            onPressed: isFull ? null : () => _enrollInCourse(course),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isEnrolled
                                  ? const Color(AppColors.success)
                                  : isFull
                                      ? const Color(AppColors.grey)
                                      : const Color(AppColors.primaryRed),
                              foregroundColor: const Color(AppColors.primaryWhite),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            icon: Icon(
                              isEnrolled ? Icons.check : isFull ? Icons.block : Icons.add,
                              size: AppSizes.iconS,
                            ),
                            label: Text(
                              isEnrolled ? 'مسجل' : isFull ? 'ممتلئة' : 'تسجيل',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ),

                        const SizedBox(width: AppSizes.paddingS),

                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => _showCourseDetails(course),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: const Color(AppColors.primaryRed),
                              side: const BorderSide(color: Color(AppColors.primaryRed)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            icon: const Icon(Icons.info_outline, size: AppSizes.iconS),
                            label: const Text('تفاصيل', style: TextStyle(fontSize: 14)),
                          ),
                        ),

                        const SizedBox(width: AppSizes.paddingS),

                        IconButton(
                          onPressed: () => _addToWishlist(course),
                          icon: Icon(
                            course['isWishlisted'] ? Icons.bookmark : Icons.bookmark_border,
                            color: course['isWishlisted']
                                ? const Color(AppColors.primaryRed)
                                : const Color(AppColors.grey),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case 'مبتدئ':
        return const Color(AppColors.success);
      case 'متوسط':
        return const Color(AppColors.warning);
      case 'متقدم':
        return const Color(AppColors.error);
      default:
        return const Color(AppColors.primaryBlack);
    }
  }

  void _showMyCourses() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Title
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Text(
                'دوراتي المسجلة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // My courses list would go here
            const Expanded(
              child: Center(
                child: Text('لا توجد دورات مسجلة بعد'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _enrollInCourse(Map<String, dynamic> course) {
    setState(() {
      course['isEnrolled'] = true;
      course['enrolled'] = (course['enrolled'] ?? 0) + 1;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم التسجيل في دورة: ${course['title']}'),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _showCourseDetails(Map<String, dynamic> course) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      course['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Text(
                      'وصف الدورة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingS),

                    Text(
                      course['fullDescription'] ?? course['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addToWishlist(Map<String, dynamic> course) {
    setState(() {
      course['isWishlisted'] = !(course['isWishlisted'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          course['isWishlisted']
              ? 'تم إضافة الدورة للمفضلة'
              : 'تم إزالة الدورة من المفضلة',
        ),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  List<Map<String, dynamic>> _getCoursesForTab(int tabIndex) {
    // Mock data for courses
    final allCourses = [
      // All courses
      [
        {
          'title': 'أساسيات زراعة الأسنان',
          'description': 'دورة شاملة تغطي أساسيات زراعة الأسنان والتقنيات الحديثة',
          'instructor': 'د. أحمد محمد علي',
          'instructorTitle': 'استشاري جراحة الفم والوجه والفكين',
          'duration': 'يومان',
          'date': '13-14 مارس',
          'location': 'مركز التدريب A',
          'level': 'متوسط',
          'category': 'جراحة',
          'credits': 16,
          'price': 1500,
          'capacity': 30,
          'enrolled': 25,
          'isEnrolled': true,
          'isFull': false,
          'isStarted': false,
          'isWishlisted': true,
        },
        {
          'title': 'التقويم الشفاف المتقدم',
          'description': 'تقنيات متقدمة في التقويم الشفاف وحالات معقدة',
          'instructor': 'د. محمد عبدالله الزهراني',
          'instructorTitle': 'استشاري تقويم الأسنان',
          'duration': 'ثلاثة أيام',
          'date': '10-12 مارس',
          'location': 'مركز التدريب B',
          'level': 'متقدم',
          'category': 'تقويم',
          'credits': 24,
          'price': 2000,
          'capacity': 20,
          'enrolled': 20,
          'isEnrolled': false,
          'isFull': true,
          'isStarted': false,
          'isWishlisted': false,
        },
      ],
      // Pre-conference courses
      [
        {
          'title': 'إدارة عيادات الأسنان الحديثة',
          'description': 'أساليب إدارة العيادات والتسويق الرقمي',
          'instructor': 'د. سارة أحمد محمد',
          'instructorTitle': 'خبيرة إدارة الأعمال الطبية',
          'duration': 'يوم واحد',
          'date': '10 مارس',
          'location': 'مركز التدريب C',
          'level': 'مبتدئ',
          'category': 'إدارة',
          'credits': 8,
          'price': 800,
          'capacity': 50,
          'enrolled': 35,
          'isEnrolled': false,
          'isFull': false,
          'isStarted': false,
          'isWishlisted': true,
        },
      ],
      // During conference courses
      [
        {
          'title': 'ورشة علاج الجذور بالمجهر',
          'description': 'تطبيق عملي لتقنيات علاج الجذور بالمجهر',
          'instructor': 'د. عبدالرحمن علي محمد',
          'instructorTitle': 'استشاري علاج الجذور',
          'duration': 'يوم واحد',
          'date': '16 مارس',
          'location': 'مختبر التدريب العملي',
          'level': 'متقدم',
          'category': 'علاج جذور',
          'credits': 8,
          'price': 0,
          'capacity': 15,
          'enrolled': 12,
          'isEnrolled': true,
          'isFull': false,
          'isStarted': true,
          'isWishlisted': false,
        },
      ],
      // Post-conference courses
      [
        {
          'title': 'متابعة حالات زراعة الأسنان',
          'description': 'متابعة طويلة المدى لحالات زراعة الأسنان',
          'instructor': 'د. نورا أحمد سالم',
          'instructorTitle': 'استشارية طب اللثة',
          'duration': 'أسبوع',
          'date': '20-26 مارس',
          'location': 'عيادات التدريب',
          'level': 'متقدم',
          'category': 'جراحة',
          'credits': 40,
          'price': 3000,
          'capacity': 10,
          'enrolled': 8,
          'isEnrolled': false,
          'isFull': false,
          'isStarted': false,
          'isWishlisted': false,
        },
      ],
    ];

    return allCourses[tabIndex];
  }
}
