const redis = require('redis');
const logger = require('../utils/logger');

let redisClient;

const connectRedis = async () => {
  try {
    if (!redisClient) {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        db: parseInt(process.env.REDIS_DB) || 0,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
      };

      // Add password if provided
      if (process.env.REDIS_PASSWORD) {
        redisConfig.password = process.env.REDIS_PASSWORD;
      }

      redisClient = redis.createClient(redisConfig);

      // Error handling
      redisClient.on('error', (err) => {
        logger.error('Redis Client Error:', err);
      });

      redisClient.on('connect', () => {
        logger.info('Redis client connected');
      });

      redisClient.on('ready', () => {
        logger.info('Redis client ready');
      });

      redisClient.on('end', () => {
        logger.info('Redis client disconnected');
      });

      // Connect to Redis
      await redisClient.connect();
      
      logger.info('Redis connection established successfully');
    }
    
    return redisClient;
  } catch (error) {
    logger.error('Redis connection failed:', error);
    throw error;
  }
};

const disconnectRedis = async () => {
  try {
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
      logger.info('Redis connection closed');
    }
  } catch (error) {
    logger.error('Error closing Redis connection:', error);
  }
};

const getRedisClient = () => {
  if (!redisClient || !redisClient.isOpen) {
    throw new Error('Redis not connected. Call connectRedis() first.');
  }
  return redisClient;
};

// Cache helper functions
const cache = {
  // Set a value with optional TTL (in seconds)
  async set(key, value, ttl = 3600) {
    try {
      const client = getRedisClient();
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        await client.setEx(key, ttl, serializedValue);
      } else {
        await client.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  },

  // Get a value
  async get(key) {
    try {
      const client = getRedisClient();
      const value = await client.get(key);
      
      if (value) {
        return JSON.parse(value);
      }
      
      return null;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  },

  // Delete a key
  async del(key) {
    try {
      const client = getRedisClient();
      const result = await client.del(key);
      return result > 0;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  },

  // Check if key exists
  async exists(key) {
    try {
      const client = getRedisClient();
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  },

  // Set expiration for a key
  async expire(key, ttl) {
    try {
      const client = getRedisClient();
      const result = await client.expire(key, ttl);
      return result === 1;
    } catch (error) {
      logger.error('Cache expire error:', error);
      return false;
    }
  },

  // Get TTL for a key
  async ttl(key) {
    try {
      const client = getRedisClient();
      return await client.ttl(key);
    } catch (error) {
      logger.error('Cache TTL error:', error);
      return -1;
    }
  },

  // Flush all cache
  async flush() {
    try {
      const client = getRedisClient();
      await client.flushDb();
      return true;
    } catch (error) {
      logger.error('Cache flush error:', error);
      return false;
    }
  }
};

// Session management
const session = {
  // Set user session
  async setUserSession(userId, sessionData, ttl = 86400) { // 24 hours default
    const key = `session:user:${userId}`;
    return await cache.set(key, sessionData, ttl);
  },

  // Get user session
  async getUserSession(userId) {
    const key = `session:user:${userId}`;
    return await cache.get(key);
  },

  // Delete user session
  async deleteUserSession(userId) {
    const key = `session:user:${userId}`;
    return await cache.del(key);
  },

  // Set JWT blacklist (for logout)
  async blacklistToken(token, ttl) {
    const key = `blacklist:${token}`;
    return await cache.set(key, true, ttl);
  },

  // Check if token is blacklisted
  async isTokenBlacklisted(token) {
    const key = `blacklist:${token}`;
    return await cache.exists(key);
  }
};

// Health check function
const checkRedisHealth = async () => {
  try {
    const client = getRedisClient();
    const result = await client.ping();
    
    if (result === 'PONG') {
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } else {
      return { 
        status: 'unhealthy', 
        error: 'Ping failed', 
        timestamp: new Date().toISOString() 
      };
    }
  } catch (error) {
    logger.error('Redis health check failed:', error);
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date().toISOString() 
    };
  }
};

module.exports = {
  connectRedis,
  disconnectRedis,
  getRedisClient,
  cache,
  session,
  checkRedisHealth
};
