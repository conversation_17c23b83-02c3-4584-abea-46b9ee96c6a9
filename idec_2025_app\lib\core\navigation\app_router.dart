import 'package:flutter/material.dart';

// Main Screens
import '../../presentation/screens/splash/splash_screen.dart';
import '../../presentation/screens/home/<USER>';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/conference/conference_screen.dart';
import '../../presentation/screens/speakers/speakers_screen.dart';
import '../../presentation/screens/agenda/agenda_screen.dart';
import '../../presentation/screens/profile/profile_screen.dart';
import '../../presentation/screens/search/search_screen.dart';
import '../../presentation/screens/notifications/notifications_screen.dart';
import '../../presentation/screens/qr/qr_scanner_screen.dart';

// Advanced Screens
import '../../presentation/screens/sessions/sessions_screen.dart';
import '../../presentation/screens/exhibition/exhibition_screen.dart';
import '../../presentation/screens/courses/courses_screen.dart';
import '../../presentation/screens/news/news_screen.dart';
import '../../presentation/screens/library/digital_library_screen.dart';
import '../../presentation/screens/exhibitors/exhibitors_screen.dart';
import '../../presentation/screens/ar/ar_experience_screen.dart';
import '../../presentation/screens/ai/ai_assistant_screen.dart';
import '../../presentation/screens/social/social_network_screen.dart';
import '../../presentation/screens/analytics/analytics_screen.dart';
import '../../presentation/screens/settings/notification_settings_screen.dart';
import '../../presentation/screens/settings/offline_settings_screen.dart';

class AppRouter {
  static const String splash = '/';
  static const String home = '/home';
  static const String login = '/login';
  static const String register = '/register';
  static const String conference = '/conference';
  static const String speakers = '/speakers';
  static const String agenda = '/agenda';
  static const String profile = '/profile';
  static const String search = '/search';
  static const String notifications = '/notifications';
  static const String notificationSettings = '/notification-settings';
  static const String offlineSettings = '/offline-settings';
  static const String qrScanner = '/qr-scanner';
  
  // Advanced Routes
  static const String sessions = '/sessions';
  static const String exhibition = '/exhibition';
  static const String courses = '/courses';
  static const String news = '/news';
  static const String digitalLibrary = '/digital-library';
  static const String exhibitors = '/exhibitors';
  static const String arExperience = '/ar-experience';
  static const String aiAssistant = '/ai-assistant';
  static const String socialNetwork = '/social-network';
  static const String analytics = '/analytics';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case home:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());
      case conference:
        return MaterialPageRoute(builder: (_) => const ConferenceScreen());
      case speakers:
        return MaterialPageRoute(builder: (_) => const SpeakersScreen());
      case agenda:
        return MaterialPageRoute(builder: (_) => const AgendaScreen());
      case profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case search:
        return MaterialPageRoute(builder: (_) => const SearchScreen());
      case notifications:
        return MaterialPageRoute(builder: (_) => const NotificationsScreen());
      case notificationSettings:
        return MaterialPageRoute(builder: (_) => const NotificationSettingsScreen());
      case offlineSettings:
        return MaterialPageRoute(builder: (_) => const OfflineSettingsScreen());
      case qrScanner:
        return MaterialPageRoute(builder: (_) => const QRScannerScreen());
        
      // Advanced Routes
      case sessions:
        return MaterialPageRoute(builder: (_) => const SessionsScreen());
      case exhibition:
        return MaterialPageRoute(builder: (_) => const ExhibitionScreen());
      case courses:
        return MaterialPageRoute(builder: (_) => const CoursesScreen());
      case news:
        return MaterialPageRoute(builder: (_) => const NewsScreen());
      case digitalLibrary:
        return MaterialPageRoute(builder: (_) => const DigitalLibraryScreen());
      case exhibitors:
        return MaterialPageRoute(builder: (_) => const ExhibitorsScreen());
      case arExperience:
        return MaterialPageRoute(builder: (_) => const ARExperienceScreen());
      case aiAssistant:
        return MaterialPageRoute(builder: (_) => const AIAssistantScreen());
      case socialNetwork:
        return MaterialPageRoute(builder: (_) => const SocialNetworkScreen());
      case analytics:
        return MaterialPageRoute(builder: (_) => const AnalyticsScreen());
        
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(
              title: const Text('صفحة غير موجودة'),
            ),
            body: const Center(
              child: Text('الصفحة المطلوبة غير موجودة'),
            ),
          ),
        );
    }
  }

  // Helper methods for navigation
  static void navigateToHome(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(context, home, (route) => false);
  }

  static void navigateToLogin(BuildContext context) {
    Navigator.pushNamed(context, login);
  }

  static void navigateToRegister(BuildContext context) {
    Navigator.pushNamed(context, register);
  }

  static void navigateToSessions(BuildContext context) {
    Navigator.pushNamed(context, sessions);
  }

  static void navigateToExhibition(BuildContext context) {
    Navigator.pushNamed(context, exhibition);
  }

  static void navigateToCourses(BuildContext context) {
    Navigator.pushNamed(context, courses);
  }

  static void navigateToNews(BuildContext context) {
    Navigator.pushNamed(context, news);
  }

  static void navigateToDigitalLibrary(BuildContext context) {
    Navigator.pushNamed(context, digitalLibrary);
  }

  static void navigateToExhibitors(BuildContext context) {
    Navigator.pushNamed(context, exhibitors);
  }

  static void navigateToARExperience(BuildContext context) {
    Navigator.pushNamed(context, arExperience);
  }

  static void navigateToAIAssistant(BuildContext context) {
    Navigator.pushNamed(context, aiAssistant);
  }

  static void navigateToSocialNetwork(BuildContext context) {
    Navigator.pushNamed(context, socialNetwork);
  }

  static void navigateToAnalytics(BuildContext context) {
    Navigator.pushNamed(context, analytics);
  }

  // Quick access methods
  static List<Map<String, dynamic>> getQuickAccessItems() {
    return [
      {
        'title': 'الجلسات العلمية',
        'icon': Icons.event_available,
        'route': sessions,
        'color': 0xFF2196F3, // Blue
      },
      {
        'title': 'المعرض المصاحب',
        'icon': Icons.store,
        'route': exhibition,
        'color': 0xFF4CAF50, // Green
      },
      {
        'title': 'الدورات التدريبية',
        'icon': Icons.school,
        'route': courses,
        'color': 0xFFFF9800, // Orange
      },
      {
        'title': 'أخبار المؤتمر',
        'icon': Icons.newspaper,
        'route': news,
        'color': 0xFFE91E63, // Pink
      },
      {
        'title': 'المكتبة الرقمية',
        'icon': Icons.library_books,
        'route': digitalLibrary,
        'color': 0xFF9C27B0, // Purple
      },
      {
        'title': 'الواقع المعزز',
        'icon': Icons.view_in_ar,
        'route': arExperience,
        'color': 0xFFE91E63, // Red
      },
      {
        'title': 'المساعد الذكي',
        'icon': Icons.smart_toy,
        'route': aiAssistant,
        'color': 0xFF4CAF50, // Green
      },
      {
        'title': 'الشبكة الاجتماعية',
        'icon': Icons.people_alt,
        'route': socialNetwork,
        'color': 0xFFFF9800, // Orange
      },
      {
        'title': 'دليل العارضين',
        'icon': Icons.business,
        'route': exhibitors,
        'color': 0xFF607D8B, // Blue Grey
      },
      {
        'title': 'الإحصائيات',
        'icon': Icons.analytics,
        'route': analytics,
        'color': 0xFF2196F3, // Blue
      },
    ];
  }

  // Get advanced features
  static List<Map<String, dynamic>> getAdvancedFeatures() {
    return [
      {
        'title': 'تجربة الواقع المعزز',
        'description': 'تعلم تفاعلي ثلاثي الأبعاد',
        'icon': Icons.view_in_ar,
        'route': arExperience,
        'color': 0xFFE91E63,
        'isNew': true,
      },
      {
        'title': 'المساعد الذكي',
        'description': 'مساعد ذكي للإجابة على استفساراتك',
        'icon': Icons.smart_toy,
        'route': aiAssistant,
        'color': 0xFF4CAF50,
        'isNew': true,
      },
      {
        'title': 'الشبكة الاجتماعية',
        'description': 'تواصل مع المشاركين والخبراء',
        'icon': Icons.people_alt,
        'route': socialNetwork,
        'color': 0xFFFF9800,
        'isNew': false,
      },
      {
        'title': 'التحليلات المتقدمة',
        'description': 'إحصائيات وتحليلات شاملة',
        'icon': Icons.analytics,
        'route': analytics,
        'color': 0xFF2196F3,
        'isNew': false,
      },
    ];
  }

  // Get main navigation items
  static List<Map<String, dynamic>> getMainNavigationItems() {
    return [
      {
        'title': 'الرئيسية',
        'icon': Icons.home,
        'route': home,
      },
      {
        'title': 'المؤتمر',
        'icon': Icons.event,
        'route': conference,
      },
      {
        'title': 'المتحدثون',
        'icon': Icons.people,
        'route': speakers,
      },
      {
        'title': 'الجدول',
        'icon': Icons.schedule,
        'route': agenda,
      },
      {
        'title': 'الملف الشخصي',
        'icon': Icons.person,
        'route': profile,
      },
    ];
  }
}
