const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedGamification() {
  console.log('🎮 Seeding gamification data...');

  // Create badges
  const badges = [
    {
      name: 'welcome_badge',
      nameAr: 'مرحباً بك',
      nameEn: 'Welcome',
      description: 'Welcome to IDEC 2025! Earned by completing your profile.',
      icon: '👋',
      color: '#4CAF50',
      criteria: {
        type: 'profile_completion',
        description: 'Complete your profile information'
      }
    },
    {
      name: 'session_explorer',
      nameAr: 'مستكشف الجلسات',
      nameEn: 'Session Explorer',
      description: 'Attended your first session.',
      icon: '🎯',
      color: '#2196F3',
      criteria: {
        type: 'session_attendance',
        count: 1,
        description: 'Attend at least 1 session'
      }
    },
    {
      name: 'knowledge_seeker',
      nameAr: 'باحث المعرفة',
      nameEn: 'Knowledge Seeker',
      description: 'Attended 5 sessions.',
      icon: '📚',
      color: '#FF9800',
      criteria: {
        type: 'session_attendance',
        count: 5,
        description: 'Attend at least 5 sessions'
      }
    },
    {
      name: 'conference_champion',
      nameAr: 'بطل المؤتمر',
      nameEn: 'Conference Champion',
      description: 'Attended 10 sessions.',
      icon: '🏆',
      color: '#FFD700',
      criteria: {
        type: 'session_attendance',
        count: 10,
        description: 'Attend at least 10 sessions'
      }
    },
    {
      name: 'social_butterfly',
      nameAr: 'الفراشة الاجتماعية',
      nameEn: 'Social Butterfly',
      description: 'Connected with 5 other participants.',
      icon: '🦋',
      color: '#E91E63',
      criteria: {
        type: 'social_connections',
        count: 5,
        description: 'Connect with at least 5 participants'
      }
    },
    {
      name: 'networking_champion',
      nameAr: 'بطل التواصل',
      nameEn: 'Networking Champion',
      description: 'Connected with 10 other participants.',
      icon: '🤝',
      color: '#9C27B0',
      criteria: {
        type: 'social_connections',
        count: 10,
        description: 'Connect with at least 10 participants'
      }
    },
    {
      name: 'course_graduate',
      nameAr: 'خريج الدورات',
      nameEn: 'Course Graduate',
      description: 'Completed your first course.',
      icon: '🎓',
      color: '#3F51B5',
      criteria: {
        type: 'course_completion',
        count: 1,
        description: 'Complete at least 1 course'
      }
    },
    {
      name: 'lifelong_learner',
      nameAr: 'المتعلم مدى الحياة',
      nameEn: 'Lifelong Learner',
      description: 'Completed 3 courses.',
      icon: '📖',
      color: '#607D8B',
      criteria: {
        type: 'course_completion',
        count: 3,
        description: 'Complete at least 3 courses'
      }
    },
    {
      name: 'early_bird',
      nameAr: 'الطائر المبكر',
      nameEn: 'Early Bird',
      description: 'Registered early for the conference.',
      icon: '🐦',
      color: '#795548',
      criteria: {
        type: 'early_registration',
        description: 'Register before early bird deadline'
      }
    },
    {
      name: 'feedback_provider',
      nameAr: 'مقدم التغذية الراجعة',
      nameEn: 'Feedback Provider',
      description: 'Provided ratings for 5 sessions.',
      icon: '⭐',
      color: '#FFC107',
      criteria: {
        type: 'rating_given',
        count: 5,
        description: 'Rate at least 5 sessions'
      }
    },
    {
      name: 'point_collector',
      nameAr: 'جامع النقاط',
      nameEn: 'Point Collector',
      description: 'Earned 100 points.',
      icon: '💎',
      color: '#00BCD4',
      criteria: {
        type: 'total_points',
        points: 100,
        description: 'Earn at least 100 points'
      }
    },
    {
      name: 'point_master',
      nameAr: 'سيد النقاط',
      nameEn: 'Point Master',
      description: 'Earned 500 points.',
      icon: '👑',
      color: '#FF5722',
      criteria: {
        type: 'total_points',
        points: 500,
        description: 'Earn at least 500 points'
      }
    }
  ];

  // Create badges
  for (const badgeData of badges) {
    try {
      await prisma.badge.upsert({
        where: { name: badgeData.name },
        update: badgeData,
        create: badgeData
      });
      console.log(`✅ Created/Updated badge: ${badgeData.nameEn}`);
    } catch (error) {
      console.error(`❌ Error creating badge ${badgeData.nameEn}:`, error.message);
    }
  }

  console.log('🎮 Gamification seeding completed!');
}

module.exports = { seedGamification };

// Run if called directly
if (require.main === module) {
  seedGamification()
    .catch((e) => {
      console.error('❌ Error seeding gamification:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
