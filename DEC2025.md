
### خطة وتفاصيل تطبيق مؤتمر ومعرض طب الأسنان




التقنيات المطلوبة:
واجهة المستخدم (Frontend): Flutter
قاعدة البيانات: PostgreSQL
Backend/API: RESTful API باستخدام Node.js أو Django أو أي تقنية مناسبة


## أولاً: البنية العامة وتصنيف المستخدمين



### لتوفير تجربة مخصصة وفعالة، يتم تصنيف المستخدمين وتحديد الواجهات الرئيسية للتطبيق:


1. أنواع المستخدمين

المشارك (Participant): يمتلك ملفًا شخصيًا كاملاً، يمكنه حضور الجلسات، التفاعل المباشر، الوصول للمحتوى (حسب الصلاحيات)، وحساب ساعات التعليم الطبي المستمر (CME). يُمكنه التسجيل في المؤتمر والاشتراك في الدورات المصاحبة.


#### تصنيف المشاركين بناءً على الفئات الرئيسية والمؤهلات/التخصصات ويمكن التحكم فيها وتعديل الخيارات :



#### المجموعات الرئيسية:



#### الطلاب:

مثال:
العدد المستهدف: 300
تكلفة الاشتراك: 5000

حالة الاشتراك: يمكن فتح الاشتراك أو إيقاف الاشتراك لهذه الفئة ككل من لوحة الإدارة.
الملاحظات: سيتم تحديد صلاحية الاشتراك بناءً على المؤهل/السنة الدراسية.


#### الأطباء:

مثال:
العدد المستهدف: 1300
تكلفة الاشتراك: 10000

حالة الاشتراك: يمكن فتح الاشتراك أو إيقاف الاشتراك لهذه الفئة ككل من لوحة الإدارة.
الملاحظات: سيتم تحديد صلاحية الاشتراك بناءً على المؤهل/التخصص.


#### المجموعات الفرعية (بناءً على المؤهلات والتخصصات):



#### الطلاب:


سنة أولى: غير مسموح لهم بالاشتراك.
سنة خامسة: مسموح لهم بالاشتراك.


#### الأطباء:


ممارس: مسموح له بالاشتراك.
ماجستير: مسموح له بالاشتراك.
مساعد طبيب: غير مسموح له بالاشتراك.


### الزائر/المتصفح: يمكنه استعراض جدول الأعمال ومعلومات العارضين والدورات، لكن لا يمكنه التسجيل في المؤتمر أو الدورات، أو الوصول للميزات التفاعلية الكاملة.


المتحدث (Speaker): يمتلك ملفًا شخصيًا خاصًا، ويُمكنه إدارة جلساته والرد على أسئلة المشاركين.

المسؤول (Admin): يمتلك صلاحيات مختلفة بناءً على دوره (مثل: لجنة القبول، المحاسب، اللجنة العلمية، إدارة المعرض، اللجنة الإعلامية، المشرف، مدير النظام).

2. الواجهات الرئيسية


### تطبيق المستخدم (Main App): وهو الواجهة الأساسية المخصصة لجميع المشاركين (الأطباء، الطلاب، الزوار)، بالإضافة إلى المتحدثين والعارضين لإدارة محتواهم والتفاعل.



### تطبيق الإدارة (Admin App/Dashboard): لوحة تحكم مركزية مخصصة للمنظمين واللجان الإدارية المختلفة لإدارة جميع جوانب المؤتمر والمعرض.



## ثانياً: تطبيق المستخدم (Main App) - الميزات والتفاصيل


يركز هذا القسم على توفير تجربة غنية وتفاعلية للمشاركين والمتحدثين والعارضين.

1. التسجيل والدخول


#### تسجيل حساب جديد:


سهولة وأمان التسجيل: عملية إنشاء حساب بسيطة وسهلة، مع إمكانية التسجيل عبر حسابات التواصل الاجتماعي (اختياري).

التحقق من رقم الهاتف (OTP): تعزيز أمان الحساب وضمان صحة بيانات المستخدمين عبر رمز تحقق يُرسل إلى رقم الهاتف.

البيانات الأساسية ورفع الوثائق: يتطلب التسجيل بالمؤتمر إدخال البيانات الأساسية (الاسم العربي والإنجليزي، تاريخ الميلاد، المحافظة، المؤهل، سنة التخرج، الجامعة/مكان العمل)، مع خيار رفع وثيقة المؤهل (إجباري/اختياري حسب إعدادات الإدارة).

الملف الشخصي (Profile): يُمكن للمشارك تعديل بياناته، إضافة صورة شخصية، وتحديد اهتماماته وتخصصه الدقيق.


### خيار التعديل على البيانات الشخصية: ستتحكم الإدارة في السماح أو عدم السماح بتعديل بيانات معينة (مثل الاسم والمؤهل) بعد التسجيل. كما يمكن تفعيل خيار "السماح بعد المراجعة"، حيث يقوم المشترك بتعديل بياناته وتُرسل للجنة القبول لمراجعتها والموافقة عليها قبل تطبيق التغيير، مما يضمن دقة البيانات اللازمة لإصدار الشهادات.



#### إظهار زر الاشتراك بالمؤتمر وشروط السماح به:


يظهر عداد للوقت المتبقي لفتح الاشتراك بالمؤتمر في واجهة المستخدم.

يظهر زر الاشتراك في المؤتمر فقط للمشتركين المسموح لهم بالاشتراك، وذلك بناءً على جدول المؤهلات والتخصصات المحددة من قبل الإدارة (المجموعات الرئيسية والفرعية).


#### حالة طلب الاشتراك بعد الإرسال:



### بعد إرسال بيانات الاشتراك، تظهر حالة الطلب للمشترك في التطبيق كـ "بانتظار المراجعة" (أو ما يعادل "تحت المراجعة" في قوائم الإدارة).


سجل المدفوعات والاشتراكات: صفحة خاصة تعرض جميع المدفوعات والاشتراكات الخاصة بكل مستخدم.

e-badge: صفحة خاصة يتم تفعيلها بعد إتمام عملية الدفع وتأكيد الاشتراك.


#### توليد كود QR للدخول:



### يتم توليد كود QR فريد لكل مشارك تم قبوله ودفع الاشتراك بنجاح، ويظهر الكود في ملف المشارك داخل التطبيق.



#### التحقق من التذاكر وتسجيل الحضور:


نظام مسح سريع لأكواد QR عند بوابات الدخول للمؤتمر والمعرض لتسجيل الحضور.

تسجيل آلي لوقت الدخول والخروج للمشاركين في المؤتمر والمعرض.

وضع عدم الاتصال (Offline Mode): إمكانية مسح أكواد QR وتسجيل الحضور حتى في حال عدم توفر الإنترنت، مع مزامنة البيانات تلقائيًا عند استعادة الاتصال.

2. جدول أعمال المؤتمر التفاعلي والذكي


#### تصفية متقدمة:


إمكانية التصفية حسب التخصص الدقيق (تقويم، زراعة، تجميل، أطفال، إلخ).

التصفية حسب نوع الجلسة (محاضرة، ورشة عمل، عرض تقديمي، جلسة بوستر).

التصفية حسب المتحدث، الكلمات المفتاحية (عنوان الجلسة، وصفها)، والقاعة/المكان.


#### جدولي الشخصي (My Agenda):


إمكانية إضافة الجلسات والفعاليات المفضلة بنقرة واحدة.

ضبط تنبيهات مخصصة لكل جلسة (قبل 15 دقيقة، 30 دقيقة، إلخ).

عرض الجلسات المضافة بترتيب زمني واضح.

مزامنة مع التقويم: إمكانية تصدير جلسة واحدة أو الجدول الشخصي بالكامل إلى تقويم الهاتف (Google Calendar, Apple Calendar) بنقرة واحدة.


#### تقييم الجلسات الفوري:


نظام تقييم بسيط (نجوم 1-5 أو إعجاب/عدم إعجاب) يظهر تلقائيًا بعد انتهاء الجلسة، مع إمكانية إضافة تعليقات نصية قصيرة.


#### تحديثات حية:


إشعارات فورية (Push Notifications) بأي تغييرات في الجدول، إلغاء جلسات، أو إعلانات هامة.

قسم خاص "أخبار المؤتمر" لعرض آخر التحديثات.


#### حضور الجلسات بنظام QR تفصيلي:


لكل جلسة رمز QR فريد لتسجيل الحضور.

تسجيل وقت الدخول والخروج من كل جلسة.

احتساب ساعات الحضور بدقة، مما يتيح إصدار تقارير حضور للمشارك ولكل جلسة.

3. الدورات والورش المصاحبة للمعرض


#### تصفح الدورات:


قائمة شاملة بالدورات التخصصية المتاحة مع تفاصيل كاملة (اسم الدورة، وصف، محاور، مدة، تاريخ، مكان، اسم المحاضر، السعر).

صور وفيديوهات توضيحية لكل دورة.


#### الاشتراك والدفع:


نظام اشتراك مباشر في الدورات ذات الرسوم، مع ربط آمن ببوابات دفع إلكترونية.

إدارة حالة الاشتراك (قيد الانتظار، مدفوعة، مكتملة).

عرض الدورات التي اشترك فيها المستخدم في قسم خاص.


### إلغاء الاشتراك واسترداد الرسوم: يُمكن للمستخدم إلغاء اشتراكه وطلب استرداد الرسوم، بشرط أن يكون ذلك قبل بداية الدورة المحددة. يجب توضيح سياسة الإلغاء والاسترداد بوضوح داخل التطبيق (مثل فترة الإلغاء المسموح بها، نسبة الاسترداد).


إشعارات الدورات: ستكون هناك تنبيهات خاصة بالدورات التي اشترك فيها المستخدم، مثل تذكير بقرب موعد الدورة، أو أي تحديثات عليها.

4. ملفات المتحدثين الشاملة


#### صفحات شخصية غنية للمتحدثين:


السيرة الذاتية المفصلة، صور عالية الجودة، وروابط لحساباتهم المهنية (LinkedIn, ResearchGate, ORCID).

قائمة واضحة بالجلسات التي سيقدمونها مع التواريخ والأوقات، وإمكانية عرض تقييمات المتحدث.


### التواصل المباشر: خاصية إرسال رسائل نصية للمتحدثين (بعد موافقتهم المسبقة على استقبال الرسائل) للمشاركين المقبولين فقط. يجب أن يكون هناك سجل واضح للرسائل المرسلة والمستلمة داخل التطبيق لسهولة المتابعة.


5. الخريطة التفاعلية لمكان المؤتمر

تمييز الأماكن الهامة: تحديد أماكن ورش العمل، مناطق الاستراحة، نقاط الطعام، الإسعافات الأولية، دورات المياه، ونقاط الشحن.


#### معلومات المؤتمر واللجان:


صفحة خاصة بمعلومات عامة عن المؤتمر وصفحات التواصل الرسمية.

صفحات تعريفية بأعضاء اللجنة التحضيرية، اللجنة الإعلامية، واللجنة العلمية.


## ثالثاً: ميزات مبتكرة ومميزة لطب الأسنان



### هذه الميزات تعزز القيمة العلمية والتفاعلية للتطبيق.


1. المكتبة الرقمية للمحتوى العلمي

ملخصات الأبحاث (Abstracts): إمكانية الوصول إلى ملخصات الأبحاث المقدمة في المؤتمر، مع خاصية التنزيل بصيغة PDF.


### العروض التقديمية (Presentations): بعد موافقة المتحدثين، إتاحة عروضهم التقديمية للمشاهدة أو التنزيل بعد انتهاء الجلسة، مع إمكانية المشاهدة داخل التطبيق.


مواد إضافية: فيديوهات توضيحية من المتحدثين أو العارضين، مقالات ذات صلة، أو أدلة إرشادية.


#### صلاحيات الوصول للمحتوى: سيتم تحديد صلاحيات الوصول للمحتوى عبر جدول تتحكم فيه الإدارة:


متاح للجميع: (مثل معلومات عامة عن المؤتمر، بعض ملخصات الأبحاث العامة).

متاح للمشاركين المسجلين: (مثل معظم ملخصات الأبحاث، بعض العروض التقديمية).

متاح للمشتركين في المؤتمر: (جميع المحتوى العلمي للمؤتمر، باستثناء الدورات المدفوعة).

حصري للمسجلين بالدورة: (المحتوى الخاص بالدورات المدفوعة).


### ملاحظات رقمية: أداة مدمجة لتدوين الملاحظات مباشرة أثناء الجلسات، مع إمكانية ربط الملاحظات بجلسات محددة أو عروض تقديمية، وحفظها بشكل شخصي داخل التطبيق.


2. التفاعل والمشاركة المباشرة


### أسئلة وأجوبة مباشرة (Live Q&A): نظام لطرح الأسئلة على المتحدثين أثناء الجلسات مباشرة عبر التطبيق، مع إمكانية التصويت على الأسئلة الأكثر أهمية لترتيبها وعرضها على المتحدث.


استطلاعات رأي فورية (Live Polls): إشراك الحضور في استطلاعات رأي سريعة حول مواضيع النقاش في الجلسات، مع عرض النتائج الفورية.

نظام تقييم المحاضرين والجلسات: جمع تغذية راجعة فورية بعد كل جلسة بشكل منفصل للمحاضر والجلسة.

جدار الأفكار / لوحة النقاش: منصة مفتوحة للمشاركين لطرح أفكار، أسئلة عامة، أو حتى فرص عمل وتدريب، مع إمكانية التفاعل (إعجاب، تعليق) وتصفية المحتوى حسب المواضيع.

تحديات سريعة (Flash Challenges): الإعلان عن تحديات قصيرة ذات صلة بالمؤتمر أو طب الأسنان، مع جوائز صغيرة، لتحفيز التفاعل (مثلاً: "أفضل صورة مع المتحدث"، "أفضل نصيحة سريعة").


### مسابقات تفاعلية مباشرة (Interactive Quizzes): خلال الجلسات الكبرى أو في فترات الاستراحة، يمكن عرض أسئلة تفاعلية على الشاشات الكبيرة ويجيب عليها الحضور عبر التطبيق.


3. شبكات تواصل احترافية موجهة

ملفات شخصية للحضور (Attendees Profile): إمكانية التحكم في خصوصية المعلومات الظاهرة (الاسم، التخصص، بلد العمل، الاهتمامات) وعرض نبذة شخصية.

بحث متقدم عن الحضور: إمكانية البحث عن زملاء أو أطباء بناءً على التخصص، الاهتمامات المشتركة، أو الدولة.

طلب تواصل (Connection Request): نظام مشابه لـ LinkedIn لإرسال طلبات تواصل مع حضور آخرين، وإدارة طلبات التواصل (قبول/رفض).

اقتراحات تواصل ذكية: بناءً على الاهتمامات المشتركة، الجلسات التي حضرها المستخدم، أو التخصصات المتشابهة.

غرف محادثة خاصة: لتمكين مجموعات صغيرة من التواصل المباشر بعد قبول طلبات التواصل.

دردشة جماعية: غرف دردشة لكل جلسة أو موضوع محدد (مثلاً: "نقاشات حول زراعة الأسنان الحديثة").

4. التلعيب (Gamification) لزيادة التفاعل

نظام نقاط ومكافآت: منح نقاط للمشاركة (حضور الجلسات، طرح أسئلة، تقييم، زيارة أجنحة العارضين، التواصل مع آخرين). نقاط إضافية للمهام الخاصة (مثل إكمال استبيان). يمكن منح جوائز عشوائية أو خصومات من العارضين.

لوحة الصدارة (Leaderboard): عرض المستخدمين الأكثر تفاعلاً وترتيبهم بناءً على النقاط المكتسبة.

شارات (Badges): منح شارات افتراضية عند إكمال مهام معينة (مثلاً: "خبير التواصل" لمن يتواصل مع عدد معين من الحضور، "المهتم" لمن يطرح عدد من الأسئلة، "الباحث النشط" لمن يحضر عددًا كبيرًا من الجلسات، "مستكشف المعرض" لزيارة عدد معين من الأجنحة).


### "البحث عن الكنز" الرقمي (Digital Scavenger Hunt): تشجيع الحضور على زيارة أجنحة معينة أو حضور جلسات محددة للحصول على أكواد سرية أو إجابات لأسئلة، وإدخال الأكواد/الإجابات في التطبيق للتأهل لجوائز.



## رابعاً: ميزات إضافية لتعزيز التجربة



### هذه الميزات تُكمل التجربة الشاملة للمشاركين وتُحسن من فعاليات المؤتمر.


1. دليل المرافق والمحلات المتعاونة

صفحة مفصلة لكل مرفق: تتضمن الاسم، الموقع على الخريطة التفاعلية، تفاصيل العروض أو الخصومات الخاصة بالمؤتمر، وصور توضيحية. يمكن تقسيمها إلى مجموعات رئيسية (مطاعم، فنادق، مراكز تسوق، إلخ).

2. أخبار المؤتمر

قسم مخصص لتحديثات وأخبار المؤتمر بشكل مستمر (افتتاح، فعاليات خاصة، إغلاق).

3. معرض صور وفيديو

مشاركة صور وفيديوهات عالية الجودة من فعاليات المؤتمر.

إمكانية رفع صور من قبل المشاركين (بعد مراجعة المشرف/اللجنة الإعلامية).

معرض فيديو لأبرز اللحظات والمقابلات.

4. استطلاعات ما بعد المؤتمر

استبيانات مفصلة لجمع آراء الحضور حول تجربتهم بشكل عام، الجلسات، التنظيم، واقتراحاتهم للتحسين، مع تحليل البيانات لتقارير إحصائية.

5. شهادات الحضور الرقمية وتتبع CME


### إمكانية تحميل شهادة الحضور: مباشرة من التطبيق بعد انتهاء المؤتمر واستيفاء شروط الحضور.


شهادات قابلة للتحقق: عبر كود QR أو رابط تحقق.

تتبع ساعات التعليم الطبي المستمر (CME): حساب الساعات المكتسبة تلقائيًا بناءً على حضور الجلسات المعتمدة، مع عرض سجل بساعات CME في ملف المشارك (مع إمكانية التحكم من قبل الإدارة في إظهار أو إخفاء هذه الساعات).

نظام حضور الجلسات التفصيلي: لكل مستخدم، يتم إنشاء "سجل حضور الجلسات" يحتوي على اسم الجلسة/الورشة، التاريخ والوقت، وقت الدخول/الخروج، وعدد الدقائق التي تم الحضور فيها (لحساب CME بدقة). يمكن للإدارة استخراج تقرير حضور لكل جلسة/ورشة/مشارك.

6. دعم فني مباشر

قسم للمساعدة والأسئلة الشائعة (FAQ).

قناة للدردشة المباشرة (Live Chat) مع فريق الدعم الفني.

نموذج لتقديم الشكاوى أو الاستفسارات.

7. مركز الإشعارات

نقطة مركزية لجميع الرسائل المرسلة من الإدارة والإشعارات التي تم إرسالها للمشترك، مع إمكانية الفلترة حسب نوع الرسالة (SMS/WhatsApp/In-App Notification).

8. دعم متعدد اللغات


### واجهة التطبيق والمحتوى باللغتين العربية والإنجليزية (مع إمكانية إضافة لغات أخرى مستقبلاً)، وإمكانية التبديل بين اللغات بسهولة.


9. الوضع الليلي (Dark Mode)

خيار لتفعيل الوضع الليلي (تلقائي/يدوي)، مع تصميم مخصص للطابع الطبي وهوية المؤتمر البصرية (أسود/رمادي/أحمر طبي)، ودعم الوضع الليلي لجميع أنواع المحتوى.


## خامساً: تطبيق الإدارة (Admin App/Dashboard) - التحكم والإدارة المركزية



### تطبيق الإدارة هو لوحة تحكم مركزية لإدارة جميع جوانب المؤتمر والمعرض، مع صلاحيات مختلفة لكل دور لضمان أعلى مستويات الكفاءة والتحكم.


لوحة تحكم تفاعلية: توفر إحصاءات وبيانات شاملة وتفاعلية.

الصلاحيات: تظهر الشاشات المناسبة للمستخدم حسب صلاحياته.

توثيق العمليات (Log): يتم توثيق جميع العمليات التي تتم في النظام.

إدارة المستخدمين والصلاحيات

إنشاء وإدارة القوائم الرئيسية للمشتركين (جدول حالة المشترك): سيتم إنشاء نظام قوائم رئيسية متسلسل لتتبع حالة المشترك في جميع مراحل التسجيل والدفع. كل قائمة سيكون لها معرف (ID) خاص.


#### القوائم الأساسية:



### 0 / المسجلين بالتطبيق (افتراضي): جميع المستخدمين الذين قاموا بإنشاء حساب في التطبيق.


1 / "طلبات الاشتراك": المستخدمون الذين قاموا بتسجيل طلب الاشتراك في المؤتمر.

2 / "فحص وثائق" / "تم تأكيد الوثائق بانتظار الدفع": المستخدمون الذين تم تأكيد وثائقهم من قبل لجنة القبول وينتظرون إتمام عملية الدفع.

3 / "المشتركين بالمؤتمر": المستخدمون الذين قاموا بتسديد الاشتراك بنجاح وأصبحوا مشتركين فعالين في المؤتمر.

4 / "المعلقين": قائمة يتم تحويل الأشخاص إليها تلقائياً الذين تمت الموافقة على وثائقهم وانتهت مهلة انتظار الدفع الخاصة بهم. يتم إيقاف خاصية الدفع لديهم عند التحويل لهذه القائمة.

5 / "طلبات إعادة تنشيط": المستخدمون الذين طلبوا إعادة تنشيط خيار الدفع بعد أن تم تحويلهم إلى قائمة "المعلقين".

6 / "الانتظار": قائمة انتظار يُمكن تحويل المشتركين إليها في حال اكتمال العدد المطلوب أو لأسباب أخرى، مع إمكانية استيعابهم لاحقاً في حال توفر مقاعد.

7 / "المرفوضين": المستخدمون الذين لم يتم قبول وثائقهم أو طلبات اشتراكهم.

إضافة/حذف/تعديل المستخدمين: إدارة حسابات المسؤولين (مدير النظام، المشرف، لجنة القبول، المحاسب، اللجنة العلمية، إدارة المعرض، اللجنة الإعلامية).


#### تحديد الصلاحيات: تعيين صلاحيات دقيقة لكل دور:


مدير النظام (Super Admin): صلاحيات كاملة على النظام.

مشرف عام: صلاحيات إشرافية على مستوى اللجان (لجنة القبول، المحاسب، اللجنة العلمية، اللجنة الإعلامية).

اللجنة التحضيرية: صلاحيات اطلاع واستعراض للمشتركين والحضور والتقارير العامة، والتحكم بالملف الشخصي.


#### لجنة القبول (Admission Committee):


استعراض بيانات المشتركين ووثائقهم المرفوعة، مع إمكانية الفلترة حسب (التخصص، المحافظة، تاريخ الميلاد، تاريخ التخرج).

تصنيف طلبات الاشتراك حسب جدول تصنيف طلبات المشتركين (جديد، تحت المراجعة، تم تأكيد الوثائق - بانتظار الدفع، مشترك - تم الدفع، رفض البيانات، تحت الانتظار).

القبول أو الرفض مع إشعار للمشترك.

إمكانية تعديل تخصص المشترك وتحويل من (طبيب إلى طالب أو العكس).

توثيق العمليات: كل عملية (قبول/رفض/تعديل، إلخ) توثق بسجل (Log) بالتاريخ والوقت والمستخدم والمشترك والعملية والرسالة المرسلة.


#### الصلاحيات على مستوى القوائم: تتمتع لجنة القبول بصلاحيات استعراض و تحكم في القوائم التالية، بالإضافة إلى صلاحية التحويل من قائمة إلى أخرى:


تأكيد الوثائق: تحويل المشتركين من قائمة "طلبات الاشتراك" إلى قائمة "فحص وثائق" / "تم تأكيد وثائقهم وبانتظار الدفع".

رفض الوثائق: تحويل المشتركين من أي قائمة (عادةً "طلبات الاشتراك" أو "فحص وثائق") إلى قائمة "المرفوضين".

طلب تصحيح بيانات: في حال وجود خطأ في بيانات المشترك أو كانت غير واضحة أو لم يتم إرفاق البيانات بشكل صحيح، يمكن للجنة القبول طلب تصحيح البيانات. يتم إرسال إشعار للمشترك بضرورة التعديل، مع إمكانية كتابة نص الإشعار أو وصف المشكلة. يقوم المشترك بتعديل البيانات وإرسالها مرة أخرى للمراجعة.

تنشيط الدفع: تفعيل خيار الدفع لمشترك معين في قائمة "المعلقين" أو "الانتظار" (مما ينقله إلى قائمة "فحص وثائق" أو ما يعادلها في انتظار الدفع)، ويتم ذلك عادةً بعد طلب "إعادة تنشيط" من المشترك.

تحويل لقائمة الانتظار: تحويل المشتركين إلى قائمة "الانتظار".

مؤقت الرد على الطلب: عند تقديم طلب الاشتراك، يبدأ عد تنازلي داخلي (مثلاً: 48 ساعة). يظهر الوقت المتبقي لكل طلب داخل لوحة لجنة القبول.


#### آلية التنبيه:


تنبيه بصري: تغيير لون الطلب في قائمة لجنة القبول (مثل الأحمر) عند تجاوز المهلة.

إشعارات داخلية: إرسال إشعارات مباشرة للمسؤولين في لجنة القبول (Push Notifications / In-App Notifications) عندما يقترب طلب من تجاوز المهلة أو يتجاوزها.

تقارير دورية: إنشاء تقارير يومية أو أسبوعية للطلبات التي تجاوزت المهلة الزمنية ولم تتم معالجتها.


#### المحاسب (Accountant):



#### الاطلاع على بيانات الدفع وتقارير المدفوعات:


يمكن للمحاسب استعراض جميع المدفوعات المتعلقة بالمؤتمر والدورات، مع إمكانية الفلترة والبحث.

الاطلاع على التقارير المالية التفصيلية، مع القدرة على تصديرها بصيغة PDF / Excel.


#### انشاء سندات قبض يدوية:


في حال الدفع النقدي أو التحويلات البنكية المباشرة، يمكن للمحاسب تسجيل السند يدويًا من خلال لوحة التحكم.

تفاصيل السند اليدوي: يتضمن إدخال اسم المشترك، المبلغ، طريقة الدفع (نقد/تحويل بنكي)، (إذا كان تحويلاً: اسم شبكة التحويل)، رقم السند/الحوالة، غرض الدفع (اشتراك مؤتمر / اشتراك دورة - مع جلب أسعار الدورات وأسمائها تلقائياً)، ملاحظات، اسم المحاسب، وتوقيت العملية.

يتم توليد رقم سند قبض تلقائي، قابل للطباعة بصيغة PDF أو الإرسال للمشارك عبر البريد الإلكتروني.

دعم خاصية إرفاق صورة من إيصال الدفع أو سند القبض الورقي كمرجع.

تنبيه تكرار الحوالة: سيتم تنبيه المحاسب في حال تكرار رقم الحوالة المدخل لتجنب الأخطاء.

نظام الدفع الإلكتروني وتفعيل الاشتراكات


#### تفعيل خيار الدفع بعد القبول (للمؤتمر):



### عند تقديم المشترك لطلب الاشتراك بالمؤتمر، يكون خيار الدفع في تطبيق المستخدم غير مُفعّل في واجهة المستخدم لديه.



### بعد مراجعة وقبول الوثائق من قبل لجنة القبول، يتم إرسال تنبيه للمشترك (عبر إشعار داخل التطبيق و/أو SMS/WhatsApp) بقبول وثائقه.


يتضمن التنبيه مطالبة المشترك بالدفع لإكمال وتفعيل الاشتراك، وفي هذه المرحلة يتم تفعيل خيار الدفع في واجهته.


#### إنشاء السند الإلكتروني التلقائي وتفعيل الاشتراك:


للمؤتمر: عند إتمام عملية الدفع بنجاح عبر بوابة الدفع الإلكترونية واستلام التأكيد من البوابة، يتم إنشاء سند إلكتروني تلقائي في نظام المحاسبة. هذا السند يتضمن جميع التفاصيل المهمة: اسم المشترك، بوابة الدفع المستخدمة، رقم العملية الفريد من بوابة الدفع، المبلغ المدفوع، عملة الدفع، غرض الدفع (اشتراك مؤتمر)، وتاريخ ووقت العملية.

في حالة انشاء السند الالكتروني للمستخدم لغرض الإشتراك بالمؤتمر يتم تحويل المشترك الى مشترك فعال ..

للدورات المدفوعة: وبالمثل، عند الاشتراك في أي من الدورات المدفوعة والدفع عن طريق بوابة الدفع، يتم إنشاء سند إلكتروني تلقائي بعد استلام التأكيد من بوابة الدفع، متضمناً نفس التفاصيل المذكورة أعلاه (مع تعديل غرض الدفع إلى "اشتراك دورة [اسم الدورة]").

مكانية استعراض التقارير المالية على مستوى بوابة الدفع لتتبع الإيرادات القادمة من كل بوابة على حدة.

تقارير الدورات: يمكن للمسؤولين استعراض كل دورة بشكل منفصل لمعرفة عدد المشتركين فيها وإجمالي المبلغ الذي تم تحصيله لكل دورة.


#### مهلة انتظار الدفع:


يمكن تفعيل أو إيقاف مهلة انتظار الدفع من قبل المشرف.

يتم تحديد المدة الزمنية المسموحة للدفع للمشتركين الذين تم قبول وثائقهم وتم تفعيل خيار الدفع لديهم.


### التحويل التلقائي عند انتهاء المهلة: إذا انتهت المهلة المحددة للدفع، يتم تحويل المشترك آليًا إلى قائمة "المعلقين" ويتم توقيف خاصية الدفع لديهم في التطبيق.



### خيار "إعادة تنشيط الدفع": يظهر للمشترك في تطبيق المستخدم خيار "طلب إعادة تنشيط الدفع" بعد تحويله إلى قائمة "المعلقين"، مما يتيح له طلب إعادة تفعيل خيار الدفع.


تنبيه المشترك قبل انتهاء المهلة: يمكن تحديد وقت معين قبل انتهاء المهلة لإرسال تنبيه للمشترك.

رسالة التنبيه: يمكن تخصيص نص رسالة التنبيه التي تُرسل للمشترك.

خيارات الإرسال: يتم إرسال التنبيه عبر SMS / WhatsApp / In-App Notification.


#### مراجعة عمليات الدفع:


إمكانية مراجعة أي عملية دفع لأي مشترك للتحقق منها، من خلال البحث عن المشترك أو رقم العملية أو تاريخ الدفع، والاطلاع على تفاصيل السند الإلكتروني أو اليدوي.

اللجنة العلمية (Scientific Committee): إدارة أقسام الدورات والدورات نفسها وتفاصيلها، إضافة/تعديل/حذف المحاضرين ومعلوماتهم، إدارة جلسات المؤتمر (إضافة، تعديل، حذف، تحديد القاعات والأوقات، ربط المتحدثين)، وإدارة المحتوى العلمي (ملخصات الأبحاث، العروض التقديمية).

اللجنة الإعلامية (Media Committee): إضافة/تعديل/حذف الأخبار والصور والفيديوهات الخاصة بالمؤتمر، إدارة قسم معرض الصور والفيديو، إدارة الإشعارات والتحديثات الحية. ستتوفر واجهة خاصة للجنة الإعلامية لإنشاء وإدارة المسابقات والأسئلة التحفيزية والترويجية.

إدارة المعرض: إضافة الشركات، تحديد مواقعها على الخريطة، إدخال معلوماتها وعروضها. يمكن لإدارة المعرض تحديث الخريطة بشكل ديناميكي (مثل تغيير موقع جناح شركة ما).

المتحدث (Speaker) إدارة معلوماتهم ومحتواهم وعروضهم بشكل مباشر.

2. إدارة بيانات المؤتمر

إعدادات المؤتمر: تحديد السنة، تاريخ بداية ونهاية المؤتمر، وتكلفة الاشتراك لكل فئة (مع إضافة إمكانية تحديد تكلفة الاشتراك لكل مجموعة رئيسية: طلاب/أطباء).

تصنيف تخصصات المشتركين: إدارة وإنشاء وتصنيف تخصصات تحت مجموعات رئيسية (أطباء/طلاب)، مع ظهور زر الاشتراك حسب المجموعة والتخصصات المحددة.

تحديد العدد المستهدف لكل مجموعة رئيسية: يمكن للمسؤول تحديد العدد المستهدف للمشتركين لكل مجموعة (طلاب/أطباء).

إيقاف الاشتراك تلقائيًا: يتم إيقاف الاشتراك تلقائياً للمجموعة المعنية عند اكتمال العدد المطلوب للمشتركين فيها.


### تصنيف طلبات المشتركين: إدارة وتصنيف طلبات الاشتراك بالمؤتمر بناءً على القوائم الأساسية الجديدة (المسجلين بالتطبيق، طلبات الاشتراك، فحص وثائق، المشتركين بالمؤتمر، المعلقين، طلبات إعادة تنشيط، الانتظار، المرفوضين)


ارفاق الوثائق عند التسجيل: تحديد ما إذا كان رفع وثائق المؤهل إجباريًا أو اختياريًا للمشتركين الجدد أو لجميع المشتركين.

إنشاء شهادات إلكترونية: نظام آلي لإنشاء وتصدير الشهادات.


#### خيارات التحكم في ملف المشترك:


خيار لعرض/إخفاء ساعات الحضور في ملف المشترك.

خيار لتفعيل/عدم تفعيل تعديل بيانات المشترك (الاسم العربي/الإنجليزي، المؤهل) والذي قد يتطلب موافقة لجنة القبول لتفعيله.

خيار لتوقيف تعديل بيانات المشتركين بشكل دائم (خاصة قبل طباعة الشهادات الإلكترونية) لضمان دقة البيانات.

3. نظام الاتصال والرسائل


### جدول رسائل النظام والتطبيق: قوالب رسائل قابلة للتعديل (عند إنشاء الحساب، تأكيد الوثائق، رفض البيانات، اكتمال عدد المشتركين، إلخ).


خيارات الإرسال المتعددة: SMS / WhatsApp / In-App Notification.

إمكانية إرسال رسالة لمشترك فردي أو رسالة جماعية لمجموعات مصنفة (تحت المراجعة، بانتظار الدفع، مشتركين، تحت الانتظار، مرفوضين).

4. التقارير والإحصائيات


#### شاشة تقارير احترافية:


تقارير شاملة عن طلبات الاشتراك (تحت المراجعة، المقبولين، المرفوضين)، مع إمكانية الفلترة حسب الفئة الرئيسية (طبيب/طالب) والتخصص.

تقارير مالية مفصلة (الإيرادات من الاشتراكات والدورات).

تقارير حضور الجلسات والدورات.

تقارير تفاعل المستخدمين (نقاط التلعيب، الأسئلة المطروحة، التقييمات).

تقارير استطلاعات الرأي ما بعد المؤتمر.


### تحليلات متقدمة: سيتم تضمين أدوات تحليلية قوية لتتبع سلوك المستخدمين، وجمع البيانات حول الجلسات الأكثر شعبية، أوقات الذروة للتفاعل، أقسام المعرض الأكثر زيارة، والميزات الأكثر استخدامًا. هذه التحليلات ستساعد بشكل كبير في فهم نقاط القوة والضعف لتحسين المؤتمرات المستقبلية.



## سادساً: الاعتبارات التقنية والتصميمية



### تُعتبر هذه الاعتبارات حجر الزاوية لضمان أداء التطبيق، موثوقيته، وأمانه.


1. واجهة المستخدم وتجربة المستخدم (UI/UX)

تصميم حديث وجذاب: واجهة نظيفة، بسيطة، وسهلة الاستخدام، مع التركيز على الانسيابية البصرية.

واجهة مخصصة للأطباء والطلاب: إمكانية تخصيص الواجهة بناءً على نوع المستخدم (طبيب ممارس، طالب) لعرض المحتوى الأكثر صلة.

الهوية البصرية: استخدام ألوان مناسبة حسب هوية شعار المؤتمر (أحمر/أسود/أبيض). رموز تعبيرية (Icons) مصممة بأسلوب طبي (مثل سن، فرشاة أسنان، أدوات جراحية) لتعزيز الانتماء للمجال.

سهولة التنقل: قائمة تنقل واضحة ومباشرة.


### دعم الوضع الليلي (Dark Mode): دعم كامل على مستوى التطبيق، مع إمكانية التبديل التلقائي حسب إعدادات النظام أو يدويًا من إعدادات المستخدم. تصميم الألوان الداكنة متوافق مع الهوية البصرية (أسود/رمادي/أحمر طبي)، ودعم الوضع الليلي لجميع أنواع المحتوى (النصوص، الصور، الجداول، الخرائط).



### دعم متعدد اللغات: واجهة التطبيق والمحتوى باللغتين العربية والإنجليزية، مع إمكانية إضافة لغات أخرى مستقبلاً والتبديل بينها بسهولة.


عرض حالة الاشتراك للمستخدم: يجب أن تعرض واجهة المستخدم بوضوح للمشارك حالته في عملية التسجيل (مثلاً: "بانتظار المراجعة"، "الوثائق مؤكدة - بانتظار الدفع"، "أنت مشترك فعال"، "طلبك معلق - يرجى إعادة تنشيط الدفع"، "طلبك مرفوض"، "في قائمة الانتظار").

تفعيل/تعطيل خيارات الدفع والاشتراك ديناميكياً بناءً على حالة المشترك وتصنيفه والمهل الزمنية المحددة.

2. الأداء والموثوقية

أداء سريع وموثوق: تحسين سرعة التحميل والاستجابة لضمان تجربة مستخدم ممتازة.


### اختبارات الأداء: ضمان قدرة التطبيق على التعامل مع أعداد كبيرة من المستخدمين المتزامنين، خاصة أثناء الجلسات المباشرة، التفاعل، وعمليات التسجيل أو الدخول.



#### إدارة الأخطاء:


فحص توفر الإنترنت وتقديم رسائل واضحة للمستخدم في حال عدم توفر الاتصال.

معالجة التأخر في الرد أو انقطاع الرد بآليات إعادة المحاولة (Retry mechanisms) ورسائل للمستخدم في حال فشل الاتصال أو الضغط على الخادم.

تسجيل الأخطاء (Error Logging) في الخلفية للمساعدة في التصحيح وعمل تقارير لمدير النظام.

تسجيل وتوثيق جميع عمليات إدارة النظام.

النسخ الاحتياطي للبيانات والتعافي من الكوارث: وضع خطة قوية للنسخ الاحتياطي الدوري للبيانات (Data Backup) وآليات استعادة سريعة (Disaster Recovery Plan) لضمان استمرارية الخدمة وحماية البيانات في حال حدوث أي أعطال.

3. الأمان والخصوصية

تشفير البيانات: تشفير البيانات المرسلة والمستقبلة (HTTPS/SSL) لحماية معلومات المستخدمين وضمان خصوصيتهم.

4. التكامل

التكامل مع بوابات الدفع: لتمكين الاشتراكات في الدورات والمؤتمر بشكل آمن وفعال.

التكامل مع التقويم: لتصدير الجدول الشخصي للمستخدمين بسهولة.


### التكامل مع وسائل التواصل الاجتماعي: لتسهيل مشاركة المحتوى أو التحديثات من التطبيق إلى المنصات الاجتماعية.


التكامل مع أنظمة إرسال الرسائل: SMS و WhatsApp للتواصل الفعال.

5. وضع عدم الاتصال (Offline Mode)

تنزيل الجدول الزمني للمؤتمر، معلومات المتحدثين، معلومات العارضين، والخريطة المهمة مسبقًا للاستخدام بدون إنترنت.

إمكانية مسح رمز QR للحضور دون الحاجة إلى الإنترنت، مع مزامنة البيانات عند توفر الاتصال.

إمكانية تدوين الملاحظات وحفظها محليًا.