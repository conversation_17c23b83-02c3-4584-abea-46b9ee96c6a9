import { Router } from 'express';
import { body, param } from 'express-validator';
import { UserController } from '@/controllers/user.controller';
import { authenticate, requireEmailVerification } from '@/middleware/auth.middleware';
import { validateRequest } from '@/middleware/validation.middleware';
import { asyncHandler } from '@/middleware/errorHandler';
import { upload } from '@/middleware/upload.middleware';

const router = Router();
const userController = new UserController();

// Profile update validation
const updateProfileValidation = [
  body('arabicName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Arabic name must be between 2-100 characters'),
  
  body('englishName')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('English name must be between 2-100 characters'),
  
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  
  body('specialization')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Specialization must be less than 100 characters'),
  
  body('university')
    .optional()
    .isLength({ max: 100 })
    .withMessage('University must be less than 100 characters'),
  
  body('graduationYear')
    .optional()
    .isInt({ min: 1950, max: new Date().getFullYear() })
    .withMessage('Valid graduation year is required'),
  
  body('licenseNumber')
    .optional()
    .isLength({ max: 50 })
    .withMessage('License number must be less than 50 characters'),
  
  body('nationality')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Nationality must be less than 50 characters'),
  
  body('gender')
    .optional()
    .isIn(['MALE', 'FEMALE'])
    .withMessage('Valid gender is required'),
  
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Valid birth date is required'),
  
  body('bio')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Bio must be less than 500 characters'),
];

// Routes
router.get('/profile', authenticate, asyncHandler(userController.getProfile));
router.put('/profile', authenticate, updateProfileValidation, validateRequest, asyncHandler(userController.updateProfile));
router.post('/profile/image', authenticate, upload.single('image'), asyncHandler(userController.uploadProfileImage));
router.delete('/profile/image', authenticate, asyncHandler(userController.deleteProfileImage));

// Subscription management
router.get('/subscription', authenticate, asyncHandler(userController.getSubscription));
router.post('/subscription', authenticate, requireEmailVerification, 
  [
    body('subscriptionType')
      .isIn(['STUDENT', 'DENTIST', 'SPECIALIST', 'INTERNATIONAL', 'COMPANION', 'EXHIBITOR'])
      .withMessage('Valid subscription type is required'),
  ],
  validateRequest,
  asyncHandler(userController.createSubscription)
);
router.put('/subscription/:id', authenticate, 
  [
    param('id').isUUID().withMessage('Valid subscription ID is required'),
    body('subscriptionType')
      .optional()
      .isIn(['STUDENT', 'DENTIST', 'SPECIALIST', 'INTERNATIONAL', 'COMPANION', 'EXHIBITOR'])
      .withMessage('Valid subscription type is required'),
  ],
  validateRequest,
  asyncHandler(userController.updateSubscription)
);

// Document management
router.get('/documents', authenticate, asyncHandler(userController.getDocuments));
router.post('/documents', authenticate, upload.array('documents', 5), asyncHandler(userController.uploadDocuments));
router.delete('/documents/:id', authenticate, 
  [param('id').isUUID().withMessage('Valid document ID is required')],
  validateRequest,
  asyncHandler(userController.deleteDocument)
);

// Session management
router.get('/sessions', authenticate, asyncHandler(userController.getUserSessions));
router.post('/sessions/:sessionId/register', authenticate, 
  [param('sessionId').isUUID().withMessage('Valid session ID is required')],
  validateRequest,
  asyncHandler(userController.registerForSession)
);
router.delete('/sessions/:sessionId/unregister', authenticate, 
  [param('sessionId').isUUID().withMessage('Valid session ID is required')],
  validateRequest,
  asyncHandler(userController.unregisterFromSession)
);

// Course management
router.get('/courses', authenticate, asyncHandler(userController.getUserCourses));
router.post('/courses/:courseId/enroll', authenticate, 
  [param('courseId').isUUID().withMessage('Valid course ID is required')],
  validateRequest,
  asyncHandler(userController.enrollInCourse)
);
router.delete('/courses/:courseId/unenroll', authenticate, 
  [param('courseId').isUUID().withMessage('Valid course ID is required')],
  validateRequest,
  asyncHandler(userController.unenrollFromCourse)
);

// QR Code management
router.get('/qr-codes', authenticate, asyncHandler(userController.getUserQRCodes));
router.post('/qr-codes/generate', authenticate, 
  [
    body('type')
      .isIn(['USER_ENTRY', 'SESSION_ATTENDANCE', 'COURSE_ATTENDANCE', 'EXHIBITION_VISIT'])
      .withMessage('Valid QR code type is required'),
    body('sessionId')
      .optional()
      .isUUID()
      .withMessage('Valid session ID is required'),
  ],
  validateRequest,
  asyncHandler(userController.generateQRCode)
);

// Notifications
router.get('/notifications', authenticate, asyncHandler(userController.getNotifications));
router.put('/notifications/:id/read', authenticate, 
  [param('id').isUUID().withMessage('Valid notification ID is required')],
  validateRequest,
  asyncHandler(userController.markNotificationAsRead)
);
router.put('/notifications/read-all', authenticate, asyncHandler(userController.markAllNotificationsAsRead));

// Social features
router.get('/connections', authenticate, asyncHandler(userController.getConnections));
router.post('/connections/request', authenticate, 
  [
    body('receiverId')
      .isUUID()
      .withMessage('Valid receiver ID is required'),
  ],
  validateRequest,
  asyncHandler(userController.sendConnectionRequest)
);
router.put('/connections/:id/accept', authenticate, 
  [param('id').isUUID().withMessage('Valid connection ID is required')],
  validateRequest,
  asyncHandler(userController.acceptConnectionRequest)
);
router.put('/connections/:id/reject', authenticate, 
  [param('id').isUUID().withMessage('Valid connection ID is required')],
  validateRequest,
  asyncHandler(userController.rejectConnectionRequest)
);
router.delete('/connections/:id', authenticate, 
  [param('id').isUUID().withMessage('Valid connection ID is required')],
  validateRequest,
  asyncHandler(userController.removeConnection)
);

// Posts and social content
router.get('/posts', authenticate, asyncHandler(userController.getUserPosts));
router.post('/posts', authenticate, 
  [
    body('content')
      .isLength({ min: 1, max: 1000 })
      .withMessage('Post content must be between 1-1000 characters'),
  ],
  validateRequest,
  upload.array('images', 5),
  asyncHandler(userController.createPost)
);
router.put('/posts/:id', authenticate, 
  [
    param('id').isUUID().withMessage('Valid post ID is required'),
    body('content')
      .isLength({ min: 1, max: 1000 })
      .withMessage('Post content must be between 1-1000 characters'),
  ],
  validateRequest,
  asyncHandler(userController.updatePost)
);
router.delete('/posts/:id', authenticate, 
  [param('id').isUUID().withMessage('Valid post ID is required')],
  validateRequest,
  asyncHandler(userController.deletePost)
);

// Analytics and statistics
router.get('/analytics', authenticate, asyncHandler(userController.getUserAnalytics));
router.get('/certificates', authenticate, asyncHandler(userController.getUserCertificates));

// Account management
router.delete('/account', authenticate, 
  [
    body('password')
      .notEmpty()
      .withMessage('Password is required for account deletion'),
    body('confirmation')
      .equals('DELETE_MY_ACCOUNT')
      .withMessage('Please type DELETE_MY_ACCOUNT to confirm'),
  ],
  validateRequest,
  asyncHandler(userController.deleteAccount)
);

export default router;
