import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';
import { config } from './config';

// Redis Client instance
let redisClient: RedisClientType;

// Redis connection function
export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    if (!redisClient) {
      redisClient = createClient({
        url: config.redis.url,
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('❌ Redis reconnection failed after 10 attempts');
              return new Error('Redis reconnection failed');
            }
            return Math.min(retries * 50, 1000);
          },
        },
      });

      // Error handling
      redisClient.on('error', (error) => {
        logger.error('❌ Redis Client Error:', error);
      });

      redisClient.on('connect', () => {
        logger.info('🔄 Redis Client connecting...');
      });

      redisClient.on('ready', () => {
        logger.info('✅ Redis Client ready');
      });

      redisClient.on('end', () => {
        logger.info('🔚 Redis Client connection ended');
      });

      redisClient.on('reconnecting', () => {
        logger.info('🔄 Redis Client reconnecting...');
      });

      // Connect to Redis
      await redisClient.connect();
      
      // Test connection
      await redisClient.ping();
      
      logger.info('✅ Redis connected successfully');
    }

    return redisClient;
  } catch (error) {
    logger.error('❌ Redis connection failed:', error);
    throw error;
  }
};

// Get Redis instance
export const getRedis = (): RedisClientType => {
  if (!redisClient) {
    throw new Error('Redis not connected. Call connectRedis() first.');
  }
  return redisClient;
};

// Disconnect from Redis
export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient) {
      await redisClient.quit();
      logger.info('✅ Redis disconnected successfully');
    }
  } catch (error) {
    logger.error('❌ Redis disconnection failed:', error);
    throw error;
  }
};

// Redis health check
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    if (!redisClient) {
      return false;
    }

    const result = await redisClient.ping();
    return result === 'PONG';
  } catch (error) {
    logger.error('❌ Redis health check failed:', error);
    return false;
  }
};

// Cache helper functions
export class CacheService {
  private client: RedisClientType;

  constructor() {
    this.client = getRedis();
  }

  // Set cache with expiration
  async set(key: string, value: any, expirationInSeconds?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      
      if (expirationInSeconds) {
        await this.client.setEx(key, expirationInSeconds, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
    } catch (error) {
      logger.error(`❌ Cache set failed for key ${key}:`, error);
      throw error;
    }
  }

  // Get cache
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      
      if (!value) {
        return null;
      }

      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`❌ Cache get failed for key ${key}:`, error);
      return null;
    }
  }

  // Delete cache
  async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
    } catch (error) {
      logger.error(`❌ Cache delete failed for key ${key}:`, error);
      throw error;
    }
  }

  // Check if key exists
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error(`❌ Cache exists check failed for key ${key}:`, error);
      return false;
    }
  }

  // Set expiration for existing key
  async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.client.expire(key, seconds);
    } catch (error) {
      logger.error(`❌ Cache expire failed for key ${key}:`, error);
      throw error;
    }
  }

  // Get keys by pattern
  async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);
    } catch (error) {
      logger.error(`❌ Cache keys search failed for pattern ${pattern}:`, error);
      return [];
    }
  }

  // Clear all cache
  async flushAll(): Promise<void> {
    try {
      await this.client.flushAll();
      logger.info('✅ All cache cleared');
    } catch (error) {
      logger.error('❌ Cache flush all failed:', error);
      throw error;
    }
  }

  // Increment counter
  async incr(key: string): Promise<number> {
    try {
      return await this.client.incr(key);
    } catch (error) {
      logger.error(`❌ Cache increment failed for key ${key}:`, error);
      throw error;
    }
  }

  // Decrement counter
  async decr(key: string): Promise<number> {
    try {
      return await this.client.decr(key);
    } catch (error) {
      logger.error(`❌ Cache decrement failed for key ${key}:`, error);
      throw error;
    }
  }

  // Add to set
  async sadd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sAdd(key, members);
    } catch (error) {
      logger.error(`❌ Cache set add failed for key ${key}:`, error);
      throw error;
    }
  }

  // Get set members
  async smembers(key: string): Promise<string[]> {
    try {
      return await this.client.sMembers(key);
    } catch (error) {
      logger.error(`❌ Cache set members failed for key ${key}:`, error);
      return [];
    }
  }

  // Remove from set
  async srem(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sRem(key, members);
    } catch (error) {
      logger.error(`❌ Cache set remove failed for key ${key}:`, error);
      throw error;
    }
  }
}

// Create cache service instance
export const cacheService = new CacheService();

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await disconnectRedis();
});

process.on('SIGINT', async () => {
  await disconnectRedis();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
  process.exit(0);
});

export default redisClient;
