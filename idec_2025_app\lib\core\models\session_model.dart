class Session {
  final String id;
  final String titleAr;
  final String titleEn;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final String? roomId;
  final String speakerId;
  final String sessionType;
  final double? cmeHours;
  final int? maxAttendees;
  final String? qrCode;
  final String status;
  final Speaker? speaker;
  final Room? room;
  final bool isRegistered;
  final bool isAttended;

  Session({
    required this.id,
    required this.titleAr,
    required this.titleEn,
    this.description,
    required this.startTime,
    required this.endTime,
    this.roomId,
    required this.speakerId,
    required this.sessionType,
    this.cmeHours,
    this.maxAttendees,
    this.qrCode,
    required this.status,
    this.speaker,
    this.room,
    this.isRegistered = false,
    this.isAttended = false,
  });

  factory Session.fromJson(Map<String, dynamic> json) {
    return Session(
      id: json['id'],
      titleAr: json['titleAr'],
      titleEn: json['titleEn'],
      description: json['description'],
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      roomId: json['roomId'],
      speakerId: json['speakerId'],
      sessionType: json['sessionType'],
      cmeHours: json['cmeHours']?.toDouble(),
      maxAttendees: json['maxAttendees'],
      qrCode: json['qrCode'],
      status: json['status'],
      speaker: json['speaker'] != null ? Speaker.fromJson(json['speaker']) : null,
      room: json['room'] != null ? Room.fromJson(json['room']) : null,
      isRegistered: json['isRegistered'] ?? false,
      isAttended: json['isAttended'] ?? false,
    );
  }

  String get title => titleEn.isNotEmpty ? titleEn : titleAr;
  String get duration => '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')} - ${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
  
  bool get isLive {
    final now = DateTime.now();
    return now.isAfter(startTime) && now.isBefore(endTime);
  }
  
  bool get isUpcoming => DateTime.now().isBefore(startTime);
  bool get isCompleted => DateTime.now().isAfter(endTime);
}

class Speaker {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? bio;
  final String? bioAr;
  final String? bioEn;
  final String? title;
  final String? titleAr;
  final String? titleEn;
  final String? specialization;
  final String? organization;
  final String? profileImage;
  final String? email;
  final String? phone;

  Speaker({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.bio,
    this.bioAr,
    this.bioEn,
    this.title,
    this.titleAr,
    this.titleEn,
    this.specialization,
    this.organization,
    this.profileImage,
    this.email,
    this.phone,
  });

  factory Speaker.fromJson(Map<String, dynamic> json) {
    return Speaker(
      id: json['id'],
      nameAr: json['nameAr'],
      nameEn: json['nameEn'],
      bio: json['bio'],
      bioAr: json['bioAr'],
      bioEn: json['bioEn'],
      title: json['title'],
      titleAr: json['titleAr'],
      titleEn: json['titleEn'],
      specialization: json['specialization'],
      organization: json['organization'],
      profileImage: json['profileImage'],
      email: json['email'],
      phone: json['phone'],
    );
  }

  String get name => nameEn.isNotEmpty ? nameEn : nameAr;
  String get displayTitle => titleEn ?? titleAr ?? title ?? '';
  String get displayBio => bioEn ?? bioAr ?? bio ?? '';
}

class Room {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? description;
  final int capacity;
  final String? location;
  final String? floor;

  Room({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.description,
    required this.capacity,
    this.location,
    this.floor,
  });

  factory Room.fromJson(Map<String, dynamic> json) {
    return Room(
      id: json['id'],
      nameAr: json['nameAr'],
      nameEn: json['nameEn'],
      description: json['description'],
      capacity: json['capacity'],
      location: json['location'],
      floor: json['floor'],
    );
  }

  String get name => nameEn.isNotEmpty ? nameEn : nameAr;
}

class SessionAttendance {
  final String id;
  final String userId;
  final String sessionId;
  final DateTime checkInTime;
  final DateTime? checkOutTime;
  final int? attendanceDuration;
  final double? cmeHoursEarned;

  SessionAttendance({
    required this.id,
    required this.userId,
    required this.sessionId,
    required this.checkInTime,
    this.checkOutTime,
    this.attendanceDuration,
    this.cmeHoursEarned,
  });

  factory SessionAttendance.fromJson(Map<String, dynamic> json) {
    return SessionAttendance(
      id: json['id'],
      userId: json['userId'],
      sessionId: json['sessionId'],
      checkInTime: DateTime.parse(json['checkInTime']),
      checkOutTime: json['checkOutTime'] != null 
          ? DateTime.parse(json['checkOutTime']) 
          : null,
      attendanceDuration: json['attendanceDuration'],
      cmeHoursEarned: json['cmeHoursEarned']?.toDouble(),
    );
  }

  bool get isCheckedOut => checkOutTime != null;
  
  Duration get duration {
    final endTime = checkOutTime ?? DateTime.now();
    return endTime.difference(checkInTime);
  }
}
