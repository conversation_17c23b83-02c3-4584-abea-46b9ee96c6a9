const socialService = require('../services/socialService');
const logger = require('../utils/logger');

class SocialController {
  // Send connection request
  async sendConnectionRequest(req, res) {
    try {
      const { receiverId, message } = req.body;
      const requesterId = req.user.id;

      const connection = await socialService.sendConnectionRequest(requesterId, receiverId, message);

      res.status(201).json({
        success: true,
        message: 'Connection request sent successfully',
        data: connection
      });
    } catch (error) {
      logger.error('Error sending connection request:', error);
      res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to send connection request',
          code: 'CONNECTION_REQUEST_ERROR'
        }
      });
    }
  }

  // Respond to connection request
  async respondToConnectionRequest(req, res) {
    try {
      const { id: connectionId } = req.params;
      const { response } = req.body; // 'ACCEPTED' or 'REJECTED'
      const userId = req.user.id;

      const connection = await socialService.respondToConnectionRequest(connectionId, userId, response);

      res.json({
        success: true,
        message: `Connection request ${response.toLowerCase()} successfully`,
        data: connection
      });
    } catch (error) {
      logger.error('Error responding to connection request:', error);
      res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to respond to connection request',
          code: 'CONNECTION_RESPONSE_ERROR'
        }
      });
    }
  }

  // Get user's connections
  async getUserConnections(req, res) {
    try {
      const userId = req.user.id;
      const { status = 'ACCEPTED' } = req.query;

      const connections = await socialService.getUserConnections(userId, status);

      res.json({
        success: true,
        data: connections
      });
    } catch (error) {
      logger.error('Error getting user connections:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get connections',
          code: 'GET_CONNECTIONS_ERROR'
        }
      });
    }
  }

  // Get pending connection requests
  async getPendingRequests(req, res) {
    try {
      const userId = req.user.id;

      const requests = await socialService.getPendingRequests(userId);

      res.json({
        success: true,
        data: requests
      });
    } catch (error) {
      logger.error('Error getting pending requests:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get pending requests',
          code: 'GET_PENDING_REQUESTS_ERROR'
        }
      });
    }
  }

  // Search users
  async searchUsers(req, res) {
    try {
      const { q: searchQuery, qualification, specialization } = req.query;
      const currentUserId = req.user.id;

      if (!searchQuery || searchQuery.length < 2) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Search query must be at least 2 characters',
            code: 'INVALID_SEARCH_QUERY'
          }
        });
      }

      const filters = {};
      if (qualification) filters.qualification = qualification;
      if (specialization) filters.specialization = specialization;

      const users = await socialService.searchUsers(searchQuery, currentUserId, filters);

      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      logger.error('Error searching users:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to search users',
          code: 'SEARCH_USERS_ERROR'
        }
      });
    }
  }

  // Get connection suggestions
  async getConnectionSuggestions(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 10 } = req.query;

      const suggestions = await socialService.getConnectionSuggestions(userId, parseInt(limit));

      res.json({
        success: true,
        data: suggestions
      });
    } catch (error) {
      logger.error('Error getting connection suggestions:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get connection suggestions',
          code: 'CONNECTION_SUGGESTIONS_ERROR'
        }
      });
    }
  }

  // Remove connection
  async removeConnection(req, res) {
    try {
      const { id: connectionId } = req.params;
      const userId = req.user.id;

      await socialService.removeConnection(connectionId, userId);

      res.json({
        success: true,
        message: 'Connection removed successfully'
      });
    } catch (error) {
      logger.error('Error removing connection:', error);
      res.status(400).json({
        success: false,
        error: {
          message: error.message || 'Failed to remove connection',
          code: 'REMOVE_CONNECTION_ERROR'
        }
      });
    }
  }

  // Get user's social stats
  async getUserSocialStats(req, res) {
    try {
      const userId = req.user.id;

      const stats = await socialService.getUserSocialStats(userId);

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error getting user social stats:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get social stats',
          code: 'SOCIAL_STATS_ERROR'
        }
      });
    }
  }

  // Get user profile for networking
  async getUserProfile(req, res) {
    try {
      const { id: userId } = req.params;
      const currentUserId = req.user.id;

      // Get user profile
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          arabicName: true,
          englishName: true,
          profileImage: true,
          qualification: true,
          specialization: true,
          university: true,
          bio: true,
          interests: true,
          createdAt: true
        }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'User not found',
            code: 'USER_NOT_FOUND'
          }
        });
      }

      // Check connection status
      let connectionStatus = 'NONE';
      if (userId !== currentUserId) {
        const connection = await prisma.connection.findFirst({
          where: {
            OR: [
              { requesterId: currentUserId, receiverId: userId },
              { requesterId: userId, receiverId: currentUserId }
            ]
          }
        });

        if (connection) {
          connectionStatus = connection.status;
        }
      }

      res.json({
        success: true,
        data: {
          ...user,
          connectionStatus
        }
      });
    } catch (error) {
      logger.error('Error getting user profile:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get user profile',
          code: 'GET_USER_PROFILE_ERROR'
        }
      });
    }
  }

  // Get networking activity feed
  async getNetworkingFeed(req, res) {
    try {
      const userId = req.user.id;
      const { limit = 20 } = req.query;

      // Get recent connections and activities
      const recentConnections = await prisma.connection.findMany({
        where: {
          OR: [
            { requesterId: userId, status: 'ACCEPTED' },
            { receiverId: userId, status: 'ACCEPTED' }
          ]
        },
        include: {
          requester: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true
            }
          },
          receiver: {
            select: {
              id: true,
              arabicName: true,
              englishName: true,
              profileImage: true
            }
          }
        },
        orderBy: { updatedAt: 'desc' },
        take: parseInt(limit)
      });

      const feed = recentConnections.map(connection => {
        const otherUser = connection.requesterId === userId 
          ? connection.receiver 
          : connection.requester;
        
        return {
          type: 'NEW_CONNECTION',
          user: otherUser,
          timestamp: connection.updatedAt,
          message: `Connected with ${otherUser.englishName || otherUser.arabicName}`
        };
      });

      res.json({
        success: true,
        data: feed
      });
    } catch (error) {
      logger.error('Error getting networking feed:', error);
      res.status(500).json({
        success: false,
        error: {
          message: 'Failed to get networking feed',
          code: 'NETWORKING_FEED_ERROR'
        }
      });
    }
  }
}

module.exports = new SocialController();
