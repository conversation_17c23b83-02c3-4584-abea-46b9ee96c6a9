import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { logger, logError } from '@/utils/logger';
import { ApiError } from '@/utils/ApiError';
import { config } from '@/config/config';

// Error response interface
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  path: string;
  method: string;
}

// Global error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal Server Error';
  let code: string | undefined;
  let details: any;

  // Log the error
  logError('Error occurred', error, {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
  });

  // Handle different types of errors
  if (error instanceof ApiError) {
    // Custom API errors
    statusCode = error.statusCode;
    message = error.message;
    code = error.code;
    details = error.details;
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Prisma known errors
    ({ statusCode, message, code, details } = handlePrismaError(error));
  } else if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    // Prisma unknown errors
    statusCode = 500;
    message = 'Database error occurred';
    code = 'DATABASE_ERROR';
  } else if (error instanceof Prisma.PrismaClientRustPanicError) {
    // Prisma panic errors
    statusCode = 500;
    message = 'Database connection error';
    code = 'DATABASE_CONNECTION_ERROR';
  } else if (error instanceof Prisma.PrismaClientInitializationError) {
    // Prisma initialization errors
    statusCode = 500;
    message = 'Database initialization error';
    code = 'DATABASE_INIT_ERROR';
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    // Prisma validation errors
    statusCode = 400;
    message = 'Invalid data provided';
    code = 'VALIDATION_ERROR';
    details = error.message;
  } else if (error instanceof JsonWebTokenError) {
    // JWT errors
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  } else if (error instanceof TokenExpiredError) {
    // JWT expiration errors
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  } else if (error.name === 'ValidationError') {
    // Joi validation errors
    statusCode = 400;
    message = 'Validation failed';
    code = 'VALIDATION_ERROR';
    details = error.message;
  } else if (error.name === 'MulterError') {
    // File upload errors
    ({ statusCode, message, code } = handleMulterError(error as any));
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    // JSON parsing errors
    statusCode = 400;
    message = 'Invalid JSON format';
    code = 'INVALID_JSON';
  }

  // Create error response
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message,
      code,
      details,
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method,
  };

  // Include stack trace in development
  if (config.nodeEnv === 'development' && config.development.debug) {
    errorResponse.error.stack = error.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

// Handle Prisma errors
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError) => {
  let statusCode = 500;
  let message = 'Database error';
  let code = 'DATABASE_ERROR';
  let details: any;

  switch (error.code) {
    case 'P2000':
      statusCode = 400;
      message = 'The provided value is too long';
      code = 'VALUE_TOO_LONG';
      break;

    case 'P2001':
      statusCode = 404;
      message = 'Record not found';
      code = 'RECORD_NOT_FOUND';
      break;

    case 'P2002':
      statusCode = 409;
      message = 'Unique constraint violation';
      code = 'DUPLICATE_ENTRY';
      details = {
        field: error.meta?.target,
      };
      break;

    case 'P2003':
      statusCode = 400;
      message = 'Foreign key constraint violation';
      code = 'FOREIGN_KEY_VIOLATION';
      break;

    case 'P2004':
      statusCode = 400;
      message = 'Constraint violation';
      code = 'CONSTRAINT_VIOLATION';
      break;

    case 'P2005':
      statusCode = 400;
      message = 'Invalid value for field';
      code = 'INVALID_FIELD_VALUE';
      break;

    case 'P2006':
      statusCode = 400;
      message = 'Invalid value provided';
      code = 'INVALID_VALUE';
      break;

    case 'P2007':
      statusCode = 400;
      message = 'Data validation error';
      code = 'DATA_VALIDATION_ERROR';
      break;

    case 'P2008':
      statusCode = 400;
      message = 'Failed to parse query';
      code = 'QUERY_PARSE_ERROR';
      break;

    case 'P2009':
      statusCode = 400;
      message = 'Failed to validate query';
      code = 'QUERY_VALIDATION_ERROR';
      break;

    case 'P2010':
      statusCode = 500;
      message = 'Raw query failed';
      code = 'RAW_QUERY_ERROR';
      break;

    case 'P2011':
      statusCode = 400;
      message = 'Null constraint violation';
      code = 'NULL_CONSTRAINT_VIOLATION';
      details = {
        field: error.meta?.target,
      };
      break;

    case 'P2012':
      statusCode = 400;
      message = 'Missing required value';
      code = 'MISSING_REQUIRED_VALUE';
      break;

    case 'P2013':
      statusCode = 400;
      message = 'Missing required argument';
      code = 'MISSING_REQUIRED_ARGUMENT';
      break;

    case 'P2014':
      statusCode = 400;
      message = 'Relation violation';
      code = 'RELATION_VIOLATION';
      break;

    case 'P2015':
      statusCode = 404;
      message = 'Related record not found';
      code = 'RELATED_RECORD_NOT_FOUND';
      break;

    case 'P2016':
      statusCode = 400;
      message = 'Query interpretation error';
      code = 'QUERY_INTERPRETATION_ERROR';
      break;

    case 'P2017':
      statusCode = 400;
      message = 'Records not connected';
      code = 'RECORDS_NOT_CONNECTED';
      break;

    case 'P2018':
      statusCode = 404;
      message = 'Required connected records not found';
      code = 'CONNECTED_RECORDS_NOT_FOUND';
      break;

    case 'P2019':
      statusCode = 400;
      message = 'Input error';
      code = 'INPUT_ERROR';
      break;

    case 'P2020':
      statusCode = 400;
      message = 'Value out of range';
      code = 'VALUE_OUT_OF_RANGE';
      break;

    case 'P2021':
      statusCode = 404;
      message = 'Table does not exist';
      code = 'TABLE_NOT_EXISTS';
      break;

    case 'P2022':
      statusCode = 404;
      message = 'Column does not exist';
      code = 'COLUMN_NOT_EXISTS';
      break;

    case 'P2025':
      statusCode = 404;
      message = 'Record not found';
      code = 'RECORD_NOT_FOUND';
      break;

    default:
      statusCode = 500;
      message = 'Unknown database error';
      code = 'UNKNOWN_DATABASE_ERROR';
      details = {
        prismaCode: error.code,
        meta: error.meta,
      };
  }

  return { statusCode, message, code, details };
};

// Handle Multer (file upload) errors
const handleMulterError = (error: any) => {
  let statusCode = 400;
  let message = 'File upload error';
  let code = 'FILE_UPLOAD_ERROR';

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = 'File too large';
      code = 'FILE_TOO_LARGE';
      break;

    case 'LIMIT_FILE_COUNT':
      message = 'Too many files';
      code = 'TOO_MANY_FILES';
      break;

    case 'LIMIT_FIELD_KEY':
      message = 'Field name too long';
      code = 'FIELD_NAME_TOO_LONG';
      break;

    case 'LIMIT_FIELD_VALUE':
      message = 'Field value too long';
      code = 'FIELD_VALUE_TOO_LONG';
      break;

    case 'LIMIT_FIELD_COUNT':
      message = 'Too many fields';
      code = 'TOO_MANY_FIELDS';
      break;

    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Unexpected file field';
      code = 'UNEXPECTED_FILE_FIELD';
      break;

    case 'MISSING_FIELD_NAME':
      message = 'Missing field name';
      code = 'MISSING_FIELD_NAME';
      break;

    default:
      message = 'Unknown file upload error';
      code = 'UNKNOWN_FILE_UPLOAD_ERROR';
  }

  return { statusCode, message, code };
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 Not Found handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new ApiError(404, `Route ${req.originalUrl} not found`);
  next(error);
};

export default errorHandler;
