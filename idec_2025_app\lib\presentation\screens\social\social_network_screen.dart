import 'package:flutter/material.dart';
import '../../../core/constants/app_constants.dart';

class SocialNetworkScreen extends StatefulWidget {
  const SocialNetworkScreen({super.key});

  @override
  State<SocialNetworkScreen> createState() => _SocialNetworkScreenState();
}

class _SocialNetworkScreenState extends State<SocialNetworkScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final TextEditingController _postController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _postController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 280,
              floating: false,
              pinned: true,
              backgroundColor: const Color(AppColors.primaryRed),
              flexibleSpace: FlexibleSpaceBar(
                title: const Text(
                  'الشبكة الاجتماعية',
                  style: TextStyle(
                    color: Color(AppColors.primaryWhite),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(AppColors.primaryRed),
                        Color(AppColors.darkRed),
                        Color(AppColors.primaryBlack),
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Animated Background
                      Positioned.fill(
                        child: AnimatedBuilder(
                          animation: _slideAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                50 * _slideAnimation.value.dx,
                                30 * _slideAnimation.value.dy,
                              ),
                              child: Opacity(
                                opacity: 0.1,
                                child: Container(
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage('assets/images/social_bg.png'),
                                      repeat: ImageRepeat.repeat,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Content
                      Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 60),
                                
                                // Social Icon
                                Container(
                                  padding: const EdgeInsets.all(AppSizes.paddingL),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                        const Color(AppColors.primaryWhite).withValues(alpha: 0.1),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(AppSizes.radiusXL),
                                    border: Border.all(
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                      width: 2,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.people,
                                    size: 50,
                                    color: Color(AppColors.primaryWhite),
                                  ),
                                ),
                                
                                const SizedBox(height: AppSizes.paddingM),
                                
                                // Stats
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    _buildStatItem('1,250+', 'مشارك'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('350+', 'منشور'),
                                    Container(
                                      width: 1,
                                      height: 30,
                                      color: const Color(AppColors.primaryWhite).withValues(alpha: 0.3),
                                    ),
                                    _buildStatItem('50+', 'مجموعة'),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              bottom: TabBar(
                controller: _tabController,
                indicatorColor: const Color(AppColors.primaryWhite),
                indicatorWeight: 3,
                labelColor: const Color(AppColors.primaryWhite),
                unselectedLabelColor: const Color(AppColors.primaryWhite).withValues(alpha: 0.7),
                labelStyle: const TextStyle(fontWeight: FontWeight.bold),
                isScrollable: true,
                tabs: const [
                  Tab(text: 'الرئيسية'),
                  Tab(text: 'المجموعات'),
                  Tab(text: 'الأشخاص'),
                  Tab(text: 'الفعاليات'),
                ],
              ),
            ),
          ];
        },
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildFeedTab(),
              _buildGroupsTab(),
              _buildPeopleTab(),
              _buildEventsTab(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreatePostDialog,
        backgroundColor: const Color(AppColors.primaryRed),
        foregroundColor: const Color(AppColors.primaryWhite),
        icon: const Icon(Icons.add),
        label: const Text('منشور جديد'),
      ),
    );
  }

  Widget _buildStatItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Color(AppColors.primaryWhite),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFeedTab() {
    final posts = _getPosts();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        final post = posts[index];
        return _buildPostCard(post);
      },
    );
  }

  Widget _buildGroupsTab() {
    final groups = _getGroups();
    
    return GridView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSizes.paddingM,
        mainAxisSpacing: AppSizes.paddingM,
        childAspectRatio: 0.8,
      ),
      itemCount: groups.length,
      itemBuilder: (context, index) {
        final group = groups[index];
        return _buildGroupCard(group);
      },
    );
  }

  Widget _buildPeopleTab() {
    final people = _getPeople();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: people.length,
      itemBuilder: (context, index) {
        final person = people[index];
        return _buildPersonCard(person);
      },
    );
  }

  Widget _buildEventsTab() {
    final events = _getEvents();
    
    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event);
      },
    );
  }

  Widget _buildPostCard(Map<String, dynamic> post) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Post Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: const Color(AppColors.primaryRed).withValues(alpha: 0.1),
                    child: Text(
                      post['author'][0],
                      style: const TextStyle(
                        color: Color(AppColors.primaryRed),
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppSizes.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post['author'],
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          post['title'],
                          style: const TextStyle(
                            color: Color(AppColors.grey),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    post['time'],
                    style: const TextStyle(
                      color: Color(AppColors.grey),
                      fontSize: 12,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _showPostOptions(post),
                    icon: const Icon(
                      Icons.more_vert,
                      color: Color(AppColors.grey),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Post Content
              Text(
                post['content'],
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                ),
              ),
              
              if (post['hasImage'] == true) ...[
                const SizedBox(height: AppSizes.paddingM),
                Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(AppColors.info).withValues(alpha: 0.3),
                        const Color(AppColors.info).withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(AppSizes.radiusM),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image,
                      size: 60,
                      color: Color(AppColors.info),
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: AppSizes.paddingM),
              
              // Post Actions
              Row(
                children: [
                  _buildPostAction(
                    Icons.thumb_up_outlined,
                    '${post['likes']}',
                    () => _likePost(post),
                  ),
                  const SizedBox(width: AppSizes.paddingL),
                  _buildPostAction(
                    Icons.comment_outlined,
                    '${post['comments']}',
                    () => _showComments(post),
                  ),
                  const SizedBox(width: AppSizes.paddingL),
                  _buildPostAction(
                    Icons.share_outlined,
                    'مشاركة',
                    () => _sharePost(post),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => _savePost(post),
                    icon: Icon(
                      post['isSaved'] ? Icons.bookmark : Icons.bookmark_border,
                      color: post['isSaved'] 
                          ? const Color(AppColors.primaryRed)
                          : const Color(AppColors.grey),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostAction(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppSizes.radiusS),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSizes.paddingS,
          vertical: AppSizes.paddingXS,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: AppSizes.iconS,
              color: const Color(AppColors.grey),
            ),
            const SizedBox(width: AppSizes.paddingXS),
            Text(
              label,
              style: const TextStyle(
                color: Color(AppColors.grey),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupCard(Map<String, dynamic> group) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
      ),
      child: InkWell(
        onTap: () => _joinGroup(group),
        borderRadius: BorderRadius.circular(AppSizes.radiusL),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
          ),
          child: Column(
            children: [
              // Group Cover
              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(group['color']),
                        Color(group['color']).withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppSizes.radiusL),
                      topRight: Radius.circular(AppSizes.radiusL),
                    ),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          group['icon'],
                          size: 40,
                          color: const Color(AppColors.primaryWhite),
                        ),
                      ),
                      Positioned(
                        top: AppSizes.paddingS,
                        right: AppSizes.paddingS,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingS,
                            vertical: AppSizes.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(AppSizes.radiusS),
                          ),
                          child: Text(
                            '${group['members']}',
                            style: TextStyle(
                              color: Color(group['color']),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Group Info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingS),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        group['name'],
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: Color(AppColors.primaryBlack),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: AppSizes.paddingXS),
                      
                      Text(
                        group['description'],
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(AppColors.grey),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const Spacer(),
                      
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => _joinGroup(group),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(group['color']),
                            foregroundColor: const Color(AppColors.primaryWhite),
                            padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingXS),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusS),
                            ),
                            elevation: 2,
                          ),
                          child: Text(
                            group['isJoined'] ? 'عضو' : 'انضمام',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Add remaining methods
  void _showCreatePostDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Color(AppColors.primaryWhite),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppSizes.radiusL),
            topRight: Radius.circular(AppSizes.radiusL),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: AppSizes.paddingM),
              decoration: BoxDecoration(
                color: const Color(AppColors.grey),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Row(
                children: [
                  const Text(
                    'منشور جديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _createPost();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(AppColors.primaryRed),
                      foregroundColor: const Color(AppColors.primaryWhite),
                    ),
                    child: const Text('نشر'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingL),
                child: TextField(
                  controller: _postController,
                  maxLines: null,
                  expands: true,
                  decoration: const InputDecoration(
                    hintText: 'ما الذي تريد مشاركته؟',
                    border: InputBorder.none,
                  ),
                  textAlignVertical: TextAlignVertical.top,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingL),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.image, color: Color(AppColors.info)),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.videocam, color: Color(AppColors.success)),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.poll, color: Color(AppColors.warning)),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(Icons.location_on, color: Color(AppColors.error)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _createPost() {
    if (_postController.text.trim().isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نشر المنشور بنجاح'),
          backgroundColor: Color(AppColors.success),
        ),
      );
      _postController.clear();
    }
  }

  void _likePost(Map<String, dynamic> post) {
    setState(() {
      post['likes'] = (post['likes'] ?? 0) + 1;
    });
  }

  void _showComments(Map<String, dynamic> post) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تعليقات: ${post['comments']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _sharePost(Map<String, dynamic> post) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم مشاركة المنشور'),
        backgroundColor: Color(AppColors.info),
      ),
    );
  }

  void _savePost(Map<String, dynamic> post) {
    setState(() {
      post['isSaved'] = !(post['isSaved'] ?? false);
    });
  }

  void _showPostOptions(Map<String, dynamic> post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('تعديل'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('حذف'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _joinGroup(Map<String, dynamic> group) {
    setState(() {
      group['isJoined'] = !(group['isJoined'] ?? false);
    });
  }

  Widget _buildPersonCard(Map<String, dynamic> person) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Row(
            children: [
              // Profile Picture
              CircleAvatar(
                radius: 30,
                backgroundColor: const Color(AppColors.info).withValues(alpha: 0.1),
                child: Text(
                  person['name'][0],
                  style: const TextStyle(
                    color: Color(AppColors.info),
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ),

              const SizedBox(width: AppSizes.paddingM),

              // Person Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      person['name'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      person['title'],
                      style: const TextStyle(
                        color: Color(AppColors.grey),
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: AppSizes.paddingXS),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: AppSizes.iconS,
                          color: const Color(AppColors.grey),
                        ),
                        const SizedBox(width: AppSizes.paddingXS),
                        Text(
                          person['location'],
                          style: const TextStyle(
                            color: Color(AppColors.grey),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Action Buttons
              Column(
                children: [
                  ElevatedButton(
                    onPressed: () => _connectWithPerson(person),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: person['isConnected']
                          ? const Color(AppColors.success)
                          : const Color(AppColors.primaryRed),
                      foregroundColor: const Color(AppColors.primaryWhite),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      minimumSize: const Size(80, 30),
                    ),
                    child: Text(
                      person['isConnected'] ? 'متصل' : 'تواصل',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                  const SizedBox(height: AppSizes.paddingXS),
                  OutlinedButton(
                    onPressed: () => _viewProfile(person),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(AppColors.info),
                      side: const BorderSide(color: Color(AppColors.info)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      minimumSize: const Size(80, 30),
                    ),
                    child: const Text('عرض', style: TextStyle(fontSize: 12)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingM),
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.radiusL),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppSizes.radiusL),
            gradient: LinearGradient(
              colors: [
                const Color(AppColors.primaryWhite),
                const Color(AppColors.offWhite),
              ],
            ),
          ),
          child: Column(
            children: [
              // Event Header
              Container(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Color(event['color']),
                      Color(event['color']).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppSizes.radiusL),
                    topRight: Radius.circular(AppSizes.radiusL),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppSizes.paddingM),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryWhite).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(AppSizes.radiusM),
                      ),
                      child: Icon(
                        event['icon'],
                        color: const Color(AppColors.primaryWhite),
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: AppSizes.paddingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event['title'],
                            style: const TextStyle(
                              color: Color(AppColors.primaryWhite),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${event['date']} • ${event['time']}',
                            style: TextStyle(
                              color: const Color(AppColors.primaryWhite).withValues(alpha: 0.9),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.paddingS,
                        vertical: AppSizes.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(AppColors.primaryWhite),
                        borderRadius: BorderRadius.circular(AppSizes.radiusS),
                      ),
                      child: Text(
                        '${event['attendees']} مشارك',
                        style: TextStyle(
                          color: Color(event['color']),
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Event Content
              Padding(
                padding: const EdgeInsets.all(AppSizes.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      event['description'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        height: 1.4,
                      ),
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: AppSizes.iconS,
                          color: const Color(AppColors.grey),
                        ),
                        const SizedBox(width: AppSizes.paddingXS),
                        Text(
                          event['location'],
                          style: const TextStyle(
                            color: Color(AppColors.grey),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppSizes.paddingM),

                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => _joinEvent(event),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: event['isJoined']
                                  ? const Color(AppColors.success)
                                  : Color(event['color']),
                              foregroundColor: const Color(AppColors.primaryWhite),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(AppSizes.radiusM),
                              ),
                            ),
                            child: Text(event['isJoined'] ? 'مشارك' : 'انضمام'),
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingS),
                        OutlinedButton(
                          onPressed: () => _shareEvent(event),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Color(event['color']),
                            side: BorderSide(color: Color(event['color'])),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppSizes.radiusM),
                            ),
                          ),
                          child: const Text('مشاركة'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _connectWithPerson(Map<String, dynamic> person) {
    setState(() {
      person['isConnected'] = !(person['isConnected'] ?? false);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          person['isConnected']
              ? 'تم التواصل مع ${person['name']}'
              : 'تم إلغاء التواصل مع ${person['name']}',
        ),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _viewProfile(Map<String, dynamic> person) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض ملف ${person['name']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  void _joinEvent(Map<String, dynamic> event) {
    setState(() {
      event['isJoined'] = !(event['isJoined'] ?? false);
      if (event['isJoined']) {
        event['attendees'] = (event['attendees'] ?? 0) + 1;
      } else {
        event['attendees'] = (event['attendees'] ?? 1) - 1;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          event['isJoined']
              ? 'تم التسجيل في الفعالية'
              : 'تم إلغاء التسجيل في الفعالية',
        ),
        backgroundColor: const Color(AppColors.success),
      ),
    );
  }

  void _shareEvent(Map<String, dynamic> event) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة فعالية: ${event['title']}'),
        backgroundColor: const Color(AppColors.info),
      ),
    );
  }

  List<Map<String, dynamic>> _getPosts() {
    return [
      {
        'author': 'د. أحمد محمد علي',
        'title': 'استشاري جراحة الفم والوجه والفكين',
        'content': 'شاركت اليوم في جلسة رائعة حول أحدث تقنيات زراعة الأسنان.',
        'time': 'منذ ساعتين',
        'likes': 24,
        'comments': 8,
        'hasImage': true,
        'isSaved': false,
      },
    ];
  }

  List<Map<String, dynamic>> _getGroups() {
    return [
      {
        'name': 'جراحة الفم والوجه والفكين',
        'description': 'مجموعة للمتخصصين في جراحة الفم',
        'members': 245,
        'icon': Icons.medical_services,
        'color': AppColors.primaryRed,
        'isJoined': false,
      },
    ];
  }

  List<Map<String, dynamic>> _getPeople() {
    return [
      {
        'name': 'د. سارة أحمد محمد',
        'title': 'خبيرة إدارة الأعمال الطبية',
        'location': 'الرياض، السعودية',
        'isConnected': false,
      },
    ];
  }

  List<Map<String, dynamic>> _getEvents() {
    return [
      {
        'title': 'ورشة التقويم الشفاف',
        'description': 'ورشة عملية حول أحدث تقنيات التقويم الشفاف',
        'date': '16 مارس',
        'time': '14:00',
        'location': 'قاعة المحاضرات A',
        'attendees': 45,
        'icon': Icons.straighten,
        'color': AppColors.info,
        'isJoined': false,
      },
    ];
  }
}
