# IDEC 2025 Backend API

Backend API for the International Dental Education Conference (IDEC) 2025 mobile application.

## 🏗️ Architecture

- **Framework**: Node.js + Express.js + TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis
- **Authentication**: JWT with refresh tokens
- **File Storage**: Local storage with AWS S3 support
- **Real-time**: Socket.IO
- **Email**: <PERSON>demailer
- **SMS**: Twilio
- **Payments**: Stripe + PayPal
- **Documentation**: Swagger/OpenAPI

## 📋 Features

### 🔐 Authentication & Authorization
- User registration and login
- Email and phone verification
- Password reset functionality
- JWT-based authentication
- Role-based access control
- Social login support (Google, Facebook, Apple)

### 👤 User Management
- Profile management
- Document upload and verification
- Subscription management
- QR code generation
- Analytics and statistics

### 📅 Conference Management
- Session management
- Speaker profiles
- Room and schedule management
- Attendance tracking
- Live session features

### 🎓 Course Management
- Course enrollment
- Certificate generation
- Progress tracking
- Payment integration

### 💳 Payment Processing
- Multiple payment gateways
- Subscription billing
- Invoice generation
- Refund processing

### 📱 Mobile Features
- Push notifications
- QR code scanning
- Offline support
- Real-time updates

### 🏪 Exhibition Management
- Exhibitor profiles
- Booth management
- Product catalogs
- Special offers

### 📚 Content Management
- News and announcements
- Digital library
- File management
- Multi-language support

### 🤝 Social Features
- User connections
- Posts and comments
- Groups and communities
- Messaging system

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 14+
- Redis 6+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd idec_2025_backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database setup**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Run migrations
   npm run db:migrate
   
   # Seed database (optional)
   npm run db:seed
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

The API will be available at `http://localhost:3000`

## 📁 Project Structure

```
src/
├── config/          # Configuration files
│   ├── config.ts    # Main configuration
│   ├── database.ts  # Database connection
│   └── redis.ts     # Redis connection
├── controllers/     # Route controllers
├── middleware/      # Express middleware
├── routes/          # API routes
├── services/        # Business logic services
├── utils/           # Utility functions
├── types/           # TypeScript type definitions
└── server.ts        # Main server file

prisma/
├── schema.prisma    # Database schema
├── migrations/      # Database migrations
└── seed.ts          # Database seeding

uploads/             # File uploads directory
logs/                # Application logs
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/idec_2025_db"
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your_jwt_secret"
JWT_REFRESH_SECRET="your_refresh_secret"

# Email (SMTP)
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="your_app_password"

# SMS (Twilio)
TWILIO_ACCOUNT_SID="your_twilio_sid"
TWILIO_AUTH_TOKEN="your_twilio_token"

# Payments
STRIPE_SECRET_KEY="sk_test_..."
PAYPAL_CLIENT_ID="your_paypal_client_id"
```

## 📚 API Documentation

### Base URL
```
http://localhost:3000/api/v1
```

### Authentication
All protected endpoints require a Bearer token:
```
Authorization: Bearer <your_jwt_token>
```

### Main Endpoints

#### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh-token` - Refresh access token
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password
- `POST /auth/verify-email` - Verify email address

#### Users
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `POST /users/profile/image` - Upload profile image
- `GET /users/subscription` - Get user subscription
- `POST /users/subscription` - Create subscription

#### Sessions
- `GET /sessions` - List all sessions
- `GET /sessions/:id` - Get session details
- `POST /sessions/:id/register` - Register for session
- `DELETE /sessions/:id/unregister` - Unregister from session

#### Courses
- `GET /courses` - List all courses
- `GET /courses/:id` - Get course details
- `POST /courses/:id/enroll` - Enroll in course
- `DELETE /courses/:id/unenroll` - Unenroll from course

#### Payments
- `POST /payments/create` - Create payment
- `POST /payments/confirm` - Confirm payment
- `GET /payments/history` - Payment history

### Response Format

All API responses follow this format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  },
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

Error responses:
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {}
  },
  "timestamp": "2025-01-01T00:00:00.000Z",
  "path": "/api/v1/endpoint",
  "method": "POST"
}
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📦 Deployment

### Production Build

```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment

```bash
# Build Docker image
docker build -t idec-2025-backend .

# Run with Docker Compose
docker-compose up -d
```

### Environment Setup

1. **Database Migration**
   ```bash
   npm run db:migrate
   ```

2. **Environment Variables**
   - Set all required environment variables
   - Use strong secrets for JWT and encryption
   - Configure proper CORS origins

3. **SSL/TLS**
   - Use HTTPS in production
   - Configure proper SSL certificates

## 🔒 Security

- JWT authentication with refresh tokens
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation and sanitization
- SQL injection prevention (Prisma)
- File upload restrictions

## 📊 Monitoring

- Winston logging
- Health check endpoint: `/health`
- Performance monitoring
- Error tracking
- Database query logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: `/api/docs`
- Health Check: `/health`
